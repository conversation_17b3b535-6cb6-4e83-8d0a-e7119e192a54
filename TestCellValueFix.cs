using System;
using Excel = Microsoft.Office.Interop.Excel;

namespace ExcelToWord
{
    /// <summary>
    /// 测试单元格值获取修复的类
    /// 用于验证使用 Value2 而不是 Text 属性来获取单元格实际值
    /// </summary>
    public class TestCellValueFix
    {
        /// <summary>
        /// 测试方法：比较使用 Text 和 Value2 属性的差异
        /// </summary>
        /// <param name="cell">Excel单元格</param>
        /// <returns>测试结果描述</returns>
        public static string TestCellValueRetrieval(Excel.Range cell)
        {
            try
            {
                // 使用 Text 属性（可能显示 ####）
                string textValue = cell.Text?.ToString() ?? "";
                
                // 使用 Value2 属性（获取实际值）
                object value2Obj = cell.Value2;
                string value2String = (value2Obj != null) ? value2Obj.ToString() : "";
                
                // 比较结果
                if (textValue == "####" && !string.IsNullOrEmpty(value2String))
                {
                    return $"修复成功：Text显示'{textValue}'，实际值为'{value2String}'";
                }
                else if (textValue == value2String)
                {
                    return $"正常显示：'{textValue}'";
                }
                else
                {
                    return $"差异：Text='{textValue}', Value2='{value2String}'";
                }
            }
            catch (Exception ex)
            {
                return $"测试出错：{ex.Message}";
            }
        }
        
        /// <summary>
        /// 安全获取单元格值的方法（推荐使用）
        /// </summary>
        /// <param name="cell">Excel单元格</param>
        /// <returns>单元格的实际值</returns>
        public static string GetCellValueSafely(Excel.Range cell)
        {
            try
            {
                if (cell == null) return "";

                // 使用 Value2 获取实际值，避免列太窄时显示 ####
                object cellValueObj = cell.Value2;
                return (cellValueObj != null) ? cellValueObj.ToString().Trim() : "";
            }
            catch (Exception)
            {
                return "";
            }
        }

        /// <summary>
        /// 格式化单元格值，对数值类型保留两位小数（与Ribbon1.cs中的方法相同）
        /// </summary>
        /// <param name="cellValue">单元格的原始值</param>
        /// <returns>格式化后的字符串</returns>
        public static string FormatCellValue(object cellValue)
        {
            if (cellValue == null)
                return "";

            // 如果是数值类型，格式化为两位小数
            if (cellValue is double || cellValue is float || cellValue is decimal)
            {
                double numValue = Convert.ToDouble(cellValue);
                return numValue.ToString("F2"); // F2 表示保留两位小数
            }

            // 如果是整数类型，也格式化为两位小数
            if (cellValue is int || cellValue is long || cellValue is short)
            {
                double numValue = Convert.ToDouble(cellValue);
                return numValue.ToString("F2");
            }

            // 尝试解析字符串为数值
            string strValue = cellValue.ToString().Trim();
            if (double.TryParse(strValue, out double parsedValue))
            {
                return parsedValue.ToString("F2");
            }

            // 如果不是数值，直接返回字符串
            return strValue;
        }
    }
}
