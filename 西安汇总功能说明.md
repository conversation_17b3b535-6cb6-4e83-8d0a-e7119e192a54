# 西安汇总功能说明

## 🎯 功能概述

"西安汇总"是一个新增的Excel数据汇总功能，可以将多个单体表的数据按照指定的关键词和位置汇总到一个模板文件中。

## 🔧 功能特点

### 1. 灵活的关键词匹配
- 支持设置多个关键词进行搜索
- 自动选择最下面的匹配项（如果有多个匹配）
- 支持部分匹配（包含关键词即可）

### 2. 精确的位置控制
- 可以指定数据插入的具体行号和列号
- 支持多个索引配置，每个配置独立处理
- 自动为每个文件分配不同的行（避免覆盖）

### 3. 批量文件处理
- 支持同时处理多个单体表文件
- 按文件顺序依次插入数据
- 自动生成带时间戳的结果文件

## 📋 使用步骤

### 步骤1：启动功能
1. 在Excel Ribbon中点击"西安汇总"按钮
2. 系统会打开配置窗体

### 步骤2：选择模板文件
1. 点击"选择模板"按钮
2. 选择要作为汇总目标的Excel模板文件
3. 模板文件路径会显示在文本框中

### 步骤3：选择单体表文件
1. 点击"选择单体表"按钮
2. 可以多选需要处理的单体表文件
3. 选中的文件名会显示在列表中

### 步骤4：配置索引规则
1. 点击"添加索引"按钮添加新的索引配置
2. 在表格中填写以下信息：
   - **附表名称**：从下拉列表中选择要插入数据的附表（如：附表1）
   - **关键词**：用逗号分隔的搜索关键词（如：总投资,投资额）
   - **插入行号**：数据要插入到模板的哪一行（如：3）
   - **插入列号**：数据要插入到模板的哪一列（如：2）
3. 可以添加多个索引配置，每个配置可以指定不同的附表
4. 选中行后点击"删除索引"可以删除配置

### 步骤5：执行汇总
1. 确认所有配置正确后，点击"确定"
2. 系统会自动处理所有文件
3. 完成后会显示处理结果和保存路径

## 🔍 工作原理

### 1. 关键词搜索
```
对每个单体表文件：
├─ 遍历所有单元格
├─ 查找包含关键词的单元格
├─ 如果找到多个，选择行号最大的（最下面的）
└─ 记录关键词位置
```

### 2. 数据提取
```
对每个找到的关键词：
├─ 定位到关键词所在单元格
├─ 获取该单元格下一行的数据
├─ 使用GetCellDisplayValue确保格式正确
└─ 准备插入到模板
```

### 3. 数据插入
```
对每个文件：
├─ 计算实际插入行 = 配置行号 + 文件索引
├─ 将数据插入到指定位置
├─ 自动设置数值格式（千分号+两位小数）
└─ 处理下一个文件
```

## 📊 配置示例

### 示例1：单附表配置
```
模板文件：汇总模板.xlsx（包含附表1、附表2等）
单体表文件：项目A.xlsx, 项目B.xlsx, 项目C.xlsx

索引配置：
附表名称：附表1
关键词：总投资,投资总额
插入行号：3
插入列号：2

结果：
附表1第3行第2列：项目A的总投资数据
附表1第4行第2列：项目B的总投资数据
附表1第5行第2列：项目C的总投资数据
```

### 示例2：多附表配置
```
索引配置1：
附表名称：附表1
关键词：总投资
插入行号：3
插入列号：2

索引配置2：
附表名称：附表2
关键词：审计金额
插入行号：5
插入列号：3

结果：
附表1第3行：项目A的总投资数据
附表1第4行：项目B的总投资数据
附表2第5行：项目A的审计金额数据
附表2第6行：项目B的审计金额数据
```

### 示例3：同附表多字段配置
```
索引配置1：
附表名称：附表1
关键词：总投资
插入行号：3
插入列号：2

索引配置2：
附表名称：附表1
关键词：审计金额
插入行号：3
插入列号：3

结果：
附表1第3行：项目A的总投资(B列) + 审计金额(C列)
附表1第4行：项目B的总投资(B列) + 审计金额(C列)
```

## ⚠️ 注意事项

### 1. 文件格式要求
- 模板文件和单体表文件都必须是Excel格式（.xls, .xlsx, .xlsm）
- 确保文件没有被其他程序占用
- 建议备份原始文件

### 2. 关键词设置
- 关键词区分大小写
- 支持部分匹配（单元格包含关键词即可）
- 多个关键词用英文逗号分隔
- 如果找到多个匹配，会选择最下面的那个

### 3. 位置设置
- 行号和列号都从1开始计数
- 确保插入位置不会覆盖模板中的重要内容
- 每个文件会自动占用一行，避免相互覆盖

### 4. 数据格式
- 数值会自动格式化为千分号+两位小数格式
- 日期和文本数据保持原格式
- 空值或无法获取的数据会显示为空

## 🚀 高级功能

### 1. 错误处理
- 单个文件处理失败不会影响其他文件
- 会显示具体的错误信息
- 处理完成后显示成功处理的文件数量

### 2. 自动保存
- 结果文件自动保存在模板文件同目录
- 文件名格式：西安汇总_yyyyMMdd_HHmmss.xlsx
- 避免覆盖原始模板文件

### 3. 数值格式化
- 继承了之前优化的数值格式化功能
- 自动处理公式计算结果
- 支持列宽自动调整

## 🔧 技术实现

### 核心类
- `XianSummaryConfig`：汇总配置类
- `XianSummaryIndexConfig`：索引配置类
- `XianSummaryConfigForm`：配置窗体类

### 核心方法
- `buttonXianSummary_Click`：按钮点击事件
- `ProcessXianSummary`：执行汇总处理
- `ProcessSingleSourceFile`：处理单个源文件
- `FindKeywordsInSource`：查找关键词位置
- `InsertDataToTemplate`：插入数据到模板

## 📋 测试建议

### 1. 基础测试
- 测试单个文件、单个关键词的情况
- 验证数据是否正确插入到指定位置
- 检查数值格式是否正确

### 2. 复杂测试
- 测试多个文件、多个关键词的情况
- 验证文件间数据不会相互覆盖
- 测试关键词匹配的准确性

### 3. 异常测试
- 测试文件不存在或无法打开的情况
- 测试关键词找不到的情况
- 测试插入位置超出范围的情况

这个功能为用户提供了一个强大而灵活的数据汇总工具，可以大大提高工作效率！
