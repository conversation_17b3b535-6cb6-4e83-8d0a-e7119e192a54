# 数值格式显示修复说明

## 问题描述

1. **小数点问题**：200600显示为"200600"，应该显示为"200,600.00"
2. **千分号问题**：没有保留千分号格式

## 解决方案

### 修改了FormatCellValue方法的数值处理逻辑

**修改前**：
```csharp
// 对整数进行特殊处理，不显示小数点
if (numValue == Math.Floor(numValue) && Math.Abs(numValue) < 1000000)
{
    return numValue.ToString("F0"); // 整数不显示小数点
}
else
{
    return numValue.ToString("F2"); // 只有非整数才显示两位小数
}
```

**修改后**：
```csharp
// 所有数值都统一格式化为带千分号的两位小数
if (cellValue is double || cellValue is float || cellValue is decimal)
{
    double numValue = Convert.ToDouble(cellValue);
    return numValue.ToString("N2"); // N2 表示带千分号的两位小数格式
}

if (cellValue is int || cellValue is long || cellValue is short)
{
    double numValue = Convert.ToDouble(cellValue);
    return numValue.ToString("N2"); // N2 表示带千分号的两位小数格式
}

// 尝试解析字符串为数值
string strValue = cellValue.ToString().Trim();
if (double.TryParse(strValue, out double parsedValue))
{
    return parsedValue.ToString("N2"); // N2 表示带千分号的两位小数格式
}
```

### .NET数值格式说明

使用`N2`格式字符串的含义：
- **N**：数字格式，自动添加千分号分隔符
- **2**：保留两位小数
- **结果示例**：
  - 200600 → "200,600.00"
  - 1234.5 → "1,234.50"
  - 123 → "123.00"
  - 1234567.89 → "1,234,567.89"

### 修复效果对比

| 原始值 | 修改前显示 | 修改后显示 |
|--------|------------|------------|
| 200600 | "200600" | "200,600.00" |
| 1234.5 | "1234.50" | "1,234.50" |
| 123 | "123" | "123.00" |
| 1234567.89 | "1234567.89" | "1,234,567.89" |
| 0 | "0" | "0.00" |

### 统一处理逻辑

现在所有数值类型都使用相同的格式化逻辑：

1. **double、float、decimal类型** → `ToString("N2")`
2. **int、long、short类型** → 转换为double后使用`ToString("N2")`
3. **字符串数值** → 解析为double后使用`ToString("N2")`
4. **非数值** → 保持原样返回

### 适用范围

这个修改影响所有使用`FormatCellValue`方法的功能：

- ✅ 生成报告（横排）
- ✅ 批量生成报告
- ✅ 生成报告（竖排）
- ✅ 链接报告（横排）
- ✅ 费用归并功能
- ✅ 各种预览和测试功能

### 特殊情况处理

1. **日期格式**：日期仍然按照Excel的原始格式显示，不受此修改影响
2. **文本格式**：纯文本内容保持原样，不会被错误格式化
3. **空值处理**：null或空字符串返回空字符串
4. **错误处理**：格式化失败时返回原始字符串

### 测试建议

1. **基本数值测试**：
   - 输入：200600
   - 期望输出：200,600.00

2. **小数测试**：
   - 输入：1234.5
   - 期望输出：1,234.50

3. **大数值测试**：
   - 输入：1234567.89
   - 期望输出：1,234,567.89

4. **边界值测试**：
   - 输入：0
   - 期望输出：0.00

5. **混合数据测试**：
   - 确保日期、文本等非数值数据不受影响

### 注意事项

- 修改保持了向后兼容性
- 所有数值现在都有统一的显示格式
- 不影响Excel链接功能的正常工作
- 错误处理机制确保程序稳定性
