﻿using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace ExcelToWord
{
    public partial class ThisAddIn
    {
        //private void ThisAddIn_Startup(object sender, System.EventArgs e)
        //{
        //    AddRegistertToWPS();
        //}
        private async void ThisAddIn_Startup(object sender, System.EventArgs e)
        {
            //MessageBox.Show("插件启动，开始检查更新...");
            await UpdateChecker.CheckForUpdateAsync();
            AddRegistertToWPS();
        }


        private static void AddRegistertToWPS()
        {
            try
            {
                RegistryKey key = Registry.CurrentUser;
                Dictionary<string, string> dic = new Dictionary<string, string>()
            {
                {@"Software\Microsoft\Office\Excel\Addins",@"Software\Kingsoft\Office\ET\AddinsWL" },
                {@"Software\Microsoft\Office\PowerPoint\Addins",@"Software\Kingsoft\Office\WPP\AddinsWL" },
                {@"Software\Microsoft\Office\Word\Addins",@"Software\Kingsoft\Office\WPS\AddinsWL" },
            };

                foreach (var item in dic)
                {
                    var addins = key.OpenSubKey(item.Key, true);
                    if (addins != null)
                    {
                        var wps = key.CreateSubKey(item.Value);
                        foreach (var subKeyName in addins.GetSubKeyNames())
                        {
                            wps.SetValue(subKeyName, subKeyName, RegistryValueKind.String);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("Error: " + ex.Message);
            }
        }

        private void ThisAddIn_Shutdown(object sender, System.EventArgs e)
        {
            try
            {
                GC.Collect();
                Marshal.FinalReleaseComObject(Globals.ThisAddIn.Application);
                GC.Collect();
            }
            catch { }

        }

        #region VSTO 生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InternalStartup()
        {
            this.Startup += new System.EventHandler(ThisAddIn_Startup);
            this.Shutdown += new System.EventHandler(ThisAddIn_Shutdown);
        }

        #endregion
    }
}
