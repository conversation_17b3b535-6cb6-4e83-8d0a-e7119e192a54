<Project ToolsVersion="16.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <!--
    This section defines project-level properties.

    AssemblyName
      Name of the output assembly.
    Configuration
      Specifies a default value for debug.
    OutputType
      Must be "Library" for VSTO.
    Platform
      Specifies what CPU the output of this project can run on.
    NoStandardLibraries
      Set to "false" for VSTO.
    RootNamespace
      In C#, this specifies the namespace given to new files. In VB, all objects are
      wrapped in this namespace at runtime.
  -->
  <PropertyGroup>
    <ProjectTypeGuids>{BAA0C2D2-18E2-41B9-852F-F413020CAA33};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{889B6827-F3AF-4CAB-A24D-9F8BFE11491B}</ProjectGuid>
    <OutputType>Library</OutputType>
    <NoStandardLibraries>false</NoStandardLibraries>
    <RootNamespace>ExcelToWord</RootNamespace>
    <AssemblyName>ExcelToWord</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <DefineConstants>VSTO40</DefineConstants>
    <IsWebBootstrapper>False</IsWebBootstrapper>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <PublishUrl>publish\</PublishUrl>
    <InstallUrl />
    <TargetCulture>zh-chs</TargetCulture>
    <ApplicationVersion>*******</ApplicationVersion>
    <AutoIncrementApplicationRevision>false</AutoIncrementApplicationRevision>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateInterval>0</UpdateInterval>
    <UpdateIntervalUnits>days</UpdateIntervalUnits>
    <ProductName>ExcelToWord</ProductName>
    <PublisherName>中联五洲</PublisherName>
    <SupportUrl />
    <FriendlyName>ExcelToWord</FriendlyName>
    <OfficeApplicationDescription />
    <LoadBehavior>3</LoadBehavior>
  </PropertyGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.7.2">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.7.2 %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.VSTORuntime.4.0">
      <Visible>False</Visible>
      <ProductName>Microsoft Visual Studio 2010 Tools for Office Runtime %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <PropertyGroup>
    <!--
      OfficeApplication
        Add-in host application
    -->
    <OfficeApplication>Excel</OfficeApplication>
  </PropertyGroup>
  <!--
    This section defines properties that are set when the "Debug" configuration is selected.

    DebugSymbols
      If "true", create symbols (.pdb). If "false", do not create symbols.
    DefineConstants
      Constants defined for the preprocessor.
    EnableUnmanagedDebugging
      If "true", starting the debugger will attach both managed and unmanaged debuggers.
    Optimize
      If "true", optimize the build output. If "false", do not optimize.
    OutputPath
      Output path of project relative to the project file.
    WarningLevel
      Warning level for the compiler.
  -->
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <DefineConstants>$(DefineConstants);DEBUG;TRACE</DefineConstants>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <!--
    This section defines properties that are set when the "Release" configuration is selected.

    DebugSymbols
      If "true", create symbols (.pdb). If "false", do not create symbols.
    DefineConstants
      Constants defined for the preprocessor.
    EnableUnmanagedDebugging
      If "true", starting the debugger will attach both managed and unmanaged debuggers.
    Optimize
      If "true", optimize the build output. If "false", do not optimize.
    OutputPath
      Output path of project relative to the project file.
    WarningLevel
      Warning level for the compiler.
  -->
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <DefineConstants>$(DefineConstants);TRACE</DefineConstants>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <!--
    This section specifies references for the project.
  -->
  <ItemGroup>
    <Reference Include="Accessibility" />
    <Reference Include="DocumentFormat.OpenXml, Version=2.5.5631.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.Office.Interop.Word, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="Microsoft.VisualStudio.Tools.Applications.ServerDocument, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <!-- 移除有问题的引用 -->
    <!-- <Reference Include="Microsoft.Windows.Design.Interaction, Version=4.3.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" /> -->
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Office.Tools.v4.0.Framework, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.VisualStudio.Tools.Applications.Runtime, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Office.Tools, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Office.Tools.Common, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Office.Tools.Excel, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Office.Tools.Common.v4.0.Utilities, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Office, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c">
      <Private>False</Private>
      <EmbedInteropTypes>true</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.Office.Interop.Excel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c">
      <Private>False</Private>
      <EmbedInteropTypes>true</EmbedInteropTypes>
    </Reference>
    <Reference Include="stdole, Version=7.0.3300.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <!--
    This section defines the user source files that are part of the project.
     
    A "Compile" element specifies a source file to compile.
    An "EmbeddedResource" element specifies an .resx file for embedded resources.
    A "None" element specifies a file that is not to be passed to the compiler (for instance, 
    a text file or XML file).
    The "AppDesigner" element specifies the directory where the application properties files
    can be found.
  -->
  <ItemGroup>
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="LicenseHelper.cs" />
    <Compile Include="MachineCodeHelper.cs" />
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="TrialHelper.cs" />
    <Compile Include="UpdateChecker.cs" />
    <Compile Include="XianSummaryConfigForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IndexRowSelectorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RangeSelectionForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IndexRangeSelectionForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="TransposeForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <EmbeddedResource Include="Ribbon1.resx">
      <DependentUpon>Ribbon1.cs</DependentUpon>
    </EmbeddedResource>
    <None Include=".editorconfig" />
    <None Include="ExcelToWord_TemporaryKey.pfx" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="Ribbon1.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Ribbon1.Designer.cs">
      <DependentUpon>Ribbon1.cs</DependentUpon>
    </Compile>
    <Compile Include="ThisAddIn.cs">
      <SubType>Code</SubType>
    </Compile>
    <None Include="ThisAddIn.Designer.xml">
      <DependentUpon>ThisAddIn.cs</DependentUpon>
    </None>
    <Compile Include="ThisAddIn.Designer.cs">
      <DependentUpon>ThisAddIn.Designer.xml</DependentUpon>
    </Compile>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <COMReference Include="Microsoft.Office.Core">
      <Guid>{2DF8D04C-5BFA-101B-BDE5-00AA0044DE52}</Guid>
      <VersionMajor>2</VersionMajor>
      <VersionMinor>8</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="Microsoft.Office.Interop.Excel1">
      <Guid>{00020813-0000-0000-C000-000000000046}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>9</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="VBIDE">
      <Guid>{0002E157-0000-0000-C000-000000000046}</Guid>
      <VersionMajor>5</VersionMajor>
      <VersionMinor>3</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>true</SignManifests>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>ExcelToWord_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>3AF7EF9F93BAE524B94CAE0D73F49D29DBF14FBC</ManifestCertificateThumbprint>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <!-- Include the build rules for a C# project. -->
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- Include additional build rules for an Office application add-in. -->
  <Import Project="$(VSToolsPath)\OfficeTools\Microsoft.VisualStudio.Tools.Office.targets" Condition="'$(VSToolsPath)' != ''" />
  <!-- This section defines VSTO properties that describe the host-changeable project properties. -->
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{BAA0C2D2-18E2-41B9-852F-F413020CAA33}">
        <ProjectProperties HostName="Excel" HostPackage="{29A7B9D7-A7F1-4328-8EF0-6B2D1A56B2C1}" OfficeVersion="15.0" VstxVersion="4.0" ApplicationType="Excel" Language="cs" TemplatesPath="" DebugInfoExeName="#Software\Microsoft\Office\16.0\Excel\InstallRoot\Path#excel.exe" DebugInfoCommandLine="/x" AddItemTemplatesGuid="{51063C3A-E220-4D12-8922-BDA915ACD783}" />
        <Host Name="Excel" GeneratedCodeNamespace="ExcelToWord" PublishedHash="69C324AB27932AA2FBF2B7EA72250886FF164DE6" IconIndex="0">
          <HostItem Name="ThisAddIn" Code="ThisAddIn.cs" CanonicalName="AddIn" PublishedHash="9762675FF4972BEEADF1426B9CC296B62D867BB9" CanActivate="false" IconIndex="1" Blueprint="ThisAddIn.Designer.xml" GeneratedCode="ThisAddIn.Designer.cs" />
        </Host>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
</Project>