# Excel报告生成功能测试说明

## 功能概述
新增的"生成Excel报告"功能可以：
1. 选择Excel中的索引行（字段名行）
2. 自动检测数据行（从索引行下一行开始，直到空行为止）
3. 与Word模板中的【】占位符进行匹配
4. 为每一行数据生成一个独立的Word报告文件

## 测试步骤

### 1. 准备测试数据
在Excel中创建如下格式的数据：

```
第1行: （可以是标题或其他内容）
第2行: 姓名    性别    年龄    部门
第3行: 张三    男      25      技术部
第4行: 李四    女      28      销售部
第5行: 王五    男      30      财务部
第6行: （空行）
```

### 2. 准备Word模板
创建一个Word文档，包含以下占位符：
```
员工信息报告

姓名：【姓名】
性别：【性别】
年龄：【年龄】
部门：【部门】

报告生成时间：【生成时间】
```

### 3. 使用功能
1. 打开包含测试数据的Excel文件
2. 点击"生成Excel报告"按钮
3. 在弹出的窗口中点击第2行（索引行）
4. 确认显示的字段信息和数据行数量
5. 选择Word模板文件
6. 选择保存文件夹
7. 等待生成完成

### 4. 预期结果
- 应该生成3个Word文件（对应3行数据）
- 每个文件中的占位符应该被正确替换
- 未匹配的占位符（如【生成时间】）应该被高亮显示

## 功能特点
- **自动检测**：无需手动输入数据行范围，程序自动检测到空行为止
- **智能匹配**：自动匹配Excel字段名与Word模板占位符
- **批量生成**：一次操作生成多个报告文件
- **错误处理**：对未匹配的字段进行高亮提示
- **用户友好**：提供直观的预览界面和确认信息

## 注意事项
- 索引行必须包含有效的字段名
- 数据行不能全部为空
- Word模板中的占位符格式必须是【字段名】
- 生成的文件名会包含行号和时间戳以避免重复
