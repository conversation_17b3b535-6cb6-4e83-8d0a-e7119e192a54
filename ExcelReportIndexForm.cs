using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Excel = Microsoft.Office.Interop.Excel;

namespace ExcelToWord
{
    /// <summary>
    /// Excel报告索引行选择表单
    /// 用于选择索引行并预览数据
    /// </summary>
    public partial class ExcelReportIndexForm : Form
    {
        private DataGridView dgvPreview;
        private Button btnOK;
        private Button btnCancel;
        private Label lblInstruction;
        private Label lblDataInfo;
        private Excel.Worksheet worksheet;

        public int SelectedIndexRow { get; private set; } = -1;
        public List<string> FieldNames { get; private set; } = new List<string>();
        public List<int> DataRows { get; private set; } = new List<int>();

        public ExcelReportIndexForm(Excel.Worksheet worksheet)
        {
            this.worksheet = worksheet;
            InitializeComponent();
            LoadWorksheetData();
        }

        private void InitializeComponent()
        {
            this.Text = "选择索引行 - Excel报告生成";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            // 说明标签
            lblInstruction = new Label
            {
                Text = "请点击选择作为字段名的索引行（通常是表头行）：",
                Location = new Point(10, 10),
                Size = new Size(760, 20),
                Font = new Font("微软雅黑", 9, FontStyle.Bold)
            };

            // 数据信息标签
            lblDataInfo = new Label
            {
                Text = "数据预览：",
                Location = new Point(10, 35),
                Size = new Size(760, 20),
                ForeColor = Color.Blue
            };

            // 数据预览表格
            dgvPreview = new DataGridView
            {
                Location = new Point(10, 60),
                Size = new Size(760, 450),
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.AllCells,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize
            };
            dgvPreview.CellClick += DgvPreview_CellClick;

            // 确定按钮
            btnOK = new Button
            {
                Text = "确定",
                Location = new Point(620, 520),
                Size = new Size(75, 30),
                Enabled = false
            };
            btnOK.Click += BtnOK_Click;

            // 取消按钮
            btnCancel = new Button
            {
                Text = "取消",
                Location = new Point(705, 520),
                Size = new Size(75, 30)
            };
            btnCancel.Click += BtnCancel_Click;

            // 添加控件到窗体
            this.Controls.Add(lblInstruction);
            this.Controls.Add(lblDataInfo);
            this.Controls.Add(dgvPreview);
            this.Controls.Add(btnOK);
            this.Controls.Add(btnCancel);
        }

        /// <summary>
        /// 加载工作表数据到预览表格
        /// </summary>
        private void LoadWorksheetData()
        {
            try
            {
                Excel.Range usedRange = worksheet.UsedRange;
                if (usedRange == null)
                {
                    MessageBox.Show("工作表中没有数据！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.DialogResult = DialogResult.Cancel;
                    return;
                }

                int rowCount = Math.Min(usedRange.Rows.Count, 20); // 最多显示20行
                int colCount = Math.Min(usedRange.Columns.Count, 15); // 最多显示15列

                // 设置列
                dgvPreview.Columns.Clear();
                for (int col = 1; col <= colCount; col++)
                {
                    dgvPreview.Columns.Add($"Col{col}", $"列{col}");
                    dgvPreview.Columns[col - 1].Width = 80;
                }

                // 添加行号列
                dgvPreview.Columns.Insert(0, new DataGridViewTextBoxColumn
                {
                    Name = "RowNumber",
                    HeaderText = "行号",
                    Width = 50,
                    ReadOnly = true
                });

                // 填充数据
                for (int row = 1; row <= rowCount; row++)
                {
                    var rowValues = new List<object> { row }; // 行号

                    for (int col = 1; col <= colCount; col++)
                    {
                        object cellValue = usedRange.Cells[row, col].Value2;
                        string displayValue = cellValue?.ToString()?.Trim() ?? "";
                        
                        // 限制显示长度
                        if (displayValue.Length > 15)
                        {
                            displayValue = displayValue.Substring(0, 12) + "...";
                        }
                        
                        rowValues.Add(displayValue);
                    }

                    dgvPreview.Rows.Add(rowValues.ToArray());
                }

                // 设置行号列的样式
                dgvPreview.Columns["RowNumber"].DefaultCellStyle.BackColor = Color.LightGray;
                dgvPreview.Columns["RowNumber"].DefaultCellStyle.Font = new Font("微软雅黑", 8, FontStyle.Bold);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载数据时出错：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.DialogResult = DialogResult.Cancel;
            }
        }

        /// <summary>
        /// 单元格点击事件
        /// </summary>
        private void DgvPreview_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                int selectedRow = e.RowIndex + 1; // 转换为Excel行号（1基索引）
                SelectedIndexRow = selectedRow;

                // 提取该行的字段名称
                FieldNames.Clear();
                for (int col = 1; col < dgvPreview.Columns.Count; col++) // 跳过行号列
                {
                    string fieldName = dgvPreview.Rows[e.RowIndex].Cells[col].Value?.ToString()?.Trim();
                    if (!string.IsNullOrEmpty(fieldName))
                    {
                        FieldNames.Add(fieldName);
                    }
                }

                // 检测数据行
                DataRows = DetectDataRowsFromWorksheet(selectedRow);

                btnOK.Enabled = FieldNames.Count > 0;

                // 更新信息显示
                string dataRowsInfo = DataRows.Count > 0 ? 
                    $"第{DataRows.First()}-{DataRows.Last()}行" : "无";
                
                lblDataInfo.Text = $"已选择第 {selectedRow} 行作为索引行 | " +
                                 $"识别字段：{FieldNames.Count}个 | " +
                                 $"数据行：{DataRows.Count}行 ({dataRowsInfo})";
                lblDataInfo.ForeColor = FieldNames.Count > 0 ? Color.Green : Color.Red;

                // 高亮选中的行
                dgvPreview.ClearSelection();
                dgvPreview.Rows[e.RowIndex].Selected = true;
            }
        }

        /// <summary>
        /// 从工作表检测数据行
        /// </summary>
        private List<int> DetectDataRowsFromWorksheet(int indexRow)
        {
            var dataRows = new List<int>();
            
            try
            {
                Excel.Range usedRange = worksheet.UsedRange;
                int maxRow = usedRange.Rows.Count;

                // 从索引行的下一行开始检测
                for (int row = indexRow + 1; row <= maxRow; row++)
                {
                    // 检查这一行是否为空行
                    bool isEmpty = true;
                    for (int col = 1; col <= FieldNames.Count && col <= usedRange.Columns.Count; col++)
                    {
                        object cellValue = worksheet.Cells[row, col].Value2;
                        if (cellValue != null && !string.IsNullOrWhiteSpace(cellValue.ToString()))
                        {
                            isEmpty = false;
                            break;
                        }
                    }

                    if (isEmpty)
                    {
                        break; // 遇到空行就停止
                    }

                    dataRows.Add(row);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"检测数据行时出错：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }

            return dataRows;
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (SelectedIndexRow <= 0 || FieldNames.Count == 0)
            {
                MessageBox.Show("请先选择一个有效的索引行！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
