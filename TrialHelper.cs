﻿using System;
using System.IO;

public static class TrialHelper
{
    private const string trialFileName = "excel_plugin_trial.txt";

    public static string GetTrialFilePath()
    {
        string folder = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
        return Path.Combine(folder, trialFileName);
    }

    public static DateTime? LoadTrialStartDate()
    {
        string path = GetTrialFilePath();
        if (!File.Exists(path))
        {
            return null;
        }

        string content = File.ReadAllText(path);
        if (DateTime.TryParse(content, out DateTime date))
        {
            return date;
        }
        return null;
    }

    public static void SaveTrialStartDate(DateTime date)
    {
        string path = GetTrialFilePath();
        File.WriteAllText(path, date.ToString("yyyy-MM-dd"));
    }

    public static bool IsTrialExpired()
    {
        DateTime? startDate = LoadTrialStartDate();
        if (startDate == null)
        {
            SaveTrialStartDate(DateTime.Today);
            return false;
        }

        TimeSpan diff = DateTime.Today - startDate.Value;
        return diff.TotalDays > 90;
    }

    public static bool IsTrialValid()
    {
        return !IsTrialExpired();
    }

    public static int GetRemainingDays()
    {
        DateTime? startDate = LoadTrialStartDate();
        if (startDate == null)
        {
            return 90;
        }

        TimeSpan diff = DateTime.Today - startDate.Value;
        return Math.Max(0, 90 - (int)diff.TotalDays);
    }
}
