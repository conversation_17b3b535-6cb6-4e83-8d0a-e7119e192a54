# Excel报告生成功能演示指南

## 新功能位置
在Excel的"中联五洲"选项卡中，"生成报告"组内新增了"生成Excel报告"按钮。

## 操作流程演示

### 1. 数据准备示例
```
Excel数据示例：
A列    B列    C列    D列
标题   信息   表格   说明     ← 第1行（可选）
姓名   性别   年龄   部门     ← 第2行（索引行）
张三   男     25     技术部   ← 第3行（数据行1）
李四   女     28     销售部   ← 第4行（数据行2）
王五   男     30     财务部   ← 第5行（数据行3）
                            ← 第6行（空行，自动停止）
```

### 2. Word模板示例
```
员工档案

基本信息：
姓名：【姓名】
性别：【性别】
年龄：【年龄】
工作部门：【部门】

备注：
联系方式：【联系方式】  ← 这个字段在Excel中不存在，会被高亮
```

### 3. 操作步骤
1. **点击按钮**：在Excel中点击"生成Excel报告"
2. **选择索引行**：
   - 弹出数据预览窗口
   - 显示前20行数据供预览
   - 点击第2行选择为索引行
   - 系统显示：已选择第2行，识别到4个字段，数据行3行
3. **选择模板**：选择准备好的Word模板文件
4. **选择保存位置**：选择报告保存的文件夹
5. **确认生成**：系统显示匹配信息，确认后开始生成

### 4. 生成结果
系统会生成3个Word文件：
- `Excel报告_张三_第3行_20250811_143022.docx`
- `Excel报告_李四_第4行_20250811_143023.docx`
- `Excel报告_王五_第5行_20250811_143024.docx`

每个文件中：
- 【姓名】→ 对应人员姓名
- 【性别】→ 对应人员性别
- 【年龄】→ 对应人员年龄
- 【部门】→ 对应人员部门
- 【联系方式】→ 保持原样并高亮显示（因为Excel中没有此字段）

## 功能优势

### 相比手动操作
- **效率提升**：原来需要逐个复制粘贴，现在一键批量生成
- **减少错误**：自动匹配字段，避免人工错误
- **标准化**：确保所有报告格式一致

### 相比现有功能
- **自动化程度更高**：无需手动指定数据行范围
- **更智能**：自动检测数据边界
- **更直观**：提供数据预览和匹配信息

## 适用场景
- 员工档案批量生成
- 客户资料报告制作
- 产品信息单页生成
- 证书批量制作
- 任何需要"一行数据生成一个文档"的场景

## 注意事项
1. **数据格式**：确保索引行包含清晰的字段名
2. **模板格式**：使用【字段名】格式的占位符
3. **数据完整性**：空行会被视为数据结束标志
4. **文件权限**：确保有Word模板的读取权限和保存文件夹的写入权限

这个功能大大简化了批量报告生成的工作流程，特别适合需要处理大量相似数据的办公场景。
