using System;
using System.Drawing;
using System.Windows.Forms;
using Excel = Microsoft.Office.Interop.Excel;

namespace ExcelToWord
{
    public partial class TransposeForm : Form
    {
        private Label lblSourceRange;
        private TextBox txtSourceRange;
        private Button btnSelectSourceRange;
        private Label lblStartRow;
        private NumericUpDown numStartRow;
        
        private Label lblTargetCell;
        private TextBox txtTargetCell;
        private Button btnSelectTargetCell;
        
        private Label lblTargetRow;
        private NumericUpDown numTargetRow;
        
        private Label lblTargetColumn;
        private TextBox txtTargetColumn;
        
        private Button btnOK;
        private Button btnCancel;
        
        private Excel.Application excelApp;
        
        public string SourceRange { get; private set; }
        public int StartRow { get; private set; }
        public string TargetCell { get; private set; }
        public int TargetRow { get; private set; }
        public string TargetColumn { get; private set; }
        
        public TransposeForm()
        {
            InitializeComponent();
            excelApp = Globals.ThisAddIn.Application;
        }
        
        private void InitializeComponent()
        {
            this.Text = "数据转置设置";
            this.Size = new Size(800, 600);
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MaximizeBox = true;
            this.MinimizeBox = true;
            this.MinimumSize = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.BackColor = Color.White;

            // 添加说明标签
            Label lblDescription = new Label
            {
                Text = "功能说明：将选择的多列数据按行优先方式转置到指定的单列中\n" +
                       "支持选择开始行，保留空单元格，转置后源数据保持不变",
                Location = new Point(10, 20),
                Size = new Size(100, 50),
                ForeColor = Color.Blue,
                Font = new Font("Microsoft YaHei", 10F, FontStyle.Bold),
                TextAlign = ContentAlignment.TopLeft,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };
            this.Controls.Add(lblDescription);
            
            // 源范围选择
            lblSourceRange = new Label
            {
                Text = "选择源列范围 (如: X:AA):",
                Location = new Point(20, 100),
                Size = new Size(150, 30),
                Font = new Font("Microsoft YaHei", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                AutoSize = false
            };

            txtSourceRange = new TextBox
            {
                Location = new Point(230, 98),
                Size = new Size(250, 30),
                ReadOnly = true,
                Font = new Font("Microsoft YaHei", 10F),
                BackColor = Color.LightGray,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            btnSelectSourceRange = new Button
            {
                Text = "选择范围",
                Location = new Point(490, 97),
                Size = new Size(120, 32),
                Font = new Font("Microsoft YaHei", 10F),
                BackColor = Color.LightSteelBlue,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };
            btnSelectSourceRange.Click += BtnSelectSourceRange_Click;

            // 开始行设置
            lblStartRow = new Label
            {
                Text = "从第几行开始转置:",
                Location = new Point(20, 150),
                Size = new Size(200, 30),
                Font = new Font("Microsoft YaHei", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                AutoSize = false
            };

            numStartRow = new NumericUpDown
            {
                Location = new Point(230, 148),
                Size = new Size(150, 30),
                Font = new Font("Microsoft YaHei", 10F),
                Minimum = 1,
                Maximum = 1048576,
                Value = 1
            };

            // 目标位置设置
            lblTargetCell = new Label
            {
                Text = "目标起始位置 (可选):",
                Location = new Point(20, 200),
                Size = new Size(200, 30),
                Font = new Font("Microsoft YaHei", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                AutoSize = false
            };

            txtTargetCell = new TextBox
            {
                Location = new Point(230, 198),
                Size = new Size(250, 30),
                ReadOnly = true,
                Text = "可选择具体位置或手动设置",
                Font = new Font("Microsoft YaHei", 10F),
                BackColor = Color.LightGray,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            btnSelectTargetCell = new Button
            {
                Text = "选择位置",
                Location = new Point(490, 197),
                Size = new Size(120, 32),
                Font = new Font("Microsoft YaHei", 10F),
                BackColor = Color.LightSteelBlue,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };
            btnSelectTargetCell.Click += BtnSelectTargetCell_Click;
            
            // 目标行设置
            lblTargetRow = new Label
            {
                Text = "转置到第几行开始:",
                Location = new Point(20, 250),
                Size = new Size(200, 30),
                Font = new Font("Microsoft YaHei", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                AutoSize = false
            };

            numTargetRow = new NumericUpDown
            {
                Location = new Point(230, 248),
                Size = new Size(150, 30),
                Font = new Font("Microsoft YaHei", 10F),
                Minimum = 1,
                Maximum = 1048576,
                Value = 3
            };

            // 目标列设置
            lblTargetColumn = new Label
            {
                Text = "转置到哪一列 (如: X):",
                Location = new Point(20, 300),
                Size = new Size(200, 30),
                Font = new Font("Microsoft YaHei", 10F),
                TextAlign = ContentAlignment.MiddleLeft,
                AutoSize = false
            };

            txtTargetColumn = new TextBox
            {
                Location = new Point(230, 298),
                Size = new Size(150, 30),
                Font = new Font("Microsoft YaHei", 10F),
                Text = "X"
            };
            
            // 添加示例说明
            Label lblExample = new Label
            {
                Text = "示例：A-B列数据从第2行开始(2行:1,2  3行:3,空  4行:5,6)，转置到C列第1行\n" +
                       "结果：C1=1，C2=2，C3=3，C4=空，C5=5，C6=6，保留空格和原始数据",
                Location = new Point(20, 350),
                Size = new Size(600, 70),
                Font = new Font("Microsoft YaHei", 9F),
                ForeColor = Color.Gray,
                TextAlign = ContentAlignment.TopLeft,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            // 按钮 - 放在显眼位置
            btnOK = new Button
            {
                Text = "✓ 开始转置",
                Location = new Point(200, 440),
                Size = new Size(150, 45),
                Font = new Font("Microsoft YaHei", 12F, FontStyle.Bold),
                BackColor = Color.FromArgb(0, 120, 215), // Windows蓝色
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                DialogResult = DialogResult.OK,
                Anchor = AnchorStyles.Bottom | AnchorStyles.None
            };
            btnOK.FlatAppearance.BorderSize = 0;
            btnOK.Click += BtnOK_Click;

            btnCancel = new Button
            {
                Text = "✗ 取消",
                Location = new Point(370, 440),
                Size = new Size(120, 45),
                Font = new Font("Microsoft YaHei", 11F),
                BackColor = Color.Gray,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                DialogResult = DialogResult.Cancel,
                Anchor = AnchorStyles.Bottom | AnchorStyles.None
            };
            btnCancel.FlatAppearance.BorderSize = 0;

            this.Controls.Add(lblExample);
            
            // 添加控件到窗体
            this.Controls.AddRange(new Control[]
            {
                lblSourceRange, txtSourceRange, btnSelectSourceRange,
                lblStartRow, numStartRow,
                lblTargetCell, txtTargetCell, btnSelectTargetCell,
                lblTargetRow, numTargetRow,
                lblTargetColumn, txtTargetColumn,
                btnOK, btnCancel
            });
        }
        
        private void BtnSelectSourceRange_Click(object sender, EventArgs e)
        {
            try
            {
                this.WindowState = FormWindowState.Minimized;
                
                Excel.Range selectedRange = excelApp.InputBox(
                    "请选择要转置的列范围 (如: X:AA)",
                    "选择源列范围",
                    Type: 8) as Excel.Range;
                
                this.WindowState = FormWindowState.Normal;
                this.BringToFront();
                
                if (selectedRange != null)
                {
                    txtSourceRange.Text = selectedRange.Address.Replace("$", "");
                }
            }
            catch (Exception ex)
            {
                this.WindowState = FormWindowState.Normal;
                MessageBox.Show("选择范围时出错: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void BtnSelectTargetCell_Click(object sender, EventArgs e)
        {
            try
            {
                this.WindowState = FormWindowState.Minimized;
                
                Excel.Range selectedRange = excelApp.InputBox(
                    "请选择目标起始位置",
                    "选择目标位置",
                    Type: 8) as Excel.Range;
                
                this.WindowState = FormWindowState.Normal;
                this.BringToFront();
                
                if (selectedRange != null)
                {
                    txtTargetCell.Text = selectedRange.Address.Replace("$", "");
                    
                    // 自动填充行号和列号
                    numTargetRow.Value = selectedRange.Row;
                    txtTargetColumn.Text = GetColumnLetter(selectedRange.Column);
                }
            }
            catch (Exception ex)
            {
                this.WindowState = FormWindowState.Normal;
                MessageBox.Show("选择位置时出错: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtSourceRange.Text))
            {
                MessageBox.Show("请选择源列范围！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            if (string.IsNullOrEmpty(txtTargetColumn.Text))
            {
                MessageBox.Show("请输入目标列！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            SourceRange = txtSourceRange.Text;
            StartRow = (int)numStartRow.Value;
            TargetCell = txtTargetCell.Text;
            TargetRow = (int)numTargetRow.Value;
            TargetColumn = txtTargetColumn.Text.Trim().ToUpper();

            this.DialogResult = DialogResult.OK;
            this.Close();
            this.Close();
        }
        
        private string GetColumnLetter(int columnNumber)
        {
            string columnName = "";
            while (columnNumber > 0)
            {
                int modulo = (columnNumber - 1) % 26;
                columnName = Convert.ToChar('A' + modulo) + columnName;
                columnNumber = (columnNumber - modulo) / 26;
            }
            return columnName;
        }
    }
}
