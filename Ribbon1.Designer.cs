
namespace ExcelToWord
{
    partial class Ribbon1 : Microsoft.Office.Tools.Ribbon.RibbonBase
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        public Ribbon1()
            : base(Globals.Factory.GetRibbonFactory())
        {
            InitializeComponent();
        }

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.tab1 = this.Factory.CreateRibbonTab();
            this.group1 = this.Factory.CreateRibbonGroup();
            this.button1 = this.Factory.CreateRibbonButton();
            this.group3 = this.Factory.CreateRibbonGroup();
            this.button17 = this.Factory.CreateRibbonButton();
            this.button2 = this.Factory.CreateRibbonButton();
            this.button3 = this.Factory.CreateRibbonButton();
            this.buttonExcelReport = this.Factory.CreateRibbonButton();
            this.group4 = this.Factory.CreateRibbonGroup();
            this.button5 = this.Factory.CreateRibbonButton();
            this.button6 = this.Factory.CreateRibbonButton();
            this.button8 = this.Factory.CreateRibbonButton();
            this.button7 = this.Factory.CreateRibbonButton();
            this.button9 = this.Factory.CreateRibbonButton();
            this.button10 = this.Factory.CreateRibbonButton();
            this.button12 = this.Factory.CreateRibbonButton();
            this.button4 = this.Factory.CreateRibbonButton();
            this.buttonExpenseMerge = this.Factory.CreateRibbonButton();
            this.buttonTranspose = this.Factory.CreateRibbonButton();
            this.group2 = this.Factory.CreateRibbonGroup();
            this.button13 = this.Factory.CreateRibbonButton();
            this.button14 = this.Factory.CreateRibbonButton();
            this.button15 = this.Factory.CreateRibbonButton();
            this.buttonXianSummary = this.Factory.CreateRibbonButton();
            this.buttonFixedSummary = this.Factory.CreateRibbonButton();
            this.group6 = this.Factory.CreateRibbonGroup();
            this.button11 = this.Factory.CreateRibbonButton();
            this.button16 = this.Factory.CreateRibbonButton();
            this.registerButton = this.Factory.CreateRibbonButton();
            this.trialDaysButton = this.Factory.CreateRibbonButton();
            this.tab1.SuspendLayout();
            this.group1.SuspendLayout();
            this.group3.SuspendLayout();
            this.group4.SuspendLayout();
            this.group2.SuspendLayout();
            this.group6.SuspendLayout();
            this.SuspendLayout();
            // 
            // tab1
            // 
            this.tab1.Groups.Add(this.group1);
            this.tab1.Groups.Add(this.group3);
            this.tab1.Groups.Add(this.group4);
            this.tab1.Groups.Add(this.group2);
            this.tab1.Groups.Add(this.group6);
            this.tab1.Label = "中联五洲";
            this.tab1.Name = "tab1";
            // 
            // group1
            // 
            this.group1.Items.Add(this.button1);
            this.group1.Label = "程序窗口";
            this.group1.Name = "group1";
            this.group1.Visible = false;
            // 
            // button1
            // 
            this.button1.ControlSize = Microsoft.Office.Core.RibbonControlSize.RibbonControlSizeLarge;
            this.button1.Enabled = false;
            this.button1.Label = "显示程序窗口";
            this.button1.Name = "button1";
            this.button1.ShowImage = true;
            this.button1.Visible = false;
            this.button1.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button1_Click);
            // 
            // group3
            // 
            this.group3.Items.Add(this.button17);
            this.group3.Items.Add(this.button2);
            this.group3.Items.Add(this.button3);
            this.group3.Items.Add(this.buttonExcelReport);
            this.group3.Label = "生成报告";
            this.group3.Name = "group3";
            // 
            // button17
            // 
            this.button17.ControlSize = Microsoft.Office.Core.RibbonControlSize.RibbonControlSizeLarge;
            this.button17.Label = "链接报告（横排）";
            this.button17.Name = "button17";
            this.button17.ShowImage = true;
            this.button17.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button17_Click);
            // 
            // button2
            // 
            this.button2.ControlSize = Microsoft.Office.Core.RibbonControlSize.RibbonControlSizeLarge;
            this.button2.Label = "生成报告(竖排)";
            this.button2.Name = "button2";
            this.button2.ShowImage = true;
            this.button2.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button2_Click);
            // 
            // button3
            // 
            this.button3.ControlSize = Microsoft.Office.Core.RibbonControlSize.RibbonControlSizeLarge;
            this.button3.Label = "生成报告(横排)";
            this.button3.Name = "button3";
            this.button3.ShowImage = true;
            this.button3.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button3_Click);
            //
            // buttonExcelReport
            //
            this.buttonExcelReport.ControlSize = Microsoft.Office.Core.RibbonControlSize.RibbonControlSizeLarge;
            this.buttonExcelReport.Label = "生成Excel报告";
            this.buttonExcelReport.Name = "buttonExcelReport";
            this.buttonExcelReport.ShowImage = true;
            this.buttonExcelReport.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonExcelReport_Click);
            // 
            // group4
            // 
            this.group4.Items.Add(this.button5);
            this.group4.Items.Add(this.button6);
            this.group4.Items.Add(this.button8);
            this.group4.Items.Add(this.button7);
            this.group4.Items.Add(this.button9);
            this.group4.Items.Add(this.button10);
            this.group4.Items.Add(this.button12);
            this.group4.Items.Add(this.button4);
            this.group4.Items.Add(this.buttonExpenseMerge);
            this.group4.Items.Add(this.buttonTranspose);
            this.group4.Label = "Excel->Word";
            this.group4.Name = "group4";
            // 
            // button5
            // 
            this.button5.Label = "改占位符";
            this.button5.Name = "button5";
            this.button5.ShowImage = true;
            this.button5.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button5_Click);
            // 
            // button6
            // 
            this.button6.Label = "链接修改";
            this.button6.Name = "button6";
            this.button6.ShowImage = true;
            this.button6.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button6_Click);
            // 
            // button8
            // 
            this.button8.Label = "插入链接";
            this.button8.Name = "button8";
            this.button8.ShowImage = true;
            this.button8.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button8_Click);
            // 
            // button7
            // 
            this.button7.Label = "写占位符";
            this.button7.Name = "button7";
            this.button7.ShowImage = true;
            this.button7.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button7_Click);
            // 
            // button9
            // 
            this.button9.Label = "写入文档";
            this.button9.Name = "button9";
            this.button9.ShowImage = true;
            this.button9.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button9_Click);
            // 
            // button10
            // 
            this.button10.Label = "写入表格";
            this.button10.Name = "button10";
            this.button10.ShowImage = true;
            this.button10.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button10_Click);
            // 
            // button12
            // 
            this.button12.Label = "word表格删空行";
            this.button12.Name = "button12";
            this.button12.ShowLabel = false;
            this.button12.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button12_Click);
            // 
            // button4
            // 
            this.button4.Label = "生成报告(一对多)";
            this.button4.Name = "button4";
            this.button4.ShowLabel = false;
            this.button4.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button4_Click);
            // 
            // buttonExpenseMerge
            //
            this.buttonExpenseMerge.Label = "费用归并";
            this.buttonExpenseMerge.Name = "buttonExpenseMerge";
            this.buttonExpenseMerge.ShowImage = true;
            this.buttonExpenseMerge.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonExpenseMerge_Click);
            //
            // buttonTranspose
            //
            this.buttonTranspose.Label = "数据转置";
            this.buttonTranspose.Name = "buttonTranspose";
            this.buttonTranspose.ShowImage = true;
            this.buttonTranspose.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonTranspose_Click);
            // 
            // group2
            // 
            this.group2.Items.Add(this.button13);
            this.group2.Items.Add(this.button14);
            this.group2.Items.Add(this.button15);
            this.group2.Items.Add(this.buttonXianSummary);
            this.group2.Items.Add(this.buttonFixedSummary);
            this.group2.Label = "数据表操作";
            this.group2.Name = "group2";
            // 
            // button13
            // 
            this.button13.Label = "西安固定";
            this.button13.Name = "button13";
            this.button13.ShowImage = true;
            this.button13.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button13_Click);
            // 
            // button14
            // 
            this.button14.Label = "多表合并";
            this.button14.Name = "button14";
            this.button14.ShowImage = true;
            this.button14.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button14_Click);
            // 
            // button15
            // 
            this.button15.Label = "多表汇总";
            this.button15.Name = "button15";
            this.button15.ShowImage = true;
            this.button15.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button15_Click);
            // 
            // buttonXianSummary
            // 
            this.buttonXianSummary.Label = "西安汇总";
            this.buttonXianSummary.Name = "buttonXianSummary";
            this.buttonXianSummary.ShowImage = true;
            this.buttonXianSummary.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonXianSummary_Click);
            //
            // buttonFixedSummary
            //
            this.buttonFixedSummary.Label = "固定汇总";
            this.buttonFixedSummary.Name = "buttonFixedSummary";
            this.buttonFixedSummary.ShowImage = true;
            this.buttonFixedSummary.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonFixedSummary_Click);
            //
            // group6
            // 
            this.group6.Items.Add(this.button11);
            this.group6.Items.Add(this.button16);
            this.group6.Items.Add(this.registerButton);
            this.group6.Items.Add(this.trialDaysButton);
            this.group6.Label = "版本、帮助";
            this.group6.Name = "group6";
            // 
            // button11
            // 
            this.button11.Label = "版本信息";
            this.button11.Name = "button11";
            this.button11.ShowImage = true;
            this.button11.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button11_Click);
            // 
            // button16
            // 
            this.button16.Label = "检查更新";
            this.button16.Name = "button16";
            this.button16.ShowImage = true;
            this.button16.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button16_Click);
            // 
            // registerButton
            // 
            this.registerButton.Label = "立即注册";
            this.registerButton.Name = "registerButton";
            this.registerButton.ShowImage = true;
            this.registerButton.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.registerButton_Click);
            // 
            // trialDaysButton
            // 
            this.trialDaysButton.ControlSize = Microsoft.Office.Core.RibbonControlSize.RibbonControlSizeLarge;
            this.trialDaysButton.Enabled = false;
            this.trialDaysButton.Label = "试用剩余 -- 天";
            this.trialDaysButton.Name = "trialDaysButton";
            this.trialDaysButton.ShowImage = true;
            this.trialDaysButton.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.trialDaysButton_Click);
            // 
            // Ribbon1
            // 
            this.Name = "Ribbon1";
            this.RibbonType = "Microsoft.Excel.Workbook";
            this.Tabs.Add(this.tab1);
            this.Load += new Microsoft.Office.Tools.Ribbon.RibbonUIEventHandler(this.Ribbon1_Load);
            this.tab1.ResumeLayout(false);
            this.tab1.PerformLayout();
            this.group1.ResumeLayout(false);
            this.group1.PerformLayout();
            this.group3.ResumeLayout(false);
            this.group3.PerformLayout();
            this.group4.ResumeLayout(false);
            this.group4.PerformLayout();
            this.group2.ResumeLayout(false);
            this.group2.PerformLayout();
            this.group6.ResumeLayout(false);
            this.group6.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        internal Microsoft.Office.Tools.Ribbon.RibbonTab tab1;
        internal Microsoft.Office.Tools.Ribbon.RibbonGroup group3;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button3;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button17;
        internal Microsoft.Office.Tools.Ribbon.RibbonGroup group4;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button5;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button6;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button8;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button9;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button10;
        internal Microsoft.Office.Tools.Ribbon.RibbonGroup group6;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button11;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button2;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button12;
        internal Microsoft.Office.Tools.Ribbon.RibbonGroup group2;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button13;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button14;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button15;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton buttonXianSummary;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton buttonFixedSummary;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button16;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton trialDaysButton;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton registerButton;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button4;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button7;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton buttonExpenseMerge;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton buttonTranspose;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton buttonExcelReport;
        internal Microsoft.Office.Tools.Ribbon.RibbonGroup group1;
        public Microsoft.Office.Tools.Ribbon.RibbonButton button1;
    }

    partial class ThisRibbonCollection
    {
        internal Ribbon1 Ribbon1
        {
            get { return this.GetRibbon<Ribbon1>(); }
        }
    }
}
