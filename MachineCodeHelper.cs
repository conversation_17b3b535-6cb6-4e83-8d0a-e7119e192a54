﻿using System.Management;

public static class MachineCodeHelper
{
    public static string GetMachineCode()
    {
        string cpuId = GetHardwareId("Win32_Processor", "ProcessorId");
        string mac = GetHardwareId("Win32_NetworkAdapterConfiguration", "MACAddress", "IPEnabled");
        return (cpuId + mac).Replace(":", "").Replace("-", "").ToUpper();
    }

    private static string GetHardwareId(string wmiClass, string wmiProperty, string conditionProp = null)
    {
        try
        {
            var mbs = new ManagementObjectSearcher("SELECT * FROM " + wmiClass);
            foreach (ManagementObject mo in mbs.Get())
            {
                if (conditionProp != null && mo[conditionProp] is bool b && !b) continue;
                return mo[wmiProperty]?.ToString();
            }
        }
        catch { }
        return "UNKNOWN";
    }
}
