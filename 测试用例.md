# 数据转置功能测试用例

## 测试准备

### 1. 创建测试数据
在Excel中创建以下测试数据：

| 行号 | X列 | Y列 | Z列 | AA列 |
|------|-----|-----|-----|------|
| 1    | JJJ | BBB | CCC | ddd  |
| 2    | 空  | 空  | 空  | 空   |
| 3    | 空  | 空  | 空  | 空   |
| 4    | QQQ | www | eee | rrr  |
| 5    | 111 | 222 | 333 | 444  |

### 2. 预期结果
转置到列AB，起始行为3：

| 行号 | AB列 |
|------|------|
| 3    | JJJ  |
| 4    | QQQ  |
| 5    | 111  |
| 6    | BBB  |
| 7    | www  |
| 8    | 222  |
| 9    | CCC  |
| 10   | eee  |
| 11   | 333  |
| 12   | ddd  |
| 13   | rrr  |
| 14   | 444  |

## 测试步骤

### 测试用例1：基本转置功能
1. 打开Excel，创建上述测试数据
2. 点击"数据转置"按钮
3. 在弹出的窗体中：
   - 点击"选择范围"，选择X:AA列
   - 设置目标列为AB
   - 设置起始行为3
4. 点击"开始转置"
5. 验证结果是否符合预期

### 测试用例2：范围选择验证
1. 尝试选择不连续的列范围
2. 验证是否有适当的错误提示

### 测试用例3：目标位置验证
1. 输入无效的列名（如数字）
2. 验证是否有适当的错误提示

### 测试用例4：空数据处理
1. 在源范围中包含空单元格
2. 验证空单元格是否被正确跳过

### 测试用例5：格式保留
1. 为源数据设置不同的格式（颜色、字体等）
2. 执行转置
3. 验证格式是否被正确复制

## 验证要点

1. **数据顺序**：确保按列优先的顺序进行转置
2. **空值处理**：空单元格应被跳过
3. **格式保留**：源单元格的格式应被复制到目标位置
4. **错误处理**：无效输入应有适当的错误提示
5. **完成提示**：操作完成后应显示转置的数据数量

## 常见问题排查

### 问题1：转置按钮不可见
- 检查Ribbon1.Designer.cs中是否正确添加了buttonTranspose
- 检查按钮是否被添加到正确的组中

### 问题2：点击按钮无响应
- 检查事件处理程序是否正确绑定
- 检查是否有编译错误

### 问题3：选择范围时出错
- 检查Excel应用程序是否正确获取
- 检查InputBox的参数是否正确

### 问题4：转置结果不正确
- 检查列号转换逻辑
- 检查循环顺序（应该是列优先）

## 性能测试

### 大数据量测试
1. 创建包含1000行、10列的测试数据
2. 执行转置操作
3. 验证性能和内存使用情况

### 内存泄漏测试
1. 多次执行转置操作
2. 监控内存使用情况
3. 确保COM对象被正确释放
