using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Excel = Microsoft.Office.Interop.Excel;

namespace ExcelToWord
{
    /// <summary>
    /// 索引行选择窗体 - 用于选择单行索引
    /// </summary>
    public partial class IndexRowSelectorForm : Form
    {
        private string filePath;
        private string sheetName;
        private DataGridView dgvPreview;
        private Button btnOK, btnCancel;
        private Label lblInstruction;
        
        public int SelectedRow { get; private set; }
        public List<string> FieldNames { get; private set; }

        public IndexRowSelectorForm(string filePath, string sheetName)
        {
            this.filePath = filePath;
            this.sheetName = sheetName;
            this.FieldNames = new List<string>();
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = "选择索引行";
            this.Size = new Size(900, 600);
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MinimumSize = new Size(700, 500);
            this.StartPosition = FormStartPosition.CenterParent;

            // 说明标签
            lblInstruction = new Label
            {
                Text = "请点击选择包含字段名称的行：",
                Location = new Point(20, 20),
                Size = new Size(300, 23),
                Font = new Font("微软雅黑", 9)
            };

            // 数据预览表格
            dgvPreview = new DataGridView
            {
                Location = new Point(20, 50),
                Size = new Size(840, 450),
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                DefaultCellStyle = new DataGridViewCellStyle { Font = new Font("微软雅黑", 9) },
                RowHeadersWidth = 60
            };
            dgvPreview.CellClick += DgvPreview_CellClick;

            // 底部按钮
            btnOK = new Button
            {
                Text = "确定",
                Location = new Point(700, 520),
                Size = new Size(75, 30),
                DialogResult = DialogResult.OK,
                Font = new Font("微软雅黑", 9),
                Enabled = false
            };

            btnCancel = new Button
            {
                Text = "取消",
                Location = new Point(785, 520),
                Size = new Size(75, 30),
                DialogResult = DialogResult.Cancel,
                Font = new Font("微软雅黑", 9)
            };

            // 添加控件到窗体
            this.Controls.AddRange(new Control[] {
                lblInstruction, dgvPreview, btnOK, btnCancel
            });
        }

        private void LoadData()
        {
            Excel.Application excelApp = null;
            Excel.Workbook workbook = null;

            try
            {
                excelApp = new Excel.Application();
                excelApp.Visible = false;
                excelApp.ScreenUpdating = false;
                excelApp.DisplayAlerts = false;

                workbook = excelApp.Workbooks.Open(filePath, ReadOnly: true);
                Excel.Worksheet worksheet = workbook.Worksheets[sheetName];

                // 读取数据并填充到DataGridView
                int maxRows = Math.Min(50, worksheet.UsedRange.Rows.Count);
                int maxCols = Math.Min(15, worksheet.UsedRange.Columns.Count);

                // 设置列
                dgvPreview.Columns.Clear();
                for (int col = 1; col <= maxCols; col++)
                {
                    string columnName = GetColumnLetter(col);
                    dgvPreview.Columns.Add(columnName, columnName);
                    dgvPreview.Columns[col - 1].Width = 100;
                }

                // 填充数据
                for (int row = 1; row <= maxRows; row++)
                {
                    object[] rowData = new object[maxCols];
                    for (int col = 1; col <= maxCols; col++)
                    {
                        Excel.Range cell = worksheet.Cells[row, col];
                        rowData[col - 1] = GetCellDisplayValue(cell) ?? "";
                    }
                    dgvPreview.Rows.Add(rowData);
                }

                // 设置行标题
                for (int i = 0; i < dgvPreview.Rows.Count; i++)
                {
                    dgvPreview.Rows[i].HeaderCell.Value = (i + 1).ToString();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载数据时出错：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                workbook?.Close(false);
                excelApp?.Quit();
                if (workbook != null) System.Runtime.InteropServices.Marshal.ReleaseComObject(workbook);
                if (excelApp != null) System.Runtime.InteropServices.Marshal.ReleaseComObject(excelApp);
            }
        }

        private void DgvPreview_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                SelectedRow = e.RowIndex + 1; // 转换为1基索引
                
                // 提取该行的字段名称
                FieldNames.Clear();
                for (int col = 0; col < dgvPreview.Columns.Count; col++)
                {
                    string fieldName = dgvPreview.Rows[e.RowIndex].Cells[col].Value?.ToString()?.Trim();
                    if (!string.IsNullOrEmpty(fieldName))
                    {
                        FieldNames.Add(fieldName);
                    }
                }

                btnOK.Enabled = true;
                
                // 更新说明标签
                lblInstruction.Text = $"已选择第 {SelectedRow} 行，识别到 {FieldNames.Count} 个字段：{string.Join(", ", FieldNames.Take(5))}{(FieldNames.Count > 5 ? "..." : "")}";
            }
        }

        private string GetColumnLetter(int columnNumber)
        {
            string columnName = "";
            while (columnNumber > 0)
            {
                int modulo = (columnNumber - 1) % 26;
                columnName = Convert.ToChar('A' + modulo) + columnName;
                columnNumber = (columnNumber - modulo) / 26;
            }
            return columnName;
        }

        private string GetCellDisplayValue(Excel.Range cell)
        {
            try
            {
                if (cell?.Value != null)
                {
                    return cell.Value.ToString();
                }
                return "";
            }
            catch
            {
                return "";
            }
        }
    }
}
