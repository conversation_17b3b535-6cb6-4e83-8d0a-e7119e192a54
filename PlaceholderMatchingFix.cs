using System;
using Word = Microsoft.Office.Interop.Word;

namespace ExcelToWord
{
    /// <summary>
    /// 占位符精确匹配修复验证类
    /// 用于解决链接报告中占位符匹配错误的问题
    /// </summary>
    public class PlaceholderMatchingFix
    {
        /// <summary>
        /// 测试占位符匹配的准确性
        /// </summary>
        /// <param name="doc">Word文档</param>
        /// <param name="placeholder">占位符，如【国网四平供电公司】</param>
        /// <returns>匹配结果信息</returns>
        public static string TestPlaceholderMatching(Word.Document doc, string placeholder)
        {
            try
            {
                int exactMatches = 0;
                int partialMatches = 0;
                
                Word.Range searchRange = doc.Content;
                Word.Find findObject = searchRange.Find;
                findObject.ClearFormatting();
                findObject.Text = placeholder;
                findObject.Forward = true;
                findObject.Wrap = Word.WdFindWrap.wdFindContinue;
                findObject.MatchWholeWord = false;
                findObject.MatchCase = false;

                while (findObject.Execute())
                {
                    Word.Range foundRange = doc.Range(findObject.Parent.Start, findObject.Parent.End);
                    string foundText = foundRange.Text;
                    
                    if (foundText == placeholder)
                    {
                        exactMatches++;
                    }
                    else
                    {
                        partialMatches++;
                    }
                    
                    // 继续搜索
                    if (foundRange.End < doc.Content.End)
                    {
                        searchRange = doc.Range(foundRange.End, doc.Content.End);
                        findObject = searchRange.Find;
                        findObject.ClearFormatting();
                        findObject.Text = placeholder;
                        findObject.Forward = true;
                        findObject.Wrap = Word.WdFindWrap.wdFindStop;
                        findObject.MatchWholeWord = false;
                        findObject.MatchCase = false;
                    }
                    else
                    {
                        break;
                    }
                }
                
                return $"占位符 {placeholder}: 精确匹配 {exactMatches} 个，部分匹配 {partialMatches} 个";
            }
            catch (Exception ex)
            {
                return $"测试失败：{ex.Message}";
            }
        }
        
        /// <summary>
        /// 安全的占位符查找方法
        /// </summary>
        /// <param name="doc">Word文档</param>
        /// <param name="placeholder">要查找的占位符</param>
        /// <returns>找到的精确匹配范围列表</returns>
        public static System.Collections.Generic.List<Word.Range> FindExactPlaceholders(Word.Document doc, string placeholder)
        {
            var exactMatches = new System.Collections.Generic.List<Word.Range>();
            
            try
            {
                Word.Range searchRange = doc.Content;
                Word.Find findObject = searchRange.Find;
                findObject.ClearFormatting();
                findObject.Text = placeholder;
                findObject.Forward = true;
                findObject.Wrap = Word.WdFindWrap.wdFindContinue;
                findObject.MatchWholeWord = false;
                findObject.MatchCase = false;

                while (findObject.Execute())
                {
                    Word.Range foundRange = doc.Range(findObject.Parent.Start, findObject.Parent.End);
                    string foundText = foundRange.Text;
                    
                    // 只添加完全匹配的范围
                    if (foundText == placeholder)
                    {
                        exactMatches.Add(foundRange.Duplicate);
                    }
                    
                    // 继续搜索
                    if (foundRange.End < doc.Content.End)
                    {
                        searchRange = doc.Range(foundRange.End, doc.Content.End);
                        findObject = searchRange.Find;
                        findObject.ClearFormatting();
                        findObject.Text = placeholder;
                        findObject.Forward = true;
                        findObject.Wrap = Word.WdFindWrap.wdFindStop;
                        findObject.MatchWholeWord = false;
                        findObject.MatchCase = false;
                    }
                    else
                    {
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"查找占位符失败：{ex.Message}");
            }
            
            return exactMatches;
        }
    }
    
    /// <summary>
    /// 占位符匹配问题的解决方案说明
    /// </summary>
    public static class PlaceholderMatchingSolution
    {
        public const string PROBLEM_DESCRIPTION = @"
        问题描述：
        当文档中存在相似文本时，Word的Find功能可能匹配到错误位置。
        例如：
        - 文档内容：""国网四平供电公司""提供【国网四平供电公司】
        - 占位符：【国网四平供电公司】
        - 错误结果：程序可能匹配到引号内的普通文本，而不是占位符
        ";
        
        public const string SOLUTION_DESCRIPTION = @"
        解决方案：
        1. 在Find.Execute()后，验证找到的文本是否完全匹配占位符
        2. 使用 foundText == placeholder 进行精确比较
        3. 如果不匹配，继续搜索下一个位置
        4. 确保搜索范围正确更新，避免无限循环
        ";
        
        public const string KEY_CODE_CHANGES = @"
        关键代码改进：
        
        // 验证找到的文本是否完全匹配占位符
        string foundText = foundRange.Text;
        if (foundText != placeholder)
        {
            // 继续搜索下一个位置
            continue;
        }
        
        // 设置搜索参数确保一致性
        findObject.MatchWholeWord = false;
        findObject.MatchCase = false;
        ";
    }
}
