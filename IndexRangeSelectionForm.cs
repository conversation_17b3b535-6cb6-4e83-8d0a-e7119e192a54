using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Excel = Microsoft.Office.Interop.Excel;

namespace ExcelToWord
{
    /// <summary>
    /// 索引范围选择窗体 - 用于选择多行多列的索引范围
    /// </summary>
    public partial class IndexRangeSelectionForm : Form
    {
        private string filePath;
        private string sheetName;
        private DataGridView dgvPreview;
        private TextBox txtStartRow, txtStartColumn, txtEndRow, txtEndColumn;
        private Button btnConfirmRange, btnCancelRange, btnOK, btnCancel;
        private bool isDragging = false;
        private int dragStartRow = -1, dragStartColumn = -1;
        private Label lblStartRow, lblStartColumn, lblEndRow, lblEndColumn;
        
        public int StartRow { get; private set; }
        public int StartColumn { get; private set; }
        public int EndRow { get; private set; }
        public int EndColumn { get; private set; }
        public List<string> SelectedFieldNames { get; private set; }
        public List<IndexFieldInfo> SelectedFieldInfos { get; private set; }

        public IndexRangeSelectionForm(string filePath, string sheetName)
        {
            this.filePath = filePath;
            this.sheetName = sheetName;
            this.SelectedFieldNames = new List<string>();
            this.SelectedFieldInfos = new List<IndexFieldInfo>();

            // 初始化默认值
            StartRow = 1;
            StartColumn = 1;
            EndRow = 2;
            EndColumn = 5;

            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = "选择索引范围";
            this.Size = new Size(1000, 700);
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MinimumSize = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;

            // 范围输入控件
            lblStartRow = new Label { Text = "开始行:", Location = new Point(20, 25), Size = new Size(60, 23) };
            txtStartRow = new TextBox { Location = new Point(85, 22), Size = new Size(80, 23), Text = "1" };
            
            lblStartColumn = new Label { Text = "开始列:", Location = new Point(180, 25), Size = new Size(60, 23) };
            txtStartColumn = new TextBox { Location = new Point(245, 22), Size = new Size(80, 23), Text = "1" };
            
            lblEndRow = new Label { Text = "结束行:", Location = new Point(340, 25), Size = new Size(60, 23) };
            txtEndRow = new TextBox { Location = new Point(405, 22), Size = new Size(80, 23), Text = "2" };
            
            lblEndColumn = new Label { Text = "结束列:", Location = new Point(500, 25), Size = new Size(60, 23) };
            txtEndColumn = new TextBox { Location = new Point(565, 22), Size = new Size(80, 23), Text = "5" };

            // 确认范围按钮
            btnConfirmRange = new Button
            {
                Text = "确认范围",
                Location = new Point(660, 22),
                Size = new Size(80, 23),
                Font = new Font("微软雅黑", 9)
            };
            btnConfirmRange.Click += BtnConfirmRange_Click;

            // 取消按钮
            btnCancelRange = new Button
            {
                Text = "取消",
                Location = new Point(760, 22),
                Size = new Size(60, 23),
                Font = new Font("微软雅黑", 9)
            };
            btnCancelRange.Click += BtnCancelRange_Click;

            // 数据预览表格
            dgvPreview = new DataGridView
            {
                Location = new Point(20, 60),
                Size = new Size(940, 500),
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.CellSelect,
                DefaultCellStyle = new DataGridViewCellStyle { Font = new Font("微软雅黑", 9) },
                RowHeadersWidth = 60
            };

            // 添加鼠标事件处理
            dgvPreview.CellMouseDown += DgvPreview_CellMouseDown;
            dgvPreview.CellMouseMove += DgvPreview_CellMouseMove;
            dgvPreview.CellMouseUp += DgvPreview_CellMouseUp;

            // 底部按钮
            btnOK = new Button
            {
                Text = "确定",
                Location = new Point(800, 580),
                Size = new Size(75, 30),
                Font = new Font("微软雅黑", 9)
            };
            btnOK.Click += BtnOK_Click;

            btnCancel = new Button
            {
                Text = "取消",
                Location = new Point(885, 580),
                Size = new Size(75, 30),
                DialogResult = DialogResult.Cancel,
                Font = new Font("微软雅黑", 9)
            };

            // 添加控件到窗体
            this.Controls.AddRange(new Control[] {
                lblStartRow, txtStartRow, lblStartColumn, txtStartColumn,
                lblEndRow, txtEndRow, lblEndColumn, txtEndColumn,
                btnConfirmRange, btnCancelRange, dgvPreview, btnOK, btnCancel
            });
        }

        private void LoadData()
        {
            Excel.Application excelApp = null;
            Excel.Workbook workbook = null;

            try
            {
                excelApp = new Excel.Application();
                excelApp.Visible = false;
                excelApp.ScreenUpdating = false;
                excelApp.DisplayAlerts = false;

                workbook = excelApp.Workbooks.Open(filePath, ReadOnly: true);
                Excel.Worksheet worksheet = workbook.Worksheets[sheetName];

                // 读取数据并填充到DataGridView
                int maxRows = Math.Min(50, worksheet.UsedRange.Rows.Count);
                int maxCols = Math.Min(15, worksheet.UsedRange.Columns.Count);

                // 设置列
                dgvPreview.Columns.Clear();
                for (int col = 1; col <= maxCols; col++)
                {
                    string columnName = GetColumnLetter(col);
                    dgvPreview.Columns.Add(columnName, columnName);
                    dgvPreview.Columns[col - 1].Width = 100;
                }

                // 填充数据
                for (int row = 1; row <= maxRows; row++)
                {
                    object[] rowData = new object[maxCols];
                    for (int col = 1; col <= maxCols; col++)
                    {
                        Excel.Range cell = worksheet.Cells[row, col];
                        rowData[col - 1] = GetCellDisplayValue(cell) ?? "";
                    }
                    dgvPreview.Rows.Add(rowData);
                }

                // 设置行标题
                for (int i = 0; i < dgvPreview.Rows.Count; i++)
                {
                    dgvPreview.Rows[i].HeaderCell.Value = (i + 1).ToString();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载数据时出错：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                workbook?.Close(false);
                excelApp?.Quit();
                if (workbook != null) System.Runtime.InteropServices.Marshal.ReleaseComObject(workbook);
                if (excelApp != null) System.Runtime.InteropServices.Marshal.ReleaseComObject(excelApp);
            }
        }

        private void BtnConfirmRange_Click(object sender, EventArgs e)
        {
            if (ValidateRange())
            {
                int startRow = int.Parse(txtStartRow.Text);
                int startColumn = int.Parse(txtStartColumn.Text);
                int endRow = int.Parse(txtEndRow.Text);
                int endColumn = int.Parse(txtEndColumn.Text);

                StartRow = startRow;
                StartColumn = startColumn;
                EndRow = endRow;
                EndColumn = endColumn;

                UpdateFieldNames();
                UpdateSelectionFromTextBoxes();

                MessageBox.Show($"范围确认成功！已识别到 {SelectedFieldNames.Count} 个字段：\n{string.Join(", ", SelectedFieldNames)}",
                               "范围确认", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            // 确保在关闭前更新属性值
            if (ValidateRange())
            {
                StartRow = int.Parse(txtStartRow.Text);
                StartColumn = int.Parse(txtStartColumn.Text);
                EndRow = int.Parse(txtEndRow.Text);
                EndColumn = int.Parse(txtEndColumn.Text);

                UpdateFieldNames();

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void BtnCancelRange_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private bool ValidateRange()
        {
            try
            {
                int startRow = int.Parse(txtStartRow.Text);
                int startColumn = int.Parse(txtStartColumn.Text);
                int endRow = int.Parse(txtEndRow.Text);
                int endColumn = int.Parse(txtEndColumn.Text);

                if (startRow < 1 || startColumn < 1 || endRow < startRow || endColumn < startColumn)
                {
                    MessageBox.Show("请输入有效的范围值！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

                return true;
            }
            catch
            {
                MessageBox.Show("请输入有效的数字！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }
        }

        private void UpdateFieldNames()
        {
            SelectedFieldNames.Clear();
            SelectedFieldInfos.Clear();

            try
            {
                int startRow = int.Parse(txtStartRow.Text);
                int startColumn = int.Parse(txtStartColumn.Text);
                int endRow = int.Parse(txtEndRow.Text);
                int endColumn = int.Parse(txtEndColumn.Text);

                System.Diagnostics.Debug.WriteLine($"=== 构建详细字段信息 ===");
                System.Diagnostics.Debug.WriteLine($"索引范围：行{startRow}-{endRow}，列{startColumn}-{endColumn}");

                // 对于每一列，构建完整的字段信息
                for (int col = startColumn - 1; col <= endColumn - 1 && col < dgvPreview.Columns.Count; col++)
                {
                    var fieldInfo = new IndexFieldInfo();
                    fieldInfo.Column = col + 1; // 转换为1基索引

                    // 构建该列的层级结构（从上到下）
                    for (int row = startRow - 1; row <= endRow - 1 && row < dgvPreview.Rows.Count; row++)
                    {
                        if (row >= 0 && col >= 0)
                        {
                            string cellValue = dgvPreview.Rows[row].Cells[col].Value?.ToString()?.Trim();
                            if (!string.IsNullOrEmpty(cellValue))
                            {
                                fieldInfo.Hierarchy.Add(cellValue);
                                fieldInfo.Row = row + 1; // 记录最后一个非空字段的行号
                            }
                        }
                    }

                    // 确定字段名称（使用最底层的非空字段）
                    if (fieldInfo.Hierarchy.Count > 0)
                    {
                        fieldInfo.FieldName = fieldInfo.Hierarchy.Last();

                        // 构建唯一标识符（包含位置和层级信息）
                        fieldInfo.UniqueKey = $"Col{fieldInfo.Column}_Row{fieldInfo.Row}_{fieldInfo.FieldName}";

                        System.Diagnostics.Debug.WriteLine($"列{fieldInfo.Column}: 字段='{fieldInfo.FieldName}', 层级=[{string.Join(" -> ", fieldInfo.Hierarchy)}], 唯一键='{fieldInfo.UniqueKey}'");
                    }
                    else
                    {
                        // 空字段处理
                        fieldInfo.FieldName = "";
                        fieldInfo.UniqueKey = $"Col{fieldInfo.Column}_Empty";
                        System.Diagnostics.Debug.WriteLine($"列{fieldInfo.Column}: 空字段");
                    }

                    SelectedFieldInfos.Add(fieldInfo);
                    SelectedFieldNames.Add(fieldInfo.FieldName);
                }

                // 移除末尾的空字符串，但保留中间的空字符串
                while (SelectedFieldNames.Count > 0 && string.IsNullOrEmpty(SelectedFieldNames[SelectedFieldNames.Count - 1]))
                {
                    SelectedFieldNames.RemoveAt(SelectedFieldNames.Count - 1);
                    SelectedFieldInfos.RemoveAt(SelectedFieldInfos.Count - 1);
                }

                System.Diagnostics.Debug.WriteLine($"最终识别的字段: {string.Join(", ", SelectedFieldNames)}");
                System.Diagnostics.Debug.WriteLine($"字段详细信息: {string.Join(", ", SelectedFieldInfos)}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新字段名称时出错：{ex.Message}");
            }
        }

        private void DgvPreview_CellMouseDown(object sender, DataGridViewCellMouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left && e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                isDragging = true;
                dragStartRow = e.RowIndex;
                dragStartColumn = e.ColumnIndex;

                // 更新文本框
                txtStartRow.Text = (e.RowIndex + 1).ToString();
                txtStartColumn.Text = (e.ColumnIndex + 1).ToString();
                txtEndRow.Text = (e.RowIndex + 1).ToString();
                txtEndColumn.Text = (e.ColumnIndex + 1).ToString();

                dgvPreview.ClearSelection();
                dgvPreview.Rows[e.RowIndex].Cells[e.ColumnIndex].Selected = true;
            }
        }

        private void DgvPreview_CellMouseMove(object sender, DataGridViewCellMouseEventArgs e)
        {
            if (isDragging && e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                // 计算选择范围
                int startRow = Math.Min(dragStartRow, e.RowIndex);
                int endRow = Math.Max(dragStartRow, e.RowIndex);
                int startColumn = Math.Min(dragStartColumn, e.ColumnIndex);
                int endColumn = Math.Max(dragStartColumn, e.ColumnIndex);

                // 更新文本框
                txtStartRow.Text = (startRow + 1).ToString();
                txtStartColumn.Text = (startColumn + 1).ToString();
                txtEndRow.Text = (endRow + 1).ToString();
                txtEndColumn.Text = (endColumn + 1).ToString();

                // 更新选择
                dgvPreview.ClearSelection();
                for (int row = startRow; row <= endRow; row++)
                {
                    for (int col = startColumn; col <= endColumn; col++)
                    {
                        if (row < dgvPreview.Rows.Count && col < dgvPreview.Columns.Count)
                        {
                            dgvPreview.Rows[row].Cells[col].Selected = true;
                        }
                    }
                }
            }
        }

        private void DgvPreview_CellMouseUp(object sender, DataGridViewCellMouseEventArgs e)
        {
            if (isDragging)
            {
                isDragging = false;
                // 拖拽结束后自动确认范围
                if (ValidateRange())
                {
                    UpdateFieldNames();
                }
            }
        }

        private void UpdateSelectionFromTextBoxes()
        {
            try
            {
                int startRow = int.Parse(txtStartRow.Text) - 1;
                int startColumn = int.Parse(txtStartColumn.Text) - 1;
                int endRow = int.Parse(txtEndRow.Text) - 1;
                int endColumn = int.Parse(txtEndColumn.Text) - 1;

                dgvPreview.ClearSelection();

                for (int row = startRow; row <= endRow && row < dgvPreview.Rows.Count && row >= 0; row++)
                {
                    for (int col = startColumn; col <= endColumn && col < dgvPreview.Columns.Count && col >= 0; col++)
                    {
                        dgvPreview.Rows[row].Cells[col].Selected = true;
                    }
                }

                System.Diagnostics.Debug.WriteLine($"选择范围：行{startRow + 1}-{endRow + 1}，列{startColumn + 1}-{endColumn + 1}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新选择范围时出错：{ex.Message}");
            }
        }

        private string GetColumnLetter(int columnNumber)
        {
            string columnName = "";
            while (columnNumber > 0)
            {
                int modulo = (columnNumber - 1) % 26;
                columnName = Convert.ToChar('A' + modulo) + columnName;
                columnNumber = (columnNumber - modulo) / 26;
            }
            return columnName;
        }

        private string GetCellDisplayValue(Excel.Range cell)
        {
            try
            {
                if (cell?.Value != null)
                {
                    return cell.Value.ToString();
                }
                return "";
            }
            catch
            {
                return "";
            }
        }
    }


}
