using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace ExcelToWord
{
    /// <summary>
    /// 西安汇总配置窗体
    /// </summary>
    public partial class XianSummaryConfigForm : Form
    {
        private XianSummaryConfig config;
        private Button btnSelectTemplate;
        private Button btnSelectSources;
        private Button btnAddIndex;
        private Button btnRemoveIndex;
        private Button btnStartSummary;
        private Button btnOK;
        private Button btnCancel;
        private TextBox txtTemplate;
        private ListBox lstSources;
        private DataGridView dgvIndexes;
        private Label lblTemplate;
        private Label lblSources;
        private Label lblIndexes;

        public XianSummaryConfigForm()
        {
            InitializeComponent();
            config = new XianSummaryConfig();
            SetupDataGridView();
        }

        private void InitializeComponent()
        {
            this.Text = "西安汇总配置";
            this.Size = new Size(1400, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MaximizeBox = true;
            this.MinimizeBox = true;
            this.MinimumSize = new Size(1200, 600);

            // 添加窗口大小改变事件处理
            this.Resize += XianSummaryConfigForm_Resize;

            // 模板文件选择
            lblTemplate = new Label
            {
                Text = "模板文件:",
                Location = new Point(20, 20),
                Size = new Size(80, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };

            txtTemplate = new TextBox
            {
                Location = new Point(110, 20),
                Size = new Size(1000, 23),
                ReadOnly = true,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            btnSelectTemplate = new Button
            {
                Text = "选择模板",
                Location = new Point(1120, 20),
                Size = new Size(80, 23),
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };
            btnSelectTemplate.Click += BtnSelectTemplate_Click;

            // 源文件选择
            lblSources = new Label
            {
                Text = "单体表文件:",
                Location = new Point(20, 60),
                Size = new Size(80, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };

            lstSources = new ListBox
            {
                Location = new Point(110, 60),
                Size = new Size(1000, 100),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            btnSelectSources = new Button
            {
                Text = "选择单体表",
                Location = new Point(1120, 60),
                Size = new Size(80, 23),
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };
            btnSelectSources.Click += BtnSelectSources_Click;

            // 索引配置
            lblIndexes = new Label
            {
                Text = "索引配置:",
                Location = new Point(20, 180),
                Size = new Size(80, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };

            dgvIndexes = new DataGridView
            {
                Location = new Point(110, 180),
                Size = new Size(1050, 400),
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None,
                ScrollBars = ScrollBars.Both,
                DefaultCellStyle = new DataGridViewCellStyle { Font = new Font("微软雅黑", 9) },
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle { Font = new Font("微软雅黑", 9, FontStyle.Bold) },
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right
            };

            btnAddIndex = new Button
            {
                Text = "添加索引",
                Location = new Point(1170, 180),
                Size = new Size(80, 23)
            };
            btnAddIndex.Click += BtnAddIndex_Click;

            btnRemoveIndex = new Button
            {
                Text = "删除索引",
                Location = new Point(1170, 210),
                Size = new Size(80, 23)
            };
            btnRemoveIndex.Click += BtnRemoveIndex_Click;

            // 开始汇总按钮（显眼位置）
            btnStartSummary = new Button
            {
                Text = "开始汇总",
                Location = new Point(1170, 250),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(0, 120, 215),
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 10, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };
            btnStartSummary.FlatAppearance.BorderSize = 0;
            btnStartSummary.Click += BtnStartSummary_Click;

            // 确定取消按钮
            btnOK = new Button
            {
                Text = "确定",
                Location = new Point(1190, 620),
                Size = new Size(75, 30),
                DialogResult = DialogResult.OK
            };
            btnOK.Click += BtnOK_Click;

            btnCancel = new Button
            {
                Text = "取消",
                Location = new Point(1275, 620),
                Size = new Size(75, 30),
                DialogResult = DialogResult.Cancel
            };

            // 添加控件到窗体
            this.Controls.AddRange(new Control[] {
                lblTemplate, txtTemplate, btnSelectTemplate,
                lblSources, lstSources, btnSelectSources,
                lblIndexes, dgvIndexes, btnAddIndex, btnRemoveIndex, btnStartSummary,
                btnOK, btnCancel
            });
        }

        private void SetupDataGridView()
        {
            dgvIndexes.Columns.Clear();

            // 目标附表列（下拉选择）
            DataGridViewComboBoxColumn targetSheetColumn = new DataGridViewComboBoxColumn();
            targetSheetColumn.Name = "TargetSheetName";
            targetSheetColumn.HeaderText = "目标附表";
            targetSheetColumn.Width = 85;
            dgvIndexes.Columns.Add(targetSheetColumn);

            // 源附表列（下拉选择）
            DataGridViewComboBoxColumn sourceSheetColumn = new DataGridViewComboBoxColumn();
            sourceSheetColumn.Name = "SourceSheetName";
            sourceSheetColumn.HeaderText = "源附表";
            sourceSheetColumn.Width = 85;
            dgvIndexes.Columns.Add(sourceSheetColumn);

            // 索引范围列
            dgvIndexes.Columns.Add("IndexStartRow", "索引起始行");
            dgvIndexes.Columns["IndexStartRow"].Width = 75;
            dgvIndexes.Columns["IndexStartRow"].ReadOnly = false;

            dgvIndexes.Columns.Add("IndexStartColumn", "索引起始列");
            dgvIndexes.Columns["IndexStartColumn"].Width = 75;
            dgvIndexes.Columns["IndexStartColumn"].ReadOnly = false;

            dgvIndexes.Columns.Add("IndexEndRow", "索引结束行");
            dgvIndexes.Columns["IndexEndRow"].Width = 75;
            dgvIndexes.Columns["IndexEndRow"].ReadOnly = false;

            dgvIndexes.Columns.Add("IndexEndColumn", "索引结束列");
            dgvIndexes.Columns["IndexEndColumn"].Width = 75;
            dgvIndexes.Columns["IndexEndColumn"].ReadOnly = false;

            // 抓取范围列
            dgvIndexes.Columns.Add("StartRow", "抓取起始行");
            dgvIndexes.Columns["StartRow"].Width = 75;
            dgvIndexes.Columns["StartRow"].ReadOnly = false;

            dgvIndexes.Columns.Add("StartColumn", "抓取起始列");
            dgvIndexes.Columns["StartColumn"].Width = 75;
            dgvIndexes.Columns["StartColumn"].ReadOnly = false;

            dgvIndexes.Columns.Add("EndRow", "抓取结束行");
            dgvIndexes.Columns["EndRow"].Width = 75;
            dgvIndexes.Columns["EndRow"].ReadOnly = false;

            dgvIndexes.Columns.Add("EndColumn", "抓取结束列");
            dgvIndexes.Columns["EndColumn"].Width = 75;
            dgvIndexes.Columns["EndColumn"].ReadOnly = false;

            // 识别的字段列（只读）
            dgvIndexes.Columns.Add("FieldNames", "识别的字段");
            dgvIndexes.Columns["FieldNames"].Width = 120;
            dgvIndexes.Columns["FieldNames"].ReadOnly = true;

            // 插入位置列
            dgvIndexes.Columns.Add("InsertRow", "插入行");
            dgvIndexes.Columns["InsertRow"].Width = 60;
            dgvIndexes.Columns["InsertRow"].ReadOnly = false;

            dgvIndexes.Columns.Add("InsertColumn", "插入列");
            dgvIndexes.Columns["InsertColumn"].Width = 60;
            dgvIndexes.Columns["InsertColumn"].ReadOnly = false;

            // 选择索引范围按钮列
            DataGridViewButtonColumn selectIndexColumn = new DataGridViewButtonColumn();
            selectIndexColumn.Name = "SelectIndex";
            selectIndexColumn.HeaderText = "选择索引范围";
            selectIndexColumn.Text = "选择索引范围";
            selectIndexColumn.UseColumnTextForButtonValue = true;
            selectIndexColumn.Width = 100;
            dgvIndexes.Columns.Add(selectIndexColumn);

            // 选择抓取范围按钮列
            DataGridViewButtonColumn selectRangeColumn = new DataGridViewButtonColumn();
            selectRangeColumn.Name = "SelectRange";
            selectRangeColumn.HeaderText = "选择抓取范围";
            selectRangeColumn.Text = "选择抓取范围";
            selectRangeColumn.UseColumnTextForButtonValue = true;
            selectRangeColumn.Width = 100;
            dgvIndexes.Columns.Add(selectRangeColumn);

            // 添加单元格点击事件处理
            dgvIndexes.CellClick += DgvIndexes_CellClick;

            // 初始化时调整列宽
            this.Load += (s, e) => AutoResizeDataGridViewColumns();
        }

        private void BtnSelectTemplate_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog dialog = new OpenFileDialog())
            {
                dialog.Filter = "Excel文件|*.xls;*.xlsx;*.xlsm";
                dialog.Title = "选择模板文件";

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    txtTemplate.Text = dialog.FileName;
                    config.TemplateFilePath = dialog.FileName;

                    // 更新附表下拉列表
                    UpdateSheetComboBox(dialog.FileName);
                }
            }
        }

        /// <summary>
        /// 更新附表下拉列表
        /// </summary>
        /// <param name="filePath">Excel文件路径</param>
        private void UpdateSheetComboBox(string filePath)
        {
            try
            {
                Microsoft.Office.Interop.Excel.Application excelApp = new Microsoft.Office.Interop.Excel.Application();
                Microsoft.Office.Interop.Excel.Workbook workbook = null;

                try
                {
                    workbook = excelApp.Workbooks.Open(filePath);

                    // 获取所有工作表名称
                    List<string> sheetNames = new List<string>();
                    foreach (Microsoft.Office.Interop.Excel.Worksheet sheet in workbook.Worksheets)
                    {
                        sheetNames.Add(sheet.Name);
                    }

                    // 更新DataGridView中的目标附表下拉列表
                    if (dgvIndexes.Columns["TargetSheetName"] is DataGridViewComboBoxColumn targetSheetColumn)
                    {
                        targetSheetColumn.Items.Clear();
                        foreach (string sheetName in sheetNames)
                        {
                            targetSheetColumn.Items.Add(sheetName);
                        }
                    }

                    // 更新源附表下拉列表（从单体表文件中获取）
                    UpdateSourceSheetDropdown();
                }
                finally
                {
                    if (workbook != null)
                    {
                        workbook.Close(false);
                    }
                    excelApp.Quit();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"读取模板文件工作表失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 更新源附表下拉列表（从单体表文件中获取）
        /// </summary>
        private void UpdateSourceSheetDropdown()
        {
            if (dgvIndexes.Columns["SourceSheetName"] is DataGridViewComboBoxColumn sourceSheetColumn)
            {
                sourceSheetColumn.Items.Clear();

                // 如果有单体表文件，从第一个文件中获取工作表名称
                if (config.SourceFilePaths.Count > 0)
                {
                    try
                    {
                        string sourceFilePath = config.SourceFilePaths[0];
                        Microsoft.Office.Interop.Excel.Application excelApp = Globals.ThisAddIn.Application;
                        Microsoft.Office.Interop.Excel.Workbook sourceWorkbook = null;

                        try
                        {
                            // 打开单体表文件获取工作表名称
                            sourceWorkbook = excelApp.Workbooks.Open(sourceFilePath, ReadOnly: true);

                            foreach (Microsoft.Office.Interop.Excel.Worksheet sheet in sourceWorkbook.Sheets)
                            {
                                sourceSheetColumn.Items.Add(sheet.Name);
                            }
                        }
                        finally
                        {
                            if (sourceWorkbook != null)
                            {
                                sourceWorkbook.Close(false);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"获取源文件工作表名称失败：{ex.Message}");
                        MessageBox.Show($"获取源文件工作表名称失败：{ex.Message}", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
            }
        }

        /// <summary>
        /// 窗口大小改变事件处理
        /// </summary>
        private void XianSummaryConfigForm_Resize(object sender, EventArgs e)
        {
            // 自动调整DataGridView列宽
            AutoResizeDataGridViewColumns();
        }

        /// <summary>
        /// 自动调整DataGridView列宽
        /// </summary>
        private void AutoResizeDataGridViewColumns()
        {
            if (dgvIndexes != null && dgvIndexes.Columns.Count > 0)
            {
                try
                {
                    // 计算可用宽度（总宽度减去滚动条和边距）
                    int availableWidth = dgvIndexes.Width - 50; // 预留滚动条和边距空间
                    int totalColumns = dgvIndexes.Columns.Count;

                    // 为不同类型的列分配不同的宽度
                    int baseWidth = availableWidth / totalColumns;

                    foreach (DataGridViewColumn column in dgvIndexes.Columns)
                    {
                        switch (column.Name)
                        {
                            case "TargetSheetName":
                            case "SourceSheetName":
                                column.Width = Math.Max(100, baseWidth);
                                break;
                            case "IndexRange":
                            case "DataRange":
                                column.Width = Math.Max(120, baseWidth + 20);
                                break;
                            case "FieldNames":
                                column.Width = Math.Max(200, baseWidth + 50); // 字段名列需要更宽
                                break;
                            case "InsertRow":
                            case "InsertColumn":
                                column.Width = Math.Max(60, baseWidth - 20);
                                break;
                            default:
                                column.Width = Math.Max(80, baseWidth);
                                break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"自动调整列宽失败：{ex.Message}");
                }
            }
        }

        /// <summary>
        /// 处理DataGridView单元格点击事件
        /// </summary>
        private void DgvIndexes_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex < 0) return;

            string columnName = dgvIndexes.Columns[e.ColumnIndex].Name;
            DataGridViewRow currentRow = dgvIndexes.Rows[e.RowIndex];

            if (columnName == "SelectIndex")
            {
                // 选择索引范围
                SelectIndexRange(currentRow);
            }
            else if (columnName == "SelectRange")
            {
                // 选择抓取范围
                SelectDataRange(currentRow);
            }
        }

        private void BtnSelectSources_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog dialog = new OpenFileDialog())
            {
                dialog.Filter = "Excel文件|*.xls;*.xlsx;*.xlsm";
                dialog.Title = "选择单体表文件";
                dialog.Multiselect = true;
                
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    lstSources.Items.Clear();
                    config.SourceFilePaths.Clear();
                    
                    foreach (string fileName in dialog.FileNames)
                    {
                        lstSources.Items.Add(System.IO.Path.GetFileName(fileName));
                        config.SourceFilePaths.Add(fileName);
                    }

                    // 更新源附表下拉列表
                    UpdateSourceSheetDropdown();
                }
            }
        }

        /// <summary>
        /// 选择索引范围
        /// </summary>
        private void SelectIndexRange(DataGridViewRow row)
        {
            if (string.IsNullOrEmpty(config.TemplateFilePath))
            {
                MessageBox.Show("请先选择模板文件！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            string targetSheetName = row.Cells["TargetSheetName"].Value?.ToString();
            if (string.IsNullOrEmpty(targetSheetName))
            {
                MessageBox.Show("请先选择目标附表！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            using (IndexRangeSelectionForm form = new IndexRangeSelectionForm(config.TemplateFilePath, targetSheetName))
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    // 更新索引范围信息到DataGridView中
                    row.Cells["IndexStartRow"].Value = form.StartRow;
                    row.Cells["IndexStartColumn"].Value = form.StartColumn;
                    row.Cells["IndexEndRow"].Value = form.EndRow;
                    row.Cells["IndexEndColumn"].Value = form.EndColumn;

                    // 更新字段名称显示
                    row.Cells["FieldNames"].Value = string.Join(", ", form.SelectedFieldNames);

                    // 存储详细字段信息到Tag中
                    row.Tag = new
                    {
                        FieldNames = form.SelectedFieldNames,
                        FieldInfos = form.SelectedFieldInfos, // 新增：详细字段信息
                        ExistingStartRow = row.Cells["StartRow"].Value,
                        ExistingStartColumn = row.Cells["StartColumn"].Value,
                        ExistingEndRow = row.Cells["EndRow"].Value,
                        ExistingEndColumn = row.Cells["EndColumn"].Value
                    };

                    MessageBox.Show($"索引范围设置成功！\n范围：行{form.StartRow}-{form.EndRow}，列{form.StartColumn}-{form.EndColumn}\n识别到 {form.SelectedFieldNames.Count} 个字段：{string.Join(", ", form.SelectedFieldNames)}",
                                   "确认", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        /// <summary>
        /// 选择抓取范围
        /// </summary>
        private void SelectDataRange(DataGridViewRow row)
        {
            if (config.SourceFilePaths.Count == 0)
            {
                MessageBox.Show("请先选择单体表文件！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            string sourceSheetName = row.Cells["SourceSheetName"].Value?.ToString();
            if (string.IsNullOrEmpty(sourceSheetName))
            {
                MessageBox.Show("请先选择源附表！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 使用第一个源文件进行范围选择
            string sourceFilePath = config.SourceFilePaths[0];
            using (RangeSelectionForm form = new RangeSelectionForm(sourceFilePath, sourceSheetName))
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    // 更新抓取范围信息到DataGridView中
                    row.Cells["StartRow"].Value = form.StartRow;
                    row.Cells["StartColumn"].Value = form.StartColumn;
                    row.Cells["EndRow"].Value = form.EndRow;
                    row.Cells["EndColumn"].Value = form.EndColumn;

                    // 更新Tag中的字段名称信息
                    var existingTag = row.Tag as dynamic;
                    List<IndexFieldInfo> existingFieldInfos = new List<IndexFieldInfo>();
                    try
                    {
                        existingFieldInfos = existingTag?.FieldInfos ?? new List<IndexFieldInfo>();
                    }
                    catch (Microsoft.CSharp.RuntimeBinder.RuntimeBinderException)
                    {
                        // 旧的Tag对象可能没有FieldInfos属性，使用空列表
                        existingFieldInfos = new List<IndexFieldInfo>();
                    }

                    row.Tag = new
                    {
                        FieldNames = existingTag?.FieldNames ?? new List<string>(),
                        FieldInfos = existingFieldInfos
                    };

                    MessageBox.Show($"抓取范围设置成功！\n范围：行{form.StartRow}-{form.EndRow}，列{form.StartColumn}-{form.EndColumn}",
                                   "确认", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        private void BtnAddIndex_Click(object sender, EventArgs e)
        {
            dgvIndexes.Rows.Add("", "", "1", "1", "2", "10", "1", "1", "100", "20", "", "3", "1", "选择索引范围", "选择抓取范围");
        }

        private void BtnRemoveIndex_Click(object sender, EventArgs e)
        {
            if (dgvIndexes.SelectedRows.Count > 0)
            {
                dgvIndexes.Rows.RemoveAt(dgvIndexes.SelectedRows[0].Index);
            }
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            // 验证配置
            if (string.IsNullOrEmpty(config.TemplateFilePath))
            {
                MessageBox.Show("请选择模板文件！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (config.SourceFilePaths.Count == 0)
            {
                MessageBox.Show("请选择至少一个单体表文件！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 收集索引配置
            config.IndexConfigs.Clear();
            foreach (DataGridViewRow row in dgvIndexes.Rows)
            {
                if (row.IsNewRow) continue;

                var indexConfig = new XianSummaryIndexConfig();

                // 解析附表名称
                indexConfig.TargetSheetName = row.Cells["TargetSheetName"].Value?.ToString() ?? "";
                indexConfig.SourceSheetName = row.Cells["SourceSheetName"].Value?.ToString() ?? "";

                // 兼容性设置
                indexConfig.SheetName = indexConfig.TargetSheetName;

                // 解析行号和列号
                if (int.TryParse(row.Cells["InsertRow"].Value?.ToString(), out int insertRow))
                {
                    indexConfig.InsertRow = insertRow;
                }

                if (int.TryParse(row.Cells["InsertColumn"].Value?.ToString(), out int insertColumn))
                {
                    indexConfig.InsertColumn = insertColumn;
                }

                // 从DataGridView中读取范围信息
                if (int.TryParse(row.Cells["IndexStartRow"].Value?.ToString(), out int indexStartRow))
                    indexConfig.IndexStartRow = indexStartRow;
                if (int.TryParse(row.Cells["IndexStartColumn"].Value?.ToString(), out int indexStartColumn))
                    indexConfig.IndexStartColumn = indexStartColumn;
                if (int.TryParse(row.Cells["IndexEndRow"].Value?.ToString(), out int indexEndRow))
                    indexConfig.IndexEndRow = indexEndRow;
                if (int.TryParse(row.Cells["IndexEndColumn"].Value?.ToString(), out int indexEndColumn))
                    indexConfig.IndexEndColumn = indexEndColumn;

                if (int.TryParse(row.Cells["StartRow"].Value?.ToString(), out int startRow))
                    indexConfig.StartRow = startRow;
                if (int.TryParse(row.Cells["StartColumn"].Value?.ToString(), out int startColumn))
                    indexConfig.StartColumn = startColumn;
                if (int.TryParse(row.Cells["EndRow"].Value?.ToString(), out int endRow))
                    indexConfig.EndRow = endRow;
                if (int.TryParse(row.Cells["EndColumn"].Value?.ToString(), out int endColumn))
                    indexConfig.EndColumn = endColumn;

                // 从Tag中获取字段名称和详细信息
                if (row.Tag != null)
                {
                    dynamic tagData = row.Tag;
                    if (tagData.FieldNames != null)
                    {
                        indexConfig.FieldNames = new List<string>(tagData.FieldNames);
                    }
                    // 存储详细字段信息（如果有的话）
                    try
                    {
                        if (tagData.FieldInfos != null)
                        {
                            indexConfig.FieldInfos = new List<IndexFieldInfo>(tagData.FieldInfos);
                        }
                    }
                    catch (Microsoft.CSharp.RuntimeBinder.RuntimeBinderException)
                    {
                        // 旧的Tag对象可能没有FieldInfos属性，忽略这个错误
                        System.Diagnostics.Debug.WriteLine("Tag对象没有FieldInfos属性，使用默认值");
                    }
                }

                // 验证配置完整性
                if (!string.IsNullOrEmpty(indexConfig.TargetSheetName) &&
                    !string.IsNullOrEmpty(indexConfig.SourceSheetName) &&
                    indexConfig.FieldNames.Count > 0)
                {
                    config.IndexConfigs.Add(indexConfig);
                }
            }

            if (config.IndexConfigs.Count == 0)
            {
                MessageBox.Show("请至少配置一个完整的索引！\n请确保：\n1. 选择了目标附表和源附表\n2. 选择了索引范围并识别到字段\n3. 选择了抓取范围",
                               "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
        }

        public XianSummaryConfig GetConfiguration()
        {
            return config;
        }

        private void BtnStartSummary_Click(object sender, EventArgs e)
        {
            try
            {
                // 验证配置
                if (string.IsNullOrEmpty(config.TemplateFilePath))
                {
                    MessageBox.Show("请选择模板文件！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (config.SourceFilePaths.Count == 0)
                {
                    MessageBox.Show("请选择至少一个单体表文件！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 收集索引配置
                config.IndexConfigs.Clear();
                foreach (DataGridViewRow row in dgvIndexes.Rows)
                {
                    if (row.IsNewRow) continue;

                    var indexConfig = new XianSummaryIndexConfig();

                    // 解析附表名称
                    indexConfig.TargetSheetName = row.Cells["TargetSheetName"].Value?.ToString() ?? "";
                    indexConfig.SourceSheetName = row.Cells["SourceSheetName"].Value?.ToString() ?? "";

                    // 兼容性设置
                    indexConfig.SheetName = indexConfig.TargetSheetName;

                    // 解析行号和列号
                    if (int.TryParse(row.Cells["InsertRow"].Value?.ToString(), out int insertRow))
                    {
                        indexConfig.InsertRow = insertRow;
                    }
                    if (int.TryParse(row.Cells["InsertColumn"].Value?.ToString(), out int insertColumn))
                    {
                        indexConfig.InsertColumn = insertColumn;
                    }

                    // 从DataGridView中读取范围信息
                    if (int.TryParse(row.Cells["IndexStartRow"].Value?.ToString(), out int indexStartRow))
                        indexConfig.IndexStartRow = indexStartRow;
                    if (int.TryParse(row.Cells["IndexStartColumn"].Value?.ToString(), out int indexStartColumn))
                        indexConfig.IndexStartColumn = indexStartColumn;
                    if (int.TryParse(row.Cells["IndexEndRow"].Value?.ToString(), out int indexEndRow))
                        indexConfig.IndexEndRow = indexEndRow;
                    if (int.TryParse(row.Cells["IndexEndColumn"].Value?.ToString(), out int indexEndColumn))
                        indexConfig.IndexEndColumn = indexEndColumn;

                    if (int.TryParse(row.Cells["StartRow"].Value?.ToString(), out int startRow))
                        indexConfig.StartRow = startRow;
                    if (int.TryParse(row.Cells["StartColumn"].Value?.ToString(), out int startColumn))
                        indexConfig.StartColumn = startColumn;
                    if (int.TryParse(row.Cells["EndRow"].Value?.ToString(), out int endRow))
                        indexConfig.EndRow = endRow;
                    if (int.TryParse(row.Cells["EndColumn"].Value?.ToString(), out int endColumn))
                        indexConfig.EndColumn = endColumn;

                    // 从Tag中获取字段名称
                    if (row.Tag != null)
                    {
                        dynamic tagData = row.Tag;
                        if (tagData.FieldNames != null)
                        {
                            indexConfig.FieldNames = new List<string>(tagData.FieldNames);
                        }
                    }

                    // 验证配置完整性
                    if (!string.IsNullOrEmpty(indexConfig.TargetSheetName) &&
                        !string.IsNullOrEmpty(indexConfig.SourceSheetName) &&
                        indexConfig.FieldNames != null && indexConfig.FieldNames.Count > 0)
                    {
                        config.IndexConfigs.Add(indexConfig);
                    }
                }

                if (config.IndexConfigs.Count == 0)
                {
                    MessageBox.Show("请至少配置一个有效的索引！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 执行汇总操作
                MessageBox.Show("开始执行西安汇总操作...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // 调用实际的汇总逻辑
                ProcessXianSummary(config);

                MessageBox.Show("西安汇总操作完成！", "完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"执行汇总时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 执行西安汇总处理
        /// </summary>
        /// <param name="config">汇总配置</param>
        private void ProcessXianSummary(XianSummaryConfig config)
        {
            Microsoft.Office.Interop.Excel.Application excelApp = Globals.ThisAddIn.Application;
            Microsoft.Office.Interop.Excel.Workbook templateWorkbook = null;

            try
            {
                // 1. 打开模板文件
                templateWorkbook = excelApp.Workbooks.Open(config.TemplateFilePath);

                // 2. 处理每个单体表文件
                int processedCount = 0;
                foreach (string sourceFilePath in config.SourceFilePaths)
                {
                    try
                    {
                        ProcessSingleSourceFile(templateWorkbook, sourceFilePath, config, processedCount);
                        processedCount++;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"处理文件 {System.IO.Path.GetFileName(sourceFilePath)} 失败：{ex.Message}",
                                      "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }

                // 3. 保存结果文件
                string resultFileName = $"西安汇总_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                string resultPath = System.IO.Path.Combine(System.IO.Path.GetDirectoryName(config.TemplateFilePath), resultFileName);
                templateWorkbook.SaveAs(resultPath);

                MessageBox.Show($"汇总完成！已处理 {processedCount} 个文件。\n结果保存至：{resultPath}",
                              "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            finally
            {
                if (templateWorkbook != null)
                {
                    templateWorkbook.Close(false);
                }
            }
        }

        /// <summary>
        /// 处理单个源文件
        /// </summary>
        /// <param name="templateWorkbook">模板工作簿</param>
        /// <param name="sourceFilePath">源文件路径</param>
        /// <param name="config">配置信息</param>
        /// <param name="fileIndex">文件索引</param>
        private void ProcessSingleSourceFile(Microsoft.Office.Interop.Excel.Workbook templateWorkbook, string sourceFilePath,
                                           XianSummaryConfig config, int fileIndex)
        {
            Microsoft.Office.Interop.Excel.Application excelApp = Globals.ThisAddIn.Application;
            Microsoft.Office.Interop.Excel.Workbook sourceWorkbook = null;

            try
            {
                // 打开源文件，兼容WPS和Excel
                try
                {
                    // 首先尝试简单的只读打开方式（兼容WPS）
                    sourceWorkbook = excelApp.Workbooks.Open(sourceFilePath, ReadOnly: true);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"简单打开失败，尝试完整参数打开：{ex.Message}");
                    // 如果简单方式失败，尝试完整参数（兼容Excel）
                    sourceWorkbook = excelApp.Workbooks.Open(
                        Filename: sourceFilePath,
                        UpdateLinks: 0,
                        ReadOnly: true,
                        Format: 5,
                        Password: Type.Missing,
                        WriteResPassword: Type.Missing,
                        IgnoreReadOnlyRecommended: true,
                        Origin: Type.Missing,
                        Delimiter: Type.Missing,
                        Editable: false,
                        Notify: false,
                        Converter: Type.Missing,
                        AddToMru: false,
                        Local: Type.Missing,
                        CorruptLoad: Type.Missing
                    );
                }

                // 处理每个索引配置
                foreach (var indexConfig in config.IndexConfigs)
                {
                    try
                    {
                        // 获取源工作表
                        Microsoft.Office.Interop.Excel.Worksheet sourceSheet = null;
                        foreach (Microsoft.Office.Interop.Excel.Worksheet sheet in sourceWorkbook.Sheets)
                        {
                            if (sheet.Name == indexConfig.SourceSheetName)
                            {
                                sourceSheet = sheet;
                                break;
                            }
                        }

                        if (sourceSheet == null)
                        {
                            System.Diagnostics.Debug.WriteLine($"在源文件中未找到工作表：{indexConfig.SourceSheetName}");
                            continue;
                        }

                        // 获取目标工作表
                        Microsoft.Office.Interop.Excel.Worksheet targetSheet = null;
                        foreach (Microsoft.Office.Interop.Excel.Worksheet sheet in templateWorkbook.Sheets)
                        {
                            if (sheet.Name == indexConfig.TargetSheetName)
                            {
                                targetSheet = sheet;
                                break;
                            }
                        }

                        if (targetSheet == null)
                        {
                            System.Diagnostics.Debug.WriteLine($"在模板文件中未找到工作表：{indexConfig.TargetSheetName}");
                            continue;
                        }

                        // 从源文件中提取数据并插入到模板中
                        var fieldDataMap = FindDataByIndexRowAndColumn(sourceSheet, indexConfig);
                        InsertMultiRowDataToTemplate(targetSheet, fieldDataMap, indexConfig);

                        System.Diagnostics.Debug.WriteLine($"成功处理索引配置：{indexConfig.TargetSheetName} <- {indexConfig.SourceSheetName}");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"处理索引配置失败：{ex.Message}");
                        MessageBox.Show($"处理索引配置失败：{indexConfig.TargetSheetName} <- {indexConfig.SourceSheetName}\n错误：{ex.Message}",
                                      "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
            }
            finally
            {
                if (sourceWorkbook != null)
                {
                    sourceWorkbook.Close(false);
                }
            }
        }

        /// <summary>
        /// 根据配置的字段名称在源文件中查找数据（支持多行索引，带索引词筛选）
        /// </summary>
        private Dictionary<string, List<string>> FindDataByIndexRowAndColumn(Microsoft.Office.Interop.Excel.Worksheet sourceSheet, XianSummaryIndexConfig indexConfig)
        {
            var fieldDataMap = new Dictionary<string, List<string>>();

            try
            {
                System.Diagnostics.Debug.WriteLine($"=== 开始数据提取 ===");
                System.Diagnostics.Debug.WriteLine($"索引范围：行{indexConfig.IndexStartRow}-{indexConfig.IndexEndRow}，列{indexConfig.IndexStartColumn}-{indexConfig.IndexEndColumn}");
                System.Diagnostics.Debug.WriteLine($"抓取范围：行{indexConfig.StartRow}-{indexConfig.EndRow}，列{indexConfig.StartColumn}-{indexConfig.EndColumn}");
                System.Diagnostics.Debug.WriteLine($"字段名称：{string.Join(", ", indexConfig.FieldNames)}");

                // 1. 构建字段到列的映射（基于索引范围）
                var fieldColumnMap = BuildMultiRowFieldColumnMap(sourceSheet, indexConfig);
                System.Diagnostics.Debug.WriteLine($"字段映射结果: {string.Join(", ", fieldColumnMap.Select(kv => $"{kv.Key}->列{kv.Value}"))}");

                // 2. 收集所有索引词（用于后续筛选）
                var indexWords = CollectIndexWords(sourceSheet, indexConfig);
                System.Diagnostics.Debug.WriteLine($"收集到的索引词: {string.Join(", ", indexWords)}");

                // 3. 初始化字段数据列表
                foreach (string fieldName in indexConfig.FieldNames)
                {
                    fieldDataMap[fieldName] = new List<string>();
                }

                // 4. 在抓取范围内提取数据（跳过索引词和空行）
                System.Diagnostics.Debug.WriteLine($"=== 开始在抓取范围内提取数据 ===");
                for (int dataRow = indexConfig.StartRow; dataRow <= indexConfig.EndRow; dataRow++)
                {
                    // 检查是否为空行
                    if (IsEmptyRow(sourceSheet, dataRow, indexConfig.StartColumn, indexConfig.EndColumn))
                    {
                        System.Diagnostics.Debug.WriteLine($"跳过空行 {dataRow}");
                        continue;
                    }

                    // 检查是否包含索引词（如果包含则跳过）
                    if (ContainsIndexWords(sourceSheet, dataRow, indexConfig.StartColumn, indexConfig.EndColumn, indexWords))
                    {
                        System.Diagnostics.Debug.WriteLine($"跳过索引行 {dataRow}（包含索引词）");
                        continue;
                    }

                    // 提取该行的数据
                    System.Diagnostics.Debug.WriteLine($"提取第{dataRow}行数据:");
                    bool hasValidData = false;

                    // 使用新的映射方式提取数据
                    for (int i = 0; i < indexConfig.FieldNames.Count; i++)
                    {
                        string fieldName = indexConfig.FieldNames[i];
                        string mapKey = $"{fieldName}_{i}"; // 使用与映射时相同的键
                        string dataValue = "";

                        if (string.IsNullOrEmpty(fieldName))
                        {
                            // 字段名称为空，添加空值
                            System.Diagnostics.Debug.WriteLine($"  字段[{i}]为空，添加空值");
                        }
                        else if (fieldColumnMap.ContainsKey(mapKey))
                        {
                            int col = fieldColumnMap[mapKey];
                            Microsoft.Office.Interop.Excel.Range dataCell = sourceSheet.Cells[dataRow, col];
                            dataValue = GetCellDisplayValue(dataCell) ?? "";

                            // 检查数据值是否为索引词（额外保护）
                            if (!indexWords.Contains(dataValue.Trim()))
                            {
                                System.Diagnostics.Debug.WriteLine($"  字段[{i}]'{fieldName}' -> 列{col} -> 值'{dataValue}'");
                                if (!string.IsNullOrEmpty(dataValue.Trim()))
                                {
                                    hasValidData = true;
                                }
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"  字段[{i}]'{fieldName}' -> 列{col} -> 跳过索引词'{dataValue}'");
                                dataValue = ""; // 将索引词替换为空值
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"  警告：字段[{i}]'{fieldName}'(映射键:{mapKey})未找到对应列");
                        }

                        fieldDataMap[fieldName].Add(dataValue);
                    }

                    // 如果该行没有有效数据，记录但继续处理
                    if (!hasValidData)
                    {
                        System.Diagnostics.Debug.WriteLine($"  第{dataRow}行没有有效数据");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"数据提取完成，共提取 {fieldDataMap.Values.FirstOrDefault()?.Count ?? 0} 行数据");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"查找数据时出错：{ex.Message}");
            }

            return fieldDataMap;
        }

        /// <summary>
        /// 收集索引范围内的所有索引词
        /// </summary>
        private HashSet<string> CollectIndexWords(Microsoft.Office.Interop.Excel.Worksheet sourceSheet, XianSummaryIndexConfig indexConfig)
        {
            var indexWords = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

            try
            {
                System.Diagnostics.Debug.WriteLine("收集索引词...");

                // 遍历索引范围内的所有单元格
                for (int row = indexConfig.IndexStartRow; row <= indexConfig.IndexEndRow; row++)
                {
                    for (int col = indexConfig.IndexStartColumn; col <= indexConfig.IndexEndColumn; col++)
                    {
                        Microsoft.Office.Interop.Excel.Range cell = sourceSheet.Cells[row, col];
                        string cellValue = GetCellDisplayValue(cell)?.Trim();

                        if (!string.IsNullOrEmpty(cellValue))
                        {
                            indexWords.Add(cellValue);
                            System.Diagnostics.Debug.WriteLine($"  添加索引词: '{cellValue}' (行{row},列{col})");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"收集索引词时出错：{ex.Message}");
            }

            return indexWords;
        }

        /// <summary>
        /// 检查指定行是否为空行
        /// </summary>
        private bool IsEmptyRow(Microsoft.Office.Interop.Excel.Worksheet sourceSheet, int row, int startCol, int endCol)
        {
            try
            {
                for (int col = startCol; col <= endCol; col++)
                {
                    Microsoft.Office.Interop.Excel.Range cell = sourceSheet.Cells[row, col];
                    string cellValue = GetCellDisplayValue(cell)?.Trim();
                    if (!string.IsNullOrEmpty(cellValue))
                    {
                        return false;
                    }
                }
                return true;
            }
            catch
            {
                return true;
            }
        }

        /// <summary>
        /// 检查指定行是否包含索引词
        /// </summary>
        private bool ContainsIndexWords(Microsoft.Office.Interop.Excel.Worksheet sourceSheet, int row, int startCol, int endCol, HashSet<string> indexWords)
        {
            try
            {
                for (int col = startCol; col <= endCol; col++)
                {
                    Microsoft.Office.Interop.Excel.Range cell = sourceSheet.Cells[row, col];
                    string cellValue = GetCellDisplayValue(cell)?.Trim();

                    if (!string.IsNullOrEmpty(cellValue) && indexWords.Contains(cellValue))
                    {
                        return true;
                    }
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 构建基于详细字段信息的精确映射关系
        /// 使用字段的唯一标识符和位置信息，确保相同名称字段在不同列的正确识别
        /// </summary>
        private Dictionary<string, int> BuildMultiRowFieldColumnMap(Microsoft.Office.Interop.Excel.Worksheet sourceSheet, XianSummaryIndexConfig indexConfig)
        {
            var fieldColumnMap = new Dictionary<string, int>();

            try
            {
                System.Diagnostics.Debug.WriteLine($"=== 构建精确字段映射 ===");
                System.Diagnostics.Debug.WriteLine($"索引范围：行{indexConfig.IndexStartRow}-{indexConfig.IndexEndRow}，列{indexConfig.IndexStartColumn}-{indexConfig.IndexEndColumn}");
                System.Diagnostics.Debug.WriteLine($"配置字段数量：{indexConfig.FieldNames.Count}");

                // 如果有详细字段信息，使用精确映射
                if (indexConfig.FieldInfos != null && indexConfig.FieldInfos.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine("使用详细字段信息进行精确映射");

                    for (int i = 0; i < indexConfig.FieldInfos.Count; i++)
                    {
                        var fieldInfo = indexConfig.FieldInfos[i];

                        // 使用唯一键作为映射键，确保相同名称字段不会冲突
                        string mapKey = $"{fieldInfo.FieldName}_{i}"; // 使用字段名称+索引作为键
                        fieldColumnMap[mapKey] = fieldInfo.Column;

                        System.Diagnostics.Debug.WriteLine($"精确映射[{i}]: '{fieldInfo.FieldName}' -> 列{fieldInfo.Column} (唯一键: {fieldInfo.UniqueKey})");
                        System.Diagnostics.Debug.WriteLine($"  层级结构: [{string.Join(" -> ", fieldInfo.Hierarchy)}]");
                    }
                }
                else
                {
                    // 回退到简单的位置映射
                    System.Diagnostics.Debug.WriteLine("使用简单位置映射（回退模式）");

                    for (int i = 0; i < indexConfig.FieldNames.Count; i++)
                    {
                        string fieldName = indexConfig.FieldNames[i];
                        int targetCol = indexConfig.IndexStartColumn + i;

                        if (targetCol <= indexConfig.IndexEndColumn)
                        {
                            string mapKey = $"{fieldName}_{i}";
                            fieldColumnMap[mapKey] = targetCol;
                            System.Diagnostics.Debug.WriteLine($"简单映射[{i}]: '{fieldName}' -> 列{targetCol}");
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"字段映射完成，共映射 {fieldColumnMap.Count} 个字段");
                return fieldColumnMap;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"构建字段映射时出错：{ex.Message}");
                return new Dictionary<string, int>();
            }
        }



        /// <summary>
        /// 将多行数据插入到模板的指定位置
        /// </summary>
        private void InsertMultiRowDataToTemplate(Microsoft.Office.Interop.Excel.Worksheet templateSheet,
                                                 Dictionary<string, List<string>> fieldDataMap,
                                                 XianSummaryIndexConfig indexConfig)
        {
            try
            {
                if (fieldDataMap.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("没有找到任何数据，跳过插入");
                    return;
                }

                // 获取数据行数
                int dataRowCount = fieldDataMap.Values.FirstOrDefault()?.Count ?? 0;
                if (dataRowCount == 0)
                {
                    System.Diagnostics.Debug.WriteLine("数据行数为0，跳过插入");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"开始插入数据，数据行数: {dataRowCount}，字段数: {indexConfig.FieldNames.Count}");

                // 插入数据行
                for (int dataRowIndex = 0; dataRowIndex < dataRowCount; dataRowIndex++)
                {
                    int actualInsertRow = indexConfig.InsertRow + dataRowIndex;
                    int currentColumn = indexConfig.InsertColumn;

                    // 按照索引字段的顺序插入数据
                    foreach (string fieldName in indexConfig.FieldNames)
                    {
                        string dataValue = "";

                        if (fieldDataMap.ContainsKey(fieldName) && dataRowIndex < fieldDataMap[fieldName].Count)
                        {
                            dataValue = fieldDataMap[fieldName][dataRowIndex] ?? "";
                        }

                        // 插入到模板的指定位置
                        Microsoft.Office.Interop.Excel.Range targetCell = templateSheet.Cells[actualInsertRow, currentColumn];
                        targetCell.Value = dataValue;

                        System.Diagnostics.Debug.WriteLine($"插入数据: 字段='{fieldName}', 值='{dataValue}', 位置=({actualInsertRow},{currentColumn})");

                        currentColumn++;
                    }
                }

                System.Diagnostics.Debug.WriteLine($"数据插入完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"插入数据时出错：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取单元格显示值
        /// </summary>
        private string GetCellDisplayValue(Microsoft.Office.Interop.Excel.Range cell)
        {
            try
            {
                if (cell?.Value != null)
                {
                    return cell.Value.ToString();
                }
                return "";
            }
            catch
            {
                return "";
            }
        }
    }


}
