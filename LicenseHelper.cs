﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

public static class LicenseHelper
{
    private const string secret = "MySimpleSecretKey";

    public static string GenerateLicenseKey(string machineCode)
    {
        using (SHA256 sha = SHA256.Create())
        {
            string combined = machineCode + secret;
            byte[] hash = sha.ComputeHash(Encoding.UTF8.GetBytes(combined));
            return Convert.ToBase64String(hash).Substring(0, 25).ToUpper();
        }
    }

    public static bool ValidateLicense(string machineCode, string inputLicense)
    {
        string expected = GenerateLicenseKey(machineCode);
        return expected == inputLicense.ToUpper();
    }

    public static void SaveLicense(string license)
    {
        string path = GetLicenseFilePath();
        File.WriteAllText(path, license.Trim());
    }

    public static string LoadLicense()
    {
        string path = GetLicenseFilePath();
        if (File.Exists(path))
            return File.ReadAllText(path).Trim();
        return null;
    }

    public static string GetLicenseFilePath()
    {
        string folder = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
        return Path.Combine(folder, "excel_plugin_license.txt");
    }

    // ✅ 添加以下两个方法
    public static void SaveRegisterTime(DateTime time)
    {
        string folder = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
        string path = Path.Combine(folder, "excel_plugin_register_time.txt");
        File.WriteAllText(path, time.ToString("yyyy-MM-dd"));
    }

    public static DateTime? LoadRegisterTime()
    {
        string folder = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
        string path = Path.Combine(folder, "excel_plugin_register_time.txt");

        if (File.Exists(path))
        {
            if (DateTime.TryParse(File.ReadAllText(path), out DateTime dt))
                return dt;
        }

        return null;
    }

    // 可选：注册是否有效（结合时间）
    public static bool IsLicenseValid(string machineCode)
    {
        string license = LoadLicense();
        DateTime? regTime = LoadRegisterTime();

        return !string.IsNullOrEmpty(license) &&
               ValidateLicense(machineCode, license) &&
               regTime.HasValue &&
               (DateTime.Now - regTime.Value).TotalDays <= 365;
    }
}
