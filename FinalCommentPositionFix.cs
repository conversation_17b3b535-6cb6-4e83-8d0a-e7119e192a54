using System;
using Word = Microsoft.Office.Interop.Word;

namespace ExcelToWord
{
    /// <summary>
    /// 最终批注位置修复方案
    /// 解决批注偏移到相邻词语的问题
    /// </summary>
    public class FinalCommentPositionFix
    {
        /// <summary>
        /// 验证批注位置的准确性
        /// </summary>
        /// <param name="doc">Word文档</param>
        /// <returns>验证报告</returns>
        public static string ValidateCommentPositions(Word.Document doc)
        {
            try
            {
                int totalComments = doc.Comments.Count;
                int correctPositions = 0;
                int incorrectPositions = 0;
                
                foreach (Word.Comment comment in doc.Comments)
                {
                    string commentScope = comment.Scope.Text?.Trim() ?? "";
                    string commentContent = comment.Range.Text?.Trim() ?? "";
                    
                    // 检查批注是否在链接字段上（包含Excel数据的内容）
                    if (commentContent.Contains("链接字段") && !string.IsNullOrEmpty(commentScope))
                    {
                        // 如果批注范围包含数字或特殊字符，可能是正确的链接内容
                        if (commentScope.Any(c => char.IsDigit(c)) || commentScope.Length > 2)
                        {
                            correctPositions++;
                        }
                        else
                        {
                            incorrectPositions++;
                            System.Diagnostics.Debug.WriteLine($"可能的错误批注位置：'{commentScope}' - {commentContent}");
                        }
                    }
                }
                
                return $"批注位置验证：总计 {totalComments} 个，正确 {correctPositions} 个，可能错误 {incorrectPositions} 个";
            }
            catch (Exception ex)
            {
                return $"验证失败：{ex.Message}";
            }
        }
        
        /// <summary>
        /// 测试PasteSpecial后Selection范围的变化
        /// </summary>
        /// <param name="wordApp">Word应用程序</param>
        /// <param name="beforeText">粘贴前的文本</param>
        /// <param name="afterText">粘贴后的文本</param>
        /// <returns>范围变化信息</returns>
        public static string TestSelectionRangeChange(Word.Application wordApp, string beforeText, string afterText)
        {
            try
            {
                Word.Range currentSelection = wordApp.Selection.Range;
                
                return $"Selection变化：粘贴前 '{beforeText}' -> 粘贴后 '{afterText}' (位置: {currentSelection.Start}-{currentSelection.End})";
            }
            catch (Exception ex)
            {
                return $"测试失败：{ex.Message}";
            }
        }
    }
    
    /// <summary>
    /// 最终解决方案的关键要点
    /// </summary>
    public static class FinalSolutionKeyPoints
    {
        public const string PROBLEM_ROOT_CAUSE = @"
        问题根本原因：
        1. PasteSpecial操作后，Word的Selection.Range可能发生偏移
        2. 在格式恢复或其他操作后添加批注，导致批注位置不准确
        3. 复杂的范围重新定位逻辑可能引入新的错误
        ";
        
        public const string FINAL_SOLUTION = @"
        最终解决方案：
        1. 在PasteSpecial前准备好批注信息，避免后续操作影响
        2. PasteSpecial后立即获取Selection.Range.Duplicate
        3. 立即添加批注，不进行任何其他操作
        4. 批注添加完成后再进行格式恢复
        5. 简化流程，减少可能导致范围偏移的操作
        ";
        
        public const string KEY_CODE_CHANGES = @"
        关键代码改进：
        
        // 1. 提前准备批注信息
        string commentText = string.Format(...);
        
        // 2. 执行粘贴操作
        wordApp.Selection.PasteSpecial(Link: ref link, DataType: ref dataType);
        
        // 3. 立即获取范围并添加批注
        Word.Range pastedRange = wordApp.Selection.Range.Duplicate;
        Word.Comment comment = pastedRange.Comments.Add(pastedRange, commentText);
        
        // 4. 最后恢复格式
        pastedRange.Font.Name = originalFontName;
        ...
        ";
        
        public const string TESTING_RECOMMENDATIONS = @"
        测试建议：
        1. 创建包含多个占位符的测试文档
        2. 确保占位符之间有其他文字（如：【字段1】提供【字段2】负责）
        3. 运行链接报告功能
        4. 检查每个批注是否都在对应的链接字段上
        5. 特别注意检查是否有批注出现在'提供'、'负责'等连接词上
        ";
    }
    
    /// <summary>
    /// 调试辅助工具
    /// </summary>
    public static class CommentDebugHelper
    {
        /// <summary>
        /// 输出文档中所有批注的详细信息
        /// </summary>
        /// <param name="doc">Word文档</param>
        public static void LogAllComments(Word.Document doc)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=== 文档批注详细信息 ===");
                
                for (int i = 1; i <= doc.Comments.Count; i++)
                {
                    Word.Comment comment = doc.Comments[i];
                    string scopeText = comment.Scope.Text?.Trim() ?? "";
                    string commentText = comment.Range.Text?.Trim() ?? "";
                    
                    System.Diagnostics.Debug.WriteLine($"批注 {i}:");
                    System.Diagnostics.Debug.WriteLine($"  位置: {comment.Scope.Start}-{comment.Scope.End}");
                    System.Diagnostics.Debug.WriteLine($"  范围文本: '{scopeText}'");
                    System.Diagnostics.Debug.WriteLine($"  批注内容: '{commentText.Substring(0, Math.Min(50, commentText.Length))}...'");
                    System.Diagnostics.Debug.WriteLine("");
                }
                
                System.Diagnostics.Debug.WriteLine("=== 批注信息结束 ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"输出批注信息失败：{ex.Message}");
            }
        }
    }
}
