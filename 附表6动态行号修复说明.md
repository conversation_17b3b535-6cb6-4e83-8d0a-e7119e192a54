# 附表6动态行号修复说明

## 🎯 问题描述

**问题现象**：
- 处理多个文件时，附表6的数据会相互覆盖
- 第一个文件（单体表）写入第3行
- 第二个文件也写入第3行，覆盖了第一个文件的数据
- 应该是第一个文件在第3行，第二个文件在第4行，依次类推

**根本原因**：
附表6的数据写入使用了固定行号（第3行），而其他附表（如附表1、附表2、附表5）都使用了动态行号逻辑。

## 🔧 解决方案

### 1. 问题定位
**原代码**（第2103行）：
```csharp
Excel.Range targetCell = targetSheet6.Cells[3, targetCol]; // 固定第3行
```

**其他附表的正确做法**（如附表1）：
```csharp
// 查找最后一行数据
int lastRowTgt = targetSheet3.Cells[targetSheet3.Rows.Count, 2].End(Excel.XlDirection.xlUp).Row;
int insertRow = lastRowTgt < 6 ? 6 : lastRowTgt + 1; // 动态行号
```

### 2. 修复方法
**新代码**（第2051-2107行）：
```csharp
// 确定插入行号：查找附表6中第一列（A列）最后有数据的行
int lastRowTgt6 = targetSheet6.Cells[targetSheet6.Rows.Count, 1].End(Excel.XlDirection.xlUp).Row;
int insertRow6 = lastRowTgt6 < 3 ? 3 : lastRowTgt6 + 1; // 从第3行开始，如果已有数据则在下一行插入

// 使用动态行号写入数据
Excel.Range targetCell = targetSheet6.Cells[insertRow6, targetCol]; // 使用动态行号
```

### 3. 动态行号逻辑说明
```csharp
// 1. 查找A列最后有数据的行
int lastRowTgt6 = targetSheet6.Cells[targetSheet6.Rows.Count, 1].End(Excel.XlDirection.xlUp).Row;

// 2. 确定插入位置
int insertRow6 = lastRowTgt6 < 3 ? 3 : lastRowTgt6 + 1;
```

**逻辑解释**：
- `targetSheet6.Cells[targetSheet6.Rows.Count, 1]`：从A列最后一行开始
- `.End(Excel.XlDirection.xlUp)`：向上查找最后一个有数据的单元格
- `lastRowTgt6 < 3 ? 3 : lastRowTgt6 + 1`：如果没有数据或数据在第3行之前，从第3行开始；否则在最后一行数据的下一行插入

## 📊 修复效果

### 修复前（固定第3行）
```
处理第1个文件：数据写入第3行
处理第2个文件：数据写入第3行（覆盖第1个文件）
处理第3个文件：数据写入第3行（覆盖前面的文件）
结果：只能看到最后一个文件的数据
```

### 修复后（动态行号）
```
处理第1个文件：数据写入第3行
处理第2个文件：数据写入第4行
处理第3个文件：数据写入第5行
结果：所有文件的数据都保留，按顺序排列
```

## 🔍 技术细节

### 1. 行号检测逻辑
- **检测列**：使用A列（第1列）作为检测基准
- **检测方法**：`End(Excel.XlDirection.xlUp)`从底部向上查找
- **起始行**：第3行（附表6的数据起始行）

### 2. 与其他附表的一致性
现在附表6的逻辑与其他附表保持一致：

**附表1**：
```csharp
int lastRowTgt = targetSheet3.Cells[targetSheet3.Rows.Count, 2].End(Excel.XlDirection.xlUp).Row;
int insertRow = lastRowTgt < 6 ? 6 : lastRowTgt + 1;
```

**附表2**：
```csharp
int lastRowTgt = targetSheet4.Cells[targetSheet4.Rows.Count, 2].End(Excel.XlDirection.xlUp).Row;
int writeRow = lastRowTgt < targetStartRow4 ? targetStartRow4 : lastRowTgt + 1;
```

**附表5**：
```csharp
int lastRowTgt = targetSheet5.Cells[targetSheet5.Rows.Count, 2].End(Excel.XlDirection.xlUp).Row;
int insertRow = lastRowTgt < targetStartRow5 ? targetStartRow5 : lastRowTgt + 1;
```

**附表6**（修复后）：
```csharp
int lastRowTgt6 = targetSheet6.Cells[targetSheet6.Rows.Count, 1].End(Excel.XlDirection.xlUp).Row;
int insertRow6 = lastRowTgt6 < 3 ? 3 : lastRowTgt6 + 1;
```

### 3. 检测列的选择
- **附表1、2、5**：使用B列（第2列）检测
- **附表6**：使用A列（第1列）检测

选择A列的原因：
- 附表6通常第一列就有数据（如序号、项目名称等）
- 确保检测的准确性和一致性

## 🧪 测试用例

### 测试用例1：单个文件
```
初始状态：附表6只有标题行（第1、2行）
处理文件1：数据写入第3行
结果：第3行有数据
```

### 测试用例2：多个文件
```
初始状态：附表6只有标题行（第1、2行）
处理文件1：数据写入第3行
处理文件2：检测到第3行有数据，写入第4行
处理文件3：检测到第4行有数据，写入第5行
结果：第3、4、5行分别有不同文件的数据
```

### 测试用例3：模板已有数据
```
初始状态：附表6已有数据到第5行
处理文件1：检测到第5行有数据，写入第6行
处理文件2：检测到第6行有数据，写入第7行
结果：新数据追加在现有数据后面
```

## ⚡ 性能影响

### 1. 额外操作
- 每个文件处理时增加一次行号检测
- 使用Excel的`End()`方法，性能良好

### 2. 检测效率
- `End(Excel.XlDirection.xlUp)`是Excel内置的高效方法
- 比遍历所有行查找数据要快得多
- 性能影响微乎其微

## 🔒 兼容性考虑

### 1. Excel版本兼容性
- `End()`方法在所有Excel版本中都支持
- `XlDirection.xlUp`是标准枚举值

### 2. 数据格式兼容性
- 不依赖特定的数据格式
- 只要A列有数据就能正确检测

### 3. 模板兼容性
- 兼容空模板（从第3行开始）
- 兼容已有数据的模板（追加在后面）

## 📋 验证清单

### 功能验证
- [ ] 单个文件数据写入第3行
- [ ] 多个文件数据写入不同行
- [ ] 数据不会相互覆盖
- [ ] 保持原有的字段匹配功能
- [ ] 数字格式正确显示

### 边界情况验证
- [ ] 空模板处理正确
- [ ] 已有数据的模板处理正确
- [ ] 大量文件处理正确
- [ ] 文件处理失败不影响后续文件

### 一致性验证
- [ ] 与其他附表行为一致
- [ ] 行号检测逻辑正确
- [ ] 数据顺序符合预期

## 🎯 预期效果

修复完成后：
- ✅ **多文件支持**：每个文件的数据写入不同行，不会覆盖
- ✅ **顺序正确**：第一个文件在第3行，第二个文件在第4行，依次类推
- ✅ **数据完整**：所有文件的数据都会保留
- ✅ **逻辑一致**：与其他附表的处理逻辑保持一致
- ✅ **向后兼容**：不影响单个文件的处理

现在处理多个文件时，附表6的数据会按顺序写入不同行，不会相互覆盖！
