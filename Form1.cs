﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using Excel = Microsoft.Office.Interop.Excel;
using Word = Microsoft.Office.Interop.Word;

namespace ExcelToWord
{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取 Excel 应用实例
                Excel.Application excelApp;
                try
                {
                    excelApp = (Excel.Application)Marshal.GetActiveObject("Excel.Application");
                }
                catch (COMException)
                {
                    MessageBox.Show("Excel 未打开，请先打开 Excel 并选择单元格！");
                    return;
                }

                // 获取选中的 Excel 单元格内容
                Excel.Range selection = excelApp.Selection as Excel.Range;
                if (selection == null || selection.Cells.Count == 0)
                {
                    MessageBox.Show("请先选择 Excel 单元格！");
                    return;
                }

                string selectedText = selection.Text.ToString();
                if (string.IsNullOrWhiteSpace(selectedText))
                {
                    MessageBox.Show("选中的单元格为空！");
                    return;
                }

                // 获取 Word 应用实例
                Word.Application wordApp;
                try
                {
                    wordApp = (Word.Application)Marshal.GetActiveObject("Word.Application");
                }
                catch (COMException)
                {
                    MessageBox.Show("Word 未打开，请先打开 Word 文档！");
                    return;
                }

                // 获取 Word 活动文档
                if (wordApp.Documents.Count == 0)
                {
                    MessageBox.Show("没有打开的 Word 文档！");
                    return;
                }
                Word.Document activeDoc = wordApp.ActiveDocument;

                // 在光标处插入 Excel 选中的文本
                Word.Selection wordSelection = wordApp.Selection;
                wordSelection.TypeText(selectedText);
                wordSelection.TypeParagraph();  // 换行

                MessageBox.Show("Excel 单元格内容已插入到 Word 文档光标处！");
            }
            catch (Exception ex)
            {
                MessageBox.Show("操作失败: " + ex.Message);
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取 Word 应用实例
                Word.Application wordApp;
                try
                {
                    wordApp = (Word.Application)Marshal.GetActiveObject("Word.Application");
                }
                catch (COMException)
                {
                    MessageBox.Show("Word 未打开，请先打开一个 Word 文档并选择文本！");
                    return;
                }

                // 获取 Word 选中的文本
                Word.Selection wordSelection = wordApp.Selection;
                string selectedText = wordSelection.Text.Trim();
                if (string.IsNullOrWhiteSpace(selectedText))
                {
                    MessageBox.Show("请先在 Word 文档中选择内容！");
                    return;
                }

                // 获取 Excel 应用实例
                Excel.Application excelApp;
                try
                {
                    excelApp = (Excel.Application)Marshal.GetActiveObject("Excel.Application");
                }
                catch (COMException)
                {
                    MessageBox.Show("Excel 未打开，请先打开 Excel 文档！");
                    return;
                }

                // 获取 Excel 选中的单元格
                Excel.Range selection = excelApp.Selection as Excel.Range;
                if (selection == null || selection.Cells.Count == 0)
                {
                    MessageBox.Show("请先在 Excel 中选择目标单元格！");
                    return;
                }

                // 将 Word 选中的文本插入 Excel 选中的单元格
                selection.Value = selectedText;

                MessageBox.Show("Word 选中的内容已成功插入 Excel 选定单元格！");
            }
            catch (Exception ex)
            {
                MessageBox.Show("操作失败: " + ex.Message);
            }

        }
        //带连接插入
        private void button3_Click(object sender, EventArgs e)
        {
            Excel.Application excelApp = Globals.ThisAddIn.Application;

            if (excelApp.Selection is Excel.Range selectedRange)
            {
                try
                {
                    Word.Application wordApp = null;
                    Word.Document wordDoc = null;

                    try
                    {
                        wordApp = (Word.Application)System.Runtime.InteropServices.Marshal.GetActiveObject("Word.Application");
                        wordDoc = wordApp.ActiveDocument;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("未找到已打开的Word文档，请先打开一个Word文档！ 错误信息: " + ex.Message);
                        return;
                    }

                    // 尝试减少内存占用，先复制到剪贴板
                    selectedRange.Copy();

                    Word.Range wordRange = wordApp.Selection.Range;

                    if (selectedRange.Count == 1)
                    {
                        object link = true;
                        object dataType = Word.WdPasteDataType.wdPasteText;
                        wordRange.PasteSpecial(Link: ref link, DataType: ref dataType);
                    }
                    else
                    {
                        object link = true;
                        object dataType = Word.WdPasteDataType.wdPasteRTF;
                        wordRange.PasteSpecial(Link: ref link, DataType: ref dataType);
                    }

                    // 清理资源
                    ReleaseComObject(wordRange);
                    ReleaseComObject(wordDoc);
                    ReleaseComObject(wordApp);

                    GC.Collect(); // 强制垃圾回收

                    MessageBox.Show("内容已插入到Word文档中！");
                }
                catch (Exception ex)
                {
                    MessageBox.Show("操作失败: " + ex.Message);
                }
            }
            else
            {
                MessageBox.Show("请先选中一个表格范围或单元格！");
            }
        }


        //更新链接
        private void button4_Click(object sender, EventArgs e)
        {
            Word.Application wordApp = null;
            try
            {
                // 获取正在运行的Word实例
                wordApp = (Word.Application)Marshal.GetActiveObject("Word.Application");
            }
            catch (COMException)
            {
                MessageBox.Show("请先打开 Microsoft Word！");
                return;
            }

            try
            {
                Word.Document doc = wordApp.ActiveDocument;
                if (doc != null)
                {
                    // 全选文档内容
                    wordApp.Selection.WholeStory();

                    // 更新所有字段（相当于手动按 F9）
                    doc.Fields.Update();

                    MessageBox.Show("Word 文档已全选并更新完成！");
                }
                else
                {
                    MessageBox.Show("未找到活动的 Word 文档！");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("操作失败：" + ex.Message);
            }
            finally
            {
                // 释放 COM 资源，避免 Word 进程残留
                ReleaseComObject(wordApp);
            }

        }

        //占位符插入
        private void button5_Click(object sender, EventArgs e)
        {
            // 获取当前Excel应用程序实例
            Excel.Application excelApp = Globals.ThisAddIn.Application;

            // 检查是否有选中的范围
            if (excelApp.Selection is Excel.Range selectedRange)
            {
                try
                {
                    // 获取Word应用程序实例
                    Word.Application wordApp = null;
                    Word.Document wordDoc = null;

                    // 尝试获取当前已经打开的Word文档
                    try
                    {
                        wordApp = (Word.Application)Marshal.GetActiveObject("Word.Application");
                        wordDoc = wordApp.ActiveDocument; // 获取当前活动的Word文档
                    }
                    catch
                    {
                        MessageBox.Show("未找到已打开的Word文档，请先打开一个Word文档！");
                        return;
                    }

                    // 获取选定范围的地址
                    string cellAddress = selectedRange.Address;

                    // 在Word文档的光标位置插入占位符
                    Word.Range wordRange = wordApp.Selection.Range;
                    wordRange.Text += $"{{{cellAddress}}}";

                    // 释放资源
                    Marshal.ReleaseComObject(wordRange);
                    Marshal.ReleaseComObject(wordDoc);
                    Marshal.ReleaseComObject(wordApp);

                    MessageBox.Show("表格地址已插入到Word文档中！");
                }
                catch (Exception ex)
                {
                    MessageBox.Show("操作失败: " + ex.Message);
                }
            }
            else
            {
                MessageBox.Show("请先选中一个表格范围！");
            }
        }

        //占位符修改
        private void button6_Click(object sender, EventArgs e)
        {
            // 获取当前 Excel 应用实例
            Excel.Application excelApp = Globals.ThisAddIn.Application;

            // 检查是否有选中的范围
            if (excelApp.Selection is Excel.Range selectedRange)
            {
                try
                {
                    // 获取 Word 应用实例
                    Word.Application wordApp = null;
                    Word.Document wordDoc = null;

                    // 获取已打开的 Word 文档
                    try
                    {
                        wordApp = (Word.Application)System.Runtime.InteropServices.Marshal.GetActiveObject("Word.Application");
                        wordDoc = wordApp.ActiveDocument;
                    }
                    catch
                    {
                        MessageBox.Show("未找到已打开的Word文档，请先打开一个Word文档！");
                        return;
                    }

                    // 获取 Word 文档文本
                    string wordText = wordDoc.Content.Text;
                    Regex regex = new Regex(@"\{\$([A-Z]+\$?\d+)\}"); // 识别 Excel 单元格格式的占位符
                    MatchCollection matches = regex.Matches(wordText);

                    foreach (Match match in matches)
                    {
                        string cellAddress = match.Groups[1].Value; // 获取 Excel 单元格地址
                        Excel.Range cellRange = selectedRange.Worksheet.Range[cellAddress];

                        if (cellRange != null)
                        {
                            // 复制 Excel 单元格数据到剪贴板
                            cellRange.Copy();

                            // 在 Word 文档中查找占位符
                            Word.Find find = wordDoc.Content.Find;
                            find.Text = match.Value;
                            find.Execute();

                            if (find.Found)
                            {
                                // 获取找到的占位符位置
                                Word.Range wordRange = find.Parent as Word.Range;

                                // 设置粘贴方式为带链接的粘贴
                                object link = true;
                                object dataType = Word.WdPasteDataType.wdPasteText;

                                // 粘贴 Excel 数据，并保持链接
                                wordRange.PasteSpecial(Link: ref link, DataType: ref dataType);
                            }
                        }
                    }

                    // 释放 COM 对象
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(wordDoc);
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(wordApp);

                    MessageBox.Show("Word文档已更新，并插入了Excel链接数据");
                }
                catch (Exception ex)
                {
                    MessageBox.Show("操作失败: " + ex.Message);
                }
            }
            else
            {
                MessageBox.Show("请先选中一个表格范围！");
            }
        }

        private void button7_Click(object sender, EventArgs e)
        {
            Excel.Application excelApp = Globals.ThisAddIn.Application;
            Excel.Workbook workbook = excelApp.ActiveWorkbook;
            Excel.Worksheet worksheet = workbook.ActiveSheet;
            Excel.Range selectedCell = excelApp.Selection as Excel.Range;

            if (selectedCell == null)
            {
                MessageBox.Show("请先选择一个 Excel 单元格所在的行！");
                return;
            }

            // 让用户输入索引行和数据行
            InputForm inputForm = new InputForm(selectedCell.Row);
            if (inputForm.ShowDialog() != DialogResult.OK)
            {
                return; // 用户取消操作
            }

            int indexRow = inputForm.IndexRow;
            int dataRow = inputForm.DataRow;

            Excel.Range indexRowRange = worksheet.Rows[indexRow];
            Excel.Range dataRowRange = worksheet.Rows[dataRow];

            // 读取索引行和数据行
            Dictionary<string, string> dataMap = new Dictionary<string, string>();
            int colCount = indexRowRange.Columns.Count;

            for (int col = 1; col <= colCount; col++)
            {
                string columnName = indexRowRange.Cells[1, col].Text.ToString().Trim();
                string cellValue = dataRowRange.Cells[1, col].Text.ToString().Trim();
                if (!string.IsNullOrEmpty(columnName))
                {
                    dataMap[$"【{columnName}】"] = cellValue;
                }
            }

            // 选择 Word 模板
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Title = "选择 Word 模板文件",
                Filter = "Word 文件 (*.doc;*.docx)|*.doc;*.docx",
                Multiselect = false
            };

            if (openFileDialog.ShowDialog() != DialogResult.OK)
            {
                MessageBox.Show("未选择 Word 模板文件，操作取消！");
                return;
            }
            string templatePath = openFileDialog.FileName;

            // 选择保存路径
            SaveFileDialog saveFileDialog = new SaveFileDialog
            {
                Title = "保存生成的 Word 报告",
                Filter = "Word 2007+ (*.docx)|*.docx|Word 97-2003 (*.doc)|*.doc",
                DefaultExt = "docx",
                FileName = "生成报告"
            };

            if (saveFileDialog.ShowDialog() != DialogResult.OK)
            {
                MessageBox.Show("未选择保存位置，操作取消！");
                return;
            }
            string outputPath = saveFileDialog.FileName;
            string fileExt = Path.GetExtension(outputPath).ToLower();

            // Word 保存格式
            object fileFormat;
            if (fileExt == ".docx")
                fileFormat = Word.WdSaveFormat.wdFormatXMLDocument;
            else if (fileExt == ".doc")
                fileFormat = Word.WdSaveFormat.wdFormatDocument97;
            else
            {
                MessageBox.Show("不支持的文件格式！");
                return;
            }

            // 打开 Word 文档并替换
            Word.Application wordApp = new Word.Application();
            Word.Document doc = wordApp.Documents.Open(templatePath);
            wordApp.Visible = false;

            // 提取文档中所有【xxx】格式的占位符
            List<string> placeholderList = ExtractPlaceholders(doc);

            // 遍历每个占位符
            foreach (string placeholder in placeholderList)
            {
                string value = dataMap.ContainsKey(placeholder) ? dataMap[placeholder] : null;
                FindAndReplace(doc, placeholder, value, highlightIfMissing: true);
            }

            // 保存并关闭
            doc.SaveAs2(outputPath, fileFormat);
            doc.Close();
            wordApp.Quit();

            MessageBox.Show($"Word 报告已成功生成！\n{outputPath}");
        }

        // 替换占位符，如果未找到则加粗并标黄
        private void FindAndReplace(Word.Document doc, string findText, string replaceText, bool highlightIfMissing = false)
        {
            Word.Range range = doc.Content;
            Word.Find findObject = range.Find;
            findObject.ClearFormatting();
            findObject.Text = findText;
            findObject.Replacement.ClearFormatting();

            object replaceAll = Word.WdReplace.wdReplaceAll;

            if (!string.IsNullOrEmpty(replaceText))
            {
                // 正常替换
                findObject.Replacement.Text = replaceText;
                findObject.Execute(Replace: ref replaceAll);
            }
            else if (highlightIfMissing)
            {
                // 高亮未匹配项
                while (findObject.Execute())
                {
                    range.Font.Bold = 1;
                    range.HighlightColorIndex = Word.WdColorIndex.wdYellow;
                }
            }
        }

        // 从 Word 文档中提取所有【xxx】格式的占位符
        private List<string> ExtractPlaceholders(Word.Document doc)
        {
            List<string> placeholders = new List<string>();
            string pattern = "【(.*?)】";
            MatchCollection matches = Regex.Matches(doc.Content.Text, pattern);

            foreach (Match match in matches)
            {
                string placeholder = match.Value;
                if (!placeholders.Contains(placeholder))
                {
                    placeholders.Add(placeholder);
                }
            }
            return placeholders;
        }

        // 自定义输入窗口
        public class InputForm : Form
        {
            private TextBox txtIndexRow;
            private TextBox txtDataRow;
            private Button btnOK;
            private Button btnCancel;

            public int IndexRow { get; private set; }
            public int DataRow { get; private set; }

            public InputForm(int defaultRow)
            {
                this.Text = "设置索引行和数据行";
                this.Width = 300;
                this.Height = 180;
                this.FormBorderStyle = FormBorderStyle.FixedDialog;
                this.StartPosition = FormStartPosition.CenterScreen;

                Label lblIndexRow = new Label { Text = "索引行号:", Left = 10, Top = 20, Width = 80 };
                txtIndexRow = new TextBox { Left = 100, Top = 20, Width = 150, Text = "1" };

                Label lblDataRow = new Label { Text = "数据行号:", Left = 10, Top = 50, Width = 80 };
                txtDataRow = new TextBox { Left = 100, Top = 50, Width = 150, Text = defaultRow.ToString() };

                btnOK = new Button { Text = "确定", Left = 50, Width = 80, Top = 90 };
                btnOK.Click += (sender, e) =>
                {
                    if (int.TryParse(txtIndexRow.Text, out int index) && int.TryParse(txtDataRow.Text, out int data))
                    {
                        IndexRow = index;
                        DataRow = data;
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                    else
                    {
                        MessageBox.Show("请输入有效的行号！");
                    }
                };

                btnCancel = new Button { Text = "取消", Left = 150, Width = 80, Top = 90 };
                btnCancel.Click += (sender, e) => { this.DialogResult = DialogResult.Cancel; this.Close(); };

                this.Controls.Add(lblIndexRow);
                this.Controls.Add(txtIndexRow);
                this.Controls.Add(lblDataRow);
                this.Controls.Add(txtDataRow);
                this.Controls.Add(btnOK);
                this.Controls.Add(btnCancel);
            }
        }

        private void ReleaseComObject(object obj)
        {
            try
            {
                System.Runtime.InteropServices.Marshal.ReleaseComObject(obj);
                obj = null;
            }
            catch (Exception ex)
            {
                obj = null;
                MessageBox.Show("无法释放对象: " + ex.Message);
            }
            finally
            {
                GC.Collect();
            }
        }



        private void button8_Click(object sender, EventArgs e)
        {
            Excel.Application excelApp = Globals.ThisAddIn.Application;
            Excel.Workbook workbook = excelApp.ActiveWorkbook;
            Excel.Worksheet worksheet = workbook.ActiveSheet;

            // **用户输入索引行和数据行**
            InputForm1 inputForm = new InputForm1();
            if (inputForm.ShowDialog() != DialogResult.OK)
            {
                return; // 用户取消
            }

            int indexRow = inputForm.IndexRow;
            List<int> dataRows = inputForm.DataRows; // 解析用户输入的多行数据

            Excel.Range indexRowRange = worksheet.Rows[indexRow];

            // **用户选择 Word 模板**
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Title = "选择 Word 模板文件",
                Filter = "Word 文件 (*.doc;*.docx)|*.doc;*.docx",
                Multiselect = false
            };

            if (openFileDialog.ShowDialog() != DialogResult.OK)
            {
                MessageBox.Show("未选择 Word 模板文件，操作取消！");
                return;
            }
            string templatePath = openFileDialog.FileName;

            // **用户选择保存路径**
            FolderBrowserDialog folderDialog = new FolderBrowserDialog
            {
                Description = "选择 Word 报告保存文件夹"
            };

            if (folderDialog.ShowDialog() != DialogResult.OK)
            {
                MessageBox.Show("未选择保存位置，操作取消！");
                return;
            }
            string saveFolderPath = folderDialog.SelectedPath;

            // **遍历用户输入的数据行**
            foreach (int rowIndex in dataRows)
            {
                Excel.Range dataRowRange = worksheet.Rows[rowIndex];

                Dictionary<string, string> dataMap = new Dictionary<string, string>();

                for (int col = 1; col <= indexRowRange.Columns.Count; col++)
                {
                    string columnName = indexRowRange.Cells[1, col].Text.ToString().Trim();
                    string cellValue = dataRowRange.Cells[1, col].Text.ToString().Trim();
                    if (!string.IsNullOrEmpty(columnName))
                    {
                        dataMap[$"【{columnName}】"] = cellValue;
                    }
                }

                // **生成 Word 报告**
                GenerateWordReport(templatePath, saveFolderPath, dataMap, rowIndex);
            }

            MessageBox.Show($"所有 Word 报告已成功生成！\n保存路径: {saveFolderPath}");
        }

        // **Word 处理函数**
        private void GenerateWordReport(string templatePath, string saveFolderPath, Dictionary<string, string> dataMap, int rowIndex)
        {
            Word.Application wordApp = new Word.Application();
            Word.Document doc = wordApp.Documents.Open(templatePath);
            wordApp.Visible = false; // 后台运行

            // 替换所有占位符
            foreach (var kvp in dataMap)
            {
                FindAndReplace(doc, kvp.Key, kvp.Value);
            }

            // **保存 Word 文档**
            string outputPath = Path.Combine(saveFolderPath, $"报告_第{rowIndex}行.docx");
            doc.SaveAs2(outputPath, Word.WdSaveFormat.wdFormatXMLDocument);
            doc.Close();
            wordApp.Quit();
        }
        public class InputForm1 : Form
        {
            private TextBox txtIndexRow;
            private TextBox txtDataRows;
            private Button btnOK;
            private Button btnCancel;

            public int IndexRow { get; private set; }
            public List<int> DataRows { get; private set; } = new List<int>();

            public InputForm1()
            {
                this.Text = "输入索引行和数据行";
                this.Width = 400;
                this.Height = 200;
                this.FormBorderStyle = FormBorderStyle.FixedDialog;
                this.StartPosition = FormStartPosition.CenterScreen;

                Label lblIndexRow = new Label { Text = "索引行:", Left = 10, Top = 20, Width = 80 };
                txtIndexRow = new TextBox { Left = 100, Top = 20, Width = 250 };

                Label lblDataRows = new Label { Text = "数据行 (逗号分隔):", Left = 10, Top = 60, Width = 150 };
                txtDataRows = new TextBox { Left = 160, Top = 60, Width = 190 };

                btnOK = new Button { Text = "确定", Left = 70, Width = 80, Top = 120 };
                btnOK.Click += BtnOK_Click;

                btnCancel = new Button { Text = "取消", Left = 180, Width = 80, Top = 120 };
                btnCancel.Click += (sender, e) => { this.DialogResult = DialogResult.Cancel; this.Close(); };

                this.Controls.Add(lblIndexRow);
                this.Controls.Add(txtIndexRow);
                this.Controls.Add(lblDataRows);
                this.Controls.Add(txtDataRows);
                this.Controls.Add(btnOK);
                this.Controls.Add(btnCancel);
            }

            private void BtnOK_Click(object sender, EventArgs e)
            {
                if (!int.TryParse(txtIndexRow.Text, out int indexRow) || indexRow <= 0)
                {
                    MessageBox.Show("请输入有效的索引行号！");
                    return;
                }

                List<int> dataRows = new List<int>();
                string[] rowStrings = txtDataRows.Text.Split(new char[] { ',', '，' }, StringSplitOptions.RemoveEmptyEntries);

                foreach (string rowStr in rowStrings)
                {
                    if (int.TryParse(rowStr.Trim(), out int rowNum) && rowNum > 0)
                    {
                        dataRows.Add(rowNum);
                    }
                }

                if (dataRows.Count == 0)
                {
                    MessageBox.Show("请输入至少一个有效的数据行号！");
                    return;
                }

                IndexRow = indexRow;
                DataRows = dataRows;
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void button9_Click(object sender, EventArgs e)
        {
            Excel.Application excelApp = Globals.ThisAddIn.Application;
            Excel.Workbook workbook = excelApp.ActiveWorkbook;
            Excel.Worksheet worksheet = workbook.ActiveSheet;
            Excel.Range selectedCell = excelApp.Selection as Excel.Range;

            if (selectedCell == null)
            {
                MessageBox.Show("请先选择一个 Excel 单元格所在的列！");
                return;
            }

            // 获取当前选中列
            int selectedColumn = selectedCell.Column;

            // 让用户输入起始行号
            InputForm2 inputForm = new InputForm2(2);
            if (inputForm.ShowDialog() != DialogResult.OK)
            {
                return; // 用户取消操作
            }

            int startRow = inputForm.IndexRow;

            // 从奇数列和偶数列读取数据，按行遍历
            Dictionary<string, string> dataMap = new Dictionary<string, string>();
            int maxRow = worksheet.UsedRange.Rows.Count;
            int maxCol = worksheet.UsedRange.Columns.Count;

            for (int row = startRow; row <= maxRow; row++)
            {
                for (int col = 1; col < maxCol; col += 2)
                {
                    string key = worksheet.Cells[row, col].Text.ToString().Trim();
                    string value = worksheet.Cells[row, col + 1].Text.ToString().Trim();
                    if (!string.IsNullOrEmpty(key))
                    {
                        dataMap[$"【{key}】"] = value;
                    }
                }
            }

            // 选择 Word 模板
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Title = "选择 Word 模板文件",
                Filter = "Word 文件 (*.doc;*.docx)|*.doc;*.docx",
                Multiselect = false
            };

            if (openFileDialog.ShowDialog() != DialogResult.OK)
            {
                MessageBox.Show("未选择 Word 模板文件，操作取消！");
                return;
            }
            string templatePath = openFileDialog.FileName;

            // 选择保存路径
            SaveFileDialog saveFileDialog = new SaveFileDialog
            {
                Title = "保存生成的 Word 报告",
                Filter = "Word 2007+ (*.docx)|*.docx|Word 97-2003 (*.doc)|*.doc",
                DefaultExt = "docx",
                FileName = "生成报告"
            };

            if (saveFileDialog.ShowDialog() != DialogResult.OK)
            {
                MessageBox.Show("未选择保存位置，操作取消！");
                return;
            }
            string outputPath = saveFileDialog.FileName;
            string fileExt = Path.GetExtension(outputPath).ToLower();

            // Word 保存格式
            object fileFormat;
            if (fileExt == ".docx")
                fileFormat = Word.WdSaveFormat.wdFormatXMLDocument;
            else if (fileExt == ".doc")
                fileFormat = Word.WdSaveFormat.wdFormatDocument97;
            else
            {
                MessageBox.Show("不支持的文件格式！");
                return;
            }

            // 打开 Word 模板
            Word.Application wordApp = new Word.Application();
            Word.Document doc = wordApp.Documents.Open(templatePath);
            wordApp.Visible = false;

            // 提取占位符并替换
            List<string> placeholderList = ExtractPlaceholders2(doc);
            foreach (string placeholder in placeholderList)
            {
                string value = dataMap.ContainsKey(placeholder) ? dataMap[placeholder] : null;
                FindAndReplace2(doc, placeholder, value, highlightIfMissing: true);
            }

            // 保存并关闭
            doc.SaveAs2(outputPath, fileFormat);
            doc.Close(false);
            wordApp.Quit();

            MessageBox.Show($"Word 报告已成功生成！\n{outputPath}");
        }

        // 替换占位符，如果值为空则加粗并标黄
        private void FindAndReplace2(Word.Document doc, string findText, string replaceText, bool highlightIfMissing = false)
        {
            Word.Range range = doc.Content;
            while (true)
            {
                Word.Find find = range.Find;
                find.ClearFormatting();
                find.Text = findText;
                find.Forward = true;
                find.Wrap = Word.WdFindWrap.wdFindStop;

                if (find.Execute())
                {
                    if (!string.IsNullOrEmpty(replaceText))
                    {
                        range.Text = replaceText;
                    }
                    else if (highlightIfMissing)
                    {
                        range.Font.Bold = 1;
                        range.HighlightColorIndex = Word.WdColorIndex.wdYellow;
                    }

                    // Move range to after the replaced text to continue searching
                    range.Start = range.End;
                    range.End = doc.Content.End;
                }
                else
                {
                    break;
                }
            }
        }


        // 提取文档中所有【xxx】格式的占位符
        private List<string> ExtractPlaceholders2(Word.Document doc)
        {
            List<string> placeholders = new List<string>();
            string pattern = "【(.*?)】";
            MatchCollection matches = Regex.Matches(doc.Content.Text, pattern);

            foreach (Match match in matches)
            {
                string placeholder = match.Value;
                if (!placeholders.Contains(placeholder))
                {
                    placeholders.Add(placeholder);
                }
            }
            return placeholders;
        }

        // 自定义输入窗口（用于输入起始行号）
        public class InputForm2 : Form
        {
            private TextBox txtIndexRow;
            private Button btnOK;
            private Button btnCancel;

            public int IndexRow { get; private set; }

            public InputForm2(int defaultRow)
            {
                this.Text = "设置起始行号（从第几行开始读取）";
                this.Width = 300;
                this.Height = 140;
                this.FormBorderStyle = FormBorderStyle.FixedDialog;
                this.StartPosition = FormStartPosition.CenterScreen;

                Label lblIndexRow = new Label { Text = "起始行号:", Left = 10, Top = 20, Width = 80 };
                txtIndexRow = new TextBox { Left = 100, Top = 20, Width = 150, Text = defaultRow.ToString() };

                btnOK = new Button { Text = "确定", Left = 50, Width = 80, Top = 60 };
                btnOK.Click += (sender, e) =>
                {
                    if (int.TryParse(txtIndexRow.Text, out int index))
                    {
                        IndexRow = index;
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                    else
                    {
                        MessageBox.Show("请输入有效的行号！");
                    }
                };

                btnCancel = new Button { Text = "取消", Left = 150, Width = 80, Top = 60 };
                btnCancel.Click += (sender, e) => { this.DialogResult = DialogResult.Cancel; this.Close(); };

                this.Controls.Add(lblIndexRow);
                this.Controls.Add(txtIndexRow);
                this.Controls.Add(btnOK);
                this.Controls.Add(btnCancel);
            }
        }
    }
}
