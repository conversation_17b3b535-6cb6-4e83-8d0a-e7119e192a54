using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using Excel = Microsoft.Office.Interop.Excel;
using Word = Microsoft.Office.Interop.Word;

namespace ExcelToWord
{
    /// <summary>
    /// Excel报告生成器核心类
    /// 负责处理从Excel数据自动生成Word报告的功能
    /// </summary>
    public class ExcelReportGenerator
    {
        /// <summary>
        /// 生成Excel报告的主要方法
        /// </summary>
        /// <param name="worksheet">Excel工作表</param>
        /// <param name="indexRow">索引行号</param>
        /// <param name="templatePath">Word模板路径</param>
        /// <param name="saveFolderPath">保存文件夹路径</param>
        public static void GenerateReports(Excel.Worksheet worksheet, int indexRow, string templatePath, string saveFolderPath)
        {
            try
            {
                // 1. 读取索引行的字段名
                var fieldNames = ReadIndexRowFields(worksheet, indexRow);
                if (fieldNames.Count == 0)
                {
                    MessageBox.Show("索引行中没有找到有效的字段名！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 2. 自动检测数据行
                var dataRows = DetectDataRows(worksheet, indexRow);
                if (dataRows.Count == 0)
                {
                    MessageBox.Show("没有找到有效的数据行！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 3. 检查Word模板中的占位符
                var templatePlaceholders = GetTemplatePlaceholders(templatePath);
                if (templatePlaceholders.Count == 0)
                {
                    MessageBox.Show("Word模板中没有找到【】格式的占位符！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }

                // 4. 显示匹配信息给用户确认
                var matchInfo = GetFieldMatchInfo(fieldNames, templatePlaceholders);
                string confirmMessage = $"检测到：\n" +
                                      $"- 索引行字段：{fieldNames.Count}个\n" +
                                      $"- 数据行：{dataRows.Count}行\n" +
                                      $"- 模板占位符：{templatePlaceholders.Count}个\n" +
                                      $"- 匹配字段：{matchInfo.MatchedCount}个\n\n" +
                                      $"将生成 {dataRows.Count} 个报告文件，是否继续？";

                if (MessageBox.Show(confirmMessage, "确认生成", MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
                {
                    return;
                }

                // 5. 批量生成报告
                int successCount = 0;
                foreach (int dataRowIndex in dataRows)
                {
                    try
                    {
                        var dataMap = BuildDataMap(worksheet, indexRow, dataRowIndex, fieldNames);
                        string fileName = GenerateFileName(worksheet, dataRowIndex, dataMap);
                        string outputPath = Path.Combine(saveFolderPath, fileName);
                        
                        GenerateSingleReport(templatePath, outputPath, dataMap);
                        successCount++;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"生成第{dataRowIndex}行报告失败：{ex.Message}", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }

                // 6. 显示完成信息
                MessageBox.Show($"报告生成完成！\n" +
                              $"成功生成：{successCount}个文件\n" +
                              $"保存位置：{saveFolderPath}", 
                              "生成完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成报告时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 读取索引行的字段名
        /// </summary>
        private static List<string> ReadIndexRowFields(Excel.Worksheet worksheet, int indexRow)
        {
            var fieldNames = new List<string>();
            Excel.Range indexRowRange = worksheet.Rows[indexRow];
            
            // 获取有效的列数
            Excel.Range usedRange = worksheet.UsedRange;
            int maxCol = usedRange.Columns.Count;

            for (int col = 1; col <= maxCol; col++)
            {
                object cellValue = indexRowRange.Cells[1, col].Value2;
                string fieldName = cellValue?.ToString()?.Trim();
                
                if (!string.IsNullOrEmpty(fieldName))
                {
                    fieldNames.Add(fieldName);
                }
                else
                {
                    // 如果遇到空列，停止读取
                    break;
                }
            }

            return fieldNames;
        }

        /// <summary>
        /// 自动检测数据行（从索引行的下一行开始，直到遇到空行）
        /// </summary>
        private static List<int> DetectDataRows(Excel.Worksheet worksheet, int indexRow)
        {
            var dataRows = new List<int>();
            Excel.Range usedRange = worksheet.UsedRange;
            int maxRow = usedRange.Rows.Count;
            
            // 从索引行的下一行开始检测
            for (int row = indexRow + 1; row <= maxRow; row++)
            {
                // 检查这一行是否为空行
                if (IsEmptyRow(worksheet, row))
                {
                    break; // 遇到空行就停止
                }
                
                dataRows.Add(row);
            }

            return dataRows;
        }

        /// <summary>
        /// 检查指定行是否为空行
        /// </summary>
        private static bool IsEmptyRow(Excel.Worksheet worksheet, int rowIndex)
        {
            Excel.Range usedRange = worksheet.UsedRange;
            int maxCol = usedRange.Columns.Count;

            for (int col = 1; col <= maxCol; col++)
            {
                object cellValue = worksheet.Cells[rowIndex, col].Value2;
                if (cellValue != null && !string.IsNullOrWhiteSpace(cellValue.ToString()))
                {
                    return false; // 找到非空单元格，不是空行
                }
            }

            return true; // 所有单元格都为空，是空行
        }

        /// <summary>
        /// 获取Word模板中的占位符
        /// </summary>
        private static List<string> GetTemplatePlaceholders(string templatePath)
        {
            var placeholders = new List<string>();
            Word.Application wordApp = null;
            Word.Document doc = null;

            try
            {
                wordApp = new Word.Application();
                doc = wordApp.Documents.Open(templatePath, ReadOnly: true, Visible: false);
                wordApp.Visible = false;

                string pattern = "【(.*?)】";
                MatchCollection matches = Regex.Matches(doc.Content.Text, pattern);

                foreach (Match match in matches)
                {
                    string placeholder = match.Value;
                    if (!placeholders.Contains(placeholder))
                    {
                        placeholders.Add(placeholder);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"读取Word模板时出错：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                try
                {
                    doc?.Close(false);
                    wordApp?.Quit(false);
                    if (doc != null) System.Runtime.InteropServices.Marshal.ReleaseComObject(doc);
                    if (wordApp != null) System.Runtime.InteropServices.Marshal.ReleaseComObject(wordApp);
                }
                catch { }
            }

            return placeholders;
        }

        /// <summary>
        /// 获取字段匹配信息
        /// </summary>
        private static FieldMatchInfo GetFieldMatchInfo(List<string> fieldNames, List<string> templatePlaceholders)
        {
            var matchInfo = new FieldMatchInfo();
            
            foreach (string fieldName in fieldNames)
            {
                string placeholder = $"【{fieldName}】";
                if (templatePlaceholders.Contains(placeholder))
                {
                    matchInfo.MatchedFields.Add(fieldName);
                    matchInfo.MatchedCount++;
                }
                else
                {
                    matchInfo.UnmatchedFields.Add(fieldName);
                }
            }

            return matchInfo;
        }

        /// <summary>
        /// 构建数据映射字典
        /// </summary>
        private static Dictionary<string, string> BuildDataMap(Excel.Worksheet worksheet, int indexRow, int dataRowIndex, List<string> fieldNames)
        {
            var dataMap = new Dictionary<string, string>();
            Excel.Range indexRowRange = worksheet.Rows[indexRow];
            Excel.Range dataRowRange = worksheet.Rows[dataRowIndex];

            for (int col = 1; col <= fieldNames.Count; col++)
            {
                string fieldName = fieldNames[col - 1];
                Excel.Range dataCell = dataRowRange.Cells[1, col];
                object cellValueObj = dataCell.Value2;
                
                string cellValue = Ribbon1.FormatCellValue(cellValueObj, dataCell);
                string placeholder = $"【{fieldName}】";
                
                dataMap[placeholder] = cellValue;
            }

            return dataMap;
        }

        /// <summary>
        /// 生成文件名
        /// </summary>
        private static string GenerateFileName(Excel.Worksheet worksheet, int dataRowIndex, Dictionary<string, string> dataMap)
        {
            // 尝试使用第一个字段作为文件名的一部分
            string identifier = $"第{dataRowIndex}行";
            
            if (dataMap.Count > 0)
            {
                var firstValue = dataMap.Values.FirstOrDefault();
                if (!string.IsNullOrEmpty(firstValue) && firstValue.Length <= 20)
                {
                    // 清理文件名中的非法字符
                    string cleanValue = CleanFileName(firstValue);
                    identifier = $"{cleanValue}_第{dataRowIndex}行";
                }
            }

            return $"Excel报告_{identifier}_{DateTime.Now:yyyyMMdd_HHmmss}.docx";
        }

        /// <summary>
        /// 清理文件名中的非法字符
        /// </summary>
        private static string CleanFileName(string fileName)
        {
            char[] invalidChars = Path.GetInvalidFileNameChars();
            foreach (char c in invalidChars)
            {
                fileName = fileName.Replace(c, '_');
            }
            return fileName;
        }

        /// <summary>
        /// 生成单个报告
        /// </summary>
        private static void GenerateSingleReport(string templatePath, string outputPath, Dictionary<string, string> dataMap)
        {
            Word.Application wordApp = null;
            Word.Document doc = null;

            try
            {
                wordApp = new Word.Application();
                doc = wordApp.Documents.Open(templatePath, ReadOnly: false, Visible: false);
                wordApp.Visible = false;

                // 直接使用当前文档的占位符，避免重复打开文件
                string pattern = "【(.*?)】";
                MatchCollection matches = Regex.Matches(doc.Content.Text, pattern);
                var placeholders = new List<string>();

                foreach (Match match in matches)
                {
                    string placeholder = match.Value;
                    if (!placeholders.Contains(placeholder))
                    {
                        placeholders.Add(placeholder);
                    }
                }

                // 替换占位符
                foreach (string placeholder in placeholders)
                {
                    string value = dataMap.ContainsKey(placeholder) ? dataMap[placeholder] : null;
                    FindAndReplace(doc, placeholder, value, highlightIfMissing: true);
                }

                // 保存文档
                doc.SaveAs2(outputPath, Word.WdSaveFormat.wdFormatXMLDocument);
            }
            catch (Exception ex)
            {
                throw new Exception($"生成报告文件失败：{ex.Message}");
            }
            finally
            {
                try
                {
                    doc?.Close(false);
                    wordApp?.Quit(false);
                    if (doc != null) System.Runtime.InteropServices.Marshal.ReleaseComObject(doc);
                    if (wordApp != null) System.Runtime.InteropServices.Marshal.ReleaseComObject(wordApp);
                }
                catch { }
            }
        }

        /// <summary>
        /// 查找并替换Word文档中的占位符
        /// </summary>
        private static void FindAndReplace(Word.Document doc, string findText, string replaceText, bool highlightIfMissing = false)
        {
            Word.Range range = doc.Content;
            Word.Find findObject = range.Find;

            findObject.ClearFormatting();
            findObject.Text = findText;
            findObject.Replacement.ClearFormatting();
            findObject.Forward = true;
            findObject.Wrap = Word.WdFindWrap.wdFindStop;

            while (findObject.Execute())
            {
                Word.Range foundRange = doc.Range(findObject.Parent.Start, findObject.Parent.End);

                if (!string.IsNullOrEmpty(replaceText))
                {
                    // 替换文本
                    foundRange.Text = replaceText;

                    if (replaceText == "0" || replaceText == "-")
                    {
                        foundRange.Font.Bold = 1;
                        foundRange.HighlightColorIndex = Word.WdColorIndex.wdYellow;
                    }
                }
                else if (highlightIfMissing)
                {
                    // 无替换内容时高亮占位符
                    foundRange.Font.Bold = 1;
                    foundRange.HighlightColorIndex = Word.WdColorIndex.wdYellow;
                }
            }
        }
    }

    /// <summary>
    /// 字段匹配信息类
    /// </summary>
    public class FieldMatchInfo
    {
        public List<string> MatchedFields { get; set; } = new List<string>();
        public List<string> UnmatchedFields { get; set; } = new List<string>();
        public int MatchedCount { get; set; } = 0;
    }
}
