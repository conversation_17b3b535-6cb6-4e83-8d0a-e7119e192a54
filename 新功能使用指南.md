# 🎉 新功能：生成Excel报告

## 功能位置
在Excel的"中联五洲"选项卡 → "生成报告"组 → "生成Excel报告"按钮

## 🚀 快速开始

### 步骤1：准备Excel数据
确保您的Excel数据格式如下：
```
第1行: （可选标题）
第2行: 姓名    性别    年龄    部门     ← 索引行（字段名）
第3行: 张三    男      25      技术部   ← 数据行1
第4行: 李四    女      28      销售部   ← 数据行2  
第5行: 王五    男      30      财务部   ← 数据行3
第6行: （空行）                        ← 自动停止检测
```

### 步骤2：准备Word模板
创建Word模板，使用【】格式占位符：
```
员工档案

姓名：【姓名】
性别：【性别】  
年龄：【年龄】
部门：【部门】
```

### 步骤3：执行生成
1. 点击"生成Excel报告"按钮
2. 在预览窗口中点击选择索引行（如第2行）
3. 确认字段和数据行信息
4. 选择Word模板文件
5. 选择保存文件夹
6. 等待生成完成

## ✨ 功能特点

### 🤖 全自动检测
- **自动检测数据行**：从索引行下一行开始，遇到空行自动停止
- **智能字段匹配**：Excel字段名自动匹配Word模板占位符
- **批量生成**：一次操作生成多个报告文件

### 👀 可视化操作
- **数据预览**：显示Excel数据的前20行供选择
- **实时反馈**：显示识别的字段数量和数据行数量
- **确认信息**：生成前显示详细的匹配信息

### 🛡️ 错误处理
- **高亮未匹配字段**：Word模板中没有对应Excel字段的占位符会被高亮
- **详细错误提示**：每个步骤都有清晰的错误提示
- **容错处理**：单个文件生成失败不影响其他文件

## 📊 生成结果

### 文件命名规则
```
Excel报告_张三_第3行_20250811_143022.docx
Excel报告_李四_第4行_20250811_143023.docx
Excel报告_王五_第5行_20250811_143024.docx
```

### 内容替换
- ✅ 匹配的字段：【姓名】→ 张三
- ✅ 匹配的字段：【性别】→ 男
- ⚠️ 未匹配字段：【联系方式】→ 保持原样并高亮显示

## 🎯 适用场景
- 员工档案批量生成
- 客户资料报告制作
- 产品信息单页生成
- 证书批量制作
- 学生成绩单生成
- 任何"一行数据一个文档"的需求

## 💡 使用技巧

### 数据准备技巧
1. **索引行要清晰**：确保字段名简洁明了
2. **数据要连续**：避免中间出现空行
3. **格式要统一**：同一列的数据格式保持一致

### 模板设计技巧
1. **占位符格式**：必须使用【字段名】格式
2. **字段名匹配**：确保与Excel中的字段名完全一致
3. **预留未匹配**：可以在模板中添加Excel中没有的字段，会被高亮提示

## 🔧 故障排除

### 常见问题
1. **没有检测到数据行**
   - 检查索引行下方是否有数据
   - 确保数据行不全为空

2. **字段匹配数量少**
   - 检查Excel字段名与Word占位符是否一致
   - 注意中英文字符和空格

3. **生成失败**
   - 确保Word模板文件可以正常打开
   - 检查保存文件夹的写入权限

### 性能提示
- 建议一次处理不超过100行数据
- 复杂的Word模板可能影响生成速度
- 生成过程中请勿操作Excel和Word

这个功能大大提升了批量报告生成的效率，特别适合需要处理大量相似数据的办公场景！
