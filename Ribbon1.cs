using Microsoft.Office.Tools.Ribbon;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using Excel = Microsoft.Office.Interop.Excel;
using Word = Microsoft.Office.Interop.Word;
using Microsoft.VisualBasic;

namespace ExcelToWord
{
    public partial class Ribbon1
    {
        public Excel.Application Excelapp;
        public Word.Application WordApp;




        private void Ribbon1_Load(object sender, RibbonUIEventArgs e)
        {
            string machineCode = MachineCodeHelper.GetMachineCode();
            string license = LicenseHelper.LoadLicense();
            DateTime? regDate = LicenseHelper.LoadRegisterTime();

            // 已注册且未过期
            if (!string.IsNullOrEmpty(license) &&
                LicenseHelper.ValidateLicense(machineCode, license) &&
                regDate.HasValue &&
                (DateTime.Now - regDate.Value).TotalDays <= 365)
            {
                int left = 365 - (int)(DateTime.Now - regDate.Value).TotalDays;
                trialDaysButton.Label = string.Format("剩余 {0} 天有效", left);
                return;
            }

            // 已注册但已过期 → 恢复试用
            if (!string.IsNullOrEmpty(license) &&
                LicenseHelper.ValidateLicense(machineCode, license))
            {
                MessageBox.Show("注册已过期，已自动恢复试用模式。", "注册过期", MessageBoxButtons.OK, MessageBoxIcon.Information);
                // 可以选择清除注册信息（看你需求）
                // LicenseHelper.ClearLicense();
            }

            // 试用中
            int trialLeft = TrialHelper.GetRemainingDays();
            if (trialLeft > 0)
            {
                trialDaysButton.Label = string.Format("试用剩余 {0} 天", trialLeft);
                return;
            }

            // 试用期已结束
            trialDaysButton.Label = "试用已结束";
            MessageBox.Show("试用期已结束，请注册后继续使用。", "试用结束", MessageBoxButtons.OK, MessageBoxIcon.Warning);

            string showCode = string.Format("您的机器码：\n\n{0}\n\n请将此码发送给作者获取注册码。", machineCode);
            MessageBox.Show(showCode, "注册提示", MessageBoxButtons.OK, MessageBoxIcon.Information);

            string input = Microsoft.VisualBasic.Interaction.InputBox("请输入注册码", "注册");

            if (!LicenseHelper.ValidateLicense(machineCode, input))
            {
                MessageBox.Show("注册码无效，程序将以限制模式运行。", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                DisableAllButtons();
                return;
            }

            // 注册成功
            LicenseHelper.SaveLicense(input);
            LicenseHelper.SaveRegisterTime(DateTime.Now);  // ⏰ 保存注册时间
            MessageBox.Show("注册成功！感谢支持！", "验证通过", MessageBoxButtons.OK, MessageBoxIcon.Information);
            trialDaysButton.Label = "剩余 365 天有效";
        }



        private void ShowMachineCodeDialog(string machineCode)
        {
            using (Form form = new Form())
            {
                form.Text = "您的机器码";
                form.Width = 400;
                form.Height = 180;

                TextBox textBox = new TextBox();
                textBox.Multiline = true;
                textBox.ReadOnly = true;
                textBox.Text = string.Format("请将以下机器码发送给管理员以获取注册码：\r\n\r\n{0}", machineCode);
                textBox.Dock = DockStyle.Fill;
                textBox.ScrollBars = ScrollBars.Vertical;

                form.Controls.Add(textBox);
                form.ShowDialog();
            }
        }



        private void registerButton_Click(object sender, RibbonControlEventArgs e)
        {
            string machineCode = MachineCodeHelper.GetMachineCode();

            if (LicenseHelper.IsLicenseValid(machineCode))
            {
                MessageBox.Show("已注册，无需再次输入注册码。", "注册提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            ShowMachineCodeDialog(machineCode);

            string input = Microsoft.VisualBasic.Interaction.InputBox("请输入注册码", "注册");

            if (!LicenseHelper.ValidateLicense(machineCode, input))
            {
                MessageBox.Show("注册码无效，请重试或联系管理员。", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Error);

                if (TrialHelper.GetRemainingDays() <= 0)
                {
                    DisableAllButtons();
                }

                return;
            }

            LicenseHelper.SaveLicense(input);
            LicenseHelper.SaveRegisterTime(DateTime.Now);
            trialDaysButton.Label = "剩余 365 天有效";
            MessageBox.Show("注册成功！感谢支持！", "验证通过", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }



        private void DisableAllButtons()
        {
            button1.Enabled = false;
            button2.Enabled = false;
            button3.Enabled = false;
            button4.Enabled = false;
            button5.Enabled = false;
            button6.Enabled = false;
            button7.Enabled = false;
            button8.Enabled = false;
            button9.Enabled = false;
            button10.Enabled = false;
            button11.Enabled = false;
            button12.Enabled = false;
            button13.Enabled = false;
            button14.Enabled = false;
            button15.Enabled = false;
            button16.Enabled = false;
            buttonTranspose.Enabled = false;
            buttonExpenseMerge.Enabled = false;
            buttonXianSummary.Enabled = false;
            buttonFixedSummary.Enabled = false;
            buttonExcelReport.Enabled = false;
        }

        //窗体显示
        private void button1_Click(object sender, RibbonControlEventArgs e)
        {
            Form1 myForm = new Form1();
            myForm.Show(); // 非模态显示窗体
        }

        //private void button2_Click(object sender, RibbonControlEventArgs e)
        //{

        //    // 创建 OpenFileDialog 实例
        //    OpenFileDialog openFileDialog = new OpenFileDialog();
        //    openFileDialog.Filter = "Word Templates|*.dotx;*.docx"; // 设置文件过滤器，只显示 Word 模板文件
        //    openFileDialog.Title = "Select a Word Template"; // 设置对话框标题

        //    // 显示对话框并检查用户是否选择了文件
        //    if (openFileDialog.ShowDialog() == DialogResult.OK)
        //    {
        //        string templatePath = openFileDialog.FileName; // 获取用户选择的文件路径

        //        // 创建一个新的 Word 应用程序实例
        //        WordApp = new Word.Application();

        //        // 显示 Word 应用程序
        //        WordApp.Visible = true;

        //        // 检查模板文件是否存在
        //        if (System.IO.File.Exists(templatePath))
        //        {
        //            // 打开指定的 Word 模板
        //            Word.Document doc = WordApp.Documents.Add(templatePath);
        //        }
        //        else
        //        {
        //            MessageBox.Show("模板文件不存在，请检查路径是否正确。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        //        }
        //    }

        //}

        //占位符修改
        private void button5_Click(object sender, RibbonControlEventArgs e)
        {
            // 获取当前 Excel 应用实例
            Excel.Application excelApp = Globals.ThisAddIn.Application;

            // 检查是否有选中的范围
            if (excelApp.Selection is Excel.Range selectedRange)
            {
                try
                {
                    // 获取 Word 应用实例
                    Word.Application wordApp = null;
                    Word.Document wordDoc = null;

                    // 获取已打开的 Word 文档
                    try
                    {
                        wordApp = (Word.Application)System.Runtime.InteropServices.Marshal.GetActiveObject("Word.Application");
                        wordDoc = wordApp.ActiveDocument;
                    }
                    catch
                    {
                        MessageBox.Show("未找到已打开的Word文档，请先打开一个Word文档！");
                        return;
                    }

                    // 获取 Word 文档文本
                    string wordText = wordDoc.Content.Text;
                    Regex regex = new Regex(@"\{\$([A-Z]+\$?\d+)\}"); // 识别 Excel 单元格格式的占位符
                    MatchCollection matches = regex.Matches(wordText);

                    foreach (Match match in matches)
                    {
                        string cellAddress = match.Groups[1].Value; // 获取 Excel 单元格地址
                        Excel.Range cellRange = selectedRange.Worksheet.Range[cellAddress];

                        if (cellRange != null)
                        {
                            // 复制 Excel 单元格数据到剪贴板
                            cellRange.Copy();

                            // 在 Word 文档中查找占位符
                            Word.Find find = wordDoc.Content.Find;
                            find.Text = match.Value;
                            find.Execute();

                            if (find.Found)
                            {
                                // 获取找到的占位符位置
                                Word.Range wordRange = find.Parent as Word.Range;

                                // 设置粘贴方式为带链接的粘贴
                                object link = true;
                                object dataType = Word.WdPasteDataType.wdPasteText;

                                // 粘贴 Excel 数据，并保持链接
                                wordRange.PasteSpecial(Link: ref link, DataType: ref dataType);
                            }
                        }
                    }

                    // 释放 COM 对象
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(wordDoc);
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(wordApp);

                    MessageBox.Show("Word文档已更新，并插入了Excel链接数据");
                }
                catch (Exception ex)
                {
                    MessageBox.Show("操作失败: " + ex.Message);
                }
            }
            else
            {
                MessageBox.Show("请先选中一个表格范围！");
            }

        }

        private void button11_Click(object sender, RibbonControlEventArgs e)
        {
            MessageBox.Show("版本 1.8.0.8\n中联五洲工程咨询有限公司 ©版权所有\n联系人：胡庆海15241217499");
        }

        //带连接插入
        private void button8_Click(object sender, RibbonControlEventArgs e)
        {
            Excel.Application excelApp = Globals.ThisAddIn.Application;

            // 检查是否有选中的范围
            if (excelApp.Selection is Excel.Range selectedRange)
            {
                try
                {
                    Word.Application wordApp = null;
                    Word.Document wordDoc = null;

                    try
                    {
                        wordApp = (Word.Application)System.Runtime.InteropServices.Marshal.GetActiveObject("Word.Application");
                        wordDoc = wordApp.ActiveDocument;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("未找到已打开的Word文档，请先打开一个Word文档！ 错误信息: " + ex.Message);
                        return;
                    }

                    // 尝试减少内存占用，先复制到剪贴板
                    selectedRange.Copy();

                    Word.Range wordRange = wordApp.Selection.Range;

                    if (selectedRange.Count == 1)
                    {
                        object link = true;
                        object dataType = Word.WdPasteDataType.wdPasteText;
                        wordRange.PasteSpecial(Link: ref link, DataType: ref dataType);
                    }
                    else
                    {
                        object link = true;
                        object dataType = Word.WdPasteDataType.wdPasteRTF;
                        wordRange.PasteSpecial(Link: ref link, DataType: ref dataType);
                    }

                    // 清理资源
                    ReleaseComObject(wordRange);
                    ReleaseComObject(wordDoc);
                    ReleaseComObject(wordApp);

                    GC.Collect(); // 强制垃圾回收

                    MessageBox.Show("内容已插入到Word文档中！");
                }
                catch (Exception ex)
                {
                    MessageBox.Show("操作失败: " + ex.Message);
                }
            }
            else
            {
                MessageBox.Show("请先选中一个表格范围或单元格！");
            }
        }

        private void ReleaseComObject(object obj)
        {
            try
            {
                System.Runtime.InteropServices.Marshal.ReleaseComObject(obj);
                obj = null;
            }
            catch (Exception ex)
            {
                obj = null;
                MessageBox.Show("无法释放对象: " + ex.Message);
            }
            finally
            {
                GC.Collect();
            }
        }

        private void button7_Click(object sender, RibbonControlEventArgs e)
        {
            // 获取当前Excel应用程序实例
            Excel.Application excelApp = Globals.ThisAddIn.Application;

            // 检查是否有选中的范围
            if (excelApp.Selection is Excel.Range selectedRange)
            {
                try
                {
                    // 获取Word应用程序实例
                    Word.Application wordApp = null;
                    Word.Document wordDoc = null;

                    // 尝试获取当前已经打开的Word文档
                    try
                    {
                        wordApp = (Word.Application)System.Runtime.InteropServices.Marshal.GetActiveObject("Word.Application");
                        wordDoc = wordApp.ActiveDocument; // 获取当前活动的Word文档
                    }
                    catch
                    {
                        MessageBox.Show("未找到已打开的Word文档，请先打开一个Word文档！");
                        return;
                    }

                    // 获取选定范围的地址
                    string cellAddress = selectedRange.Address;

                    // 在Word文档的光标位置插入占位符
                    Word.Range wordRange = wordApp.Selection.Range;
                    wordRange.Text += string.Format("{{{0}}}", cellAddress);

                    // 释放资源
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(wordRange);
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(wordDoc);
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(wordApp);

                    MessageBox.Show("表格地址已插入到Word文档中！");
                }
                catch (Exception ex)
                {
                    MessageBox.Show("操作失败: " + ex.Message);
                }
            }
            else
            {
                MessageBox.Show("请先选中一个表格范围！");
            }
            //excelApp.Quit();
        }

        //链接修改
        private void button6_Click(object sender, RibbonControlEventArgs e)
        {
            Microsoft.Office.Interop.Word.Application wordApp = null;
            try
            {
                // 获取正在运行的Word实例
                wordApp = (Microsoft.Office.Interop.Word.Application)System.Runtime.InteropServices.Marshal.GetActiveObject("Word.Application");
            }
            catch (COMException)
            {
                MessageBox.Show("请先打开Microsoft Word。");
                return;
            }

            try
            {
                var doc = wordApp.ActiveDocument;
                if (doc != null)
                {
                    // 全选文档内容
                    wordApp.Selection.WholeStory();

                    // 更新所有字段（替代按F9的效果）
                    doc.Fields.Update();

                    MessageBox.Show("文档已全选并更新完成！");
                }
                else
                {
                    MessageBox.Show("未找到活动的Word文档。");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("操作失败：" + ex.Message);
            }
            finally
            {
                // 清理COM对象
                if (wordApp != null)
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(wordApp);
            }


        }
        //一对一生成报告
        private void button3_Click(object sender, RibbonControlEventArgs e)
        {
            Excel.Application excelApp = Globals.ThisAddIn.Application;
            Excel.Workbook workbook = excelApp.ActiveWorkbook;
            Excel.Worksheet worksheet = workbook.ActiveSheet;
            Excel.Range selectedCell = excelApp.Selection as Excel.Range;

            if (selectedCell == null)
            {
                MessageBox.Show("请先选择一个 Excel 单元格所在的行！");
                return;
            }

            // 让用户输入索引行和数据行
            InputForm inputForm = new InputForm(selectedCell.Row);
            if (inputForm.ShowDialog() != DialogResult.OK)
            {
                return; // 用户取消操作
            }

            int indexRow = inputForm.IndexRow;
            int dataRow = inputForm.DataRow;

            Excel.Range indexRowRange = worksheet.Rows[indexRow];
            Excel.Range dataRowRange = worksheet.Rows[dataRow];

            // 读取索引行和数据行
            Dictionary<string, string> dataMap = new Dictionary<string, string>();
            int colCount = indexRowRange.Columns.Count;

            for (int col = 1; col <= colCount; col++)
            {
                // 使用 Value2 获取实际值，避免列太窄时显示 ####
                object columnNameObj = indexRowRange.Cells[1, col].Value2;
                Excel.Range dataCell = dataRowRange.Cells[1, col];
                object cellValueObj = dataCell.Value2;

                string columnName = (columnNameObj != null) ? columnNameObj.ToString().Trim() : "";
                string cellValue = FormatCellValue(cellValueObj, dataCell); // 传入源单元格以正确处理日期

                if (!string.IsNullOrEmpty(columnName))
                {
                    dataMap[string.Format("【{0}】", columnName)] = cellValue;
                }
            }

            // 选择 Word 模板
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Title = "选择 Word 模板文件",
                Filter = "Word 文件 (*.doc;*.docx)|*.doc;*.docx",
                Multiselect = false
            };

            if (openFileDialog.ShowDialog() != DialogResult.OK)
            {
                MessageBox.Show("未选择 Word 模板文件，操作取消！");
                return;
            }
            string templatePath = openFileDialog.FileName;

            // 选择保存路径
            SaveFileDialog saveFileDialog = new SaveFileDialog
            {
                Title = "保存生成的 Word 报告",
                Filter = "Word 2007+ (*.docx)|*.docx|Word 97-2003 (*.doc)|*.doc",
                DefaultExt = "docx",
                FileName = string.Format("生成报告_{0}", DateTime.Now.ToString("yyyyMMdd_HHmmss"))
            };

            if (saveFileDialog.ShowDialog() != DialogResult.OK)
            {
                MessageBox.Show("未选择保存位置，操作取消！");
                return;
            }
            string outputPath = saveFileDialog.FileName;
            string fileExt = Path.GetExtension(outputPath).ToLower();

            // Word 保存格式
            object fileFormat;
            if (fileExt == ".docx")
                fileFormat = Word.WdSaveFormat.wdFormatXMLDocument;
            else if (fileExt == ".doc")
                fileFormat = Word.WdSaveFormat.wdFormatDocument97;
            else
            {
                MessageBox.Show("不支持的文件格式！");
                return;
            }

            // 打开 Word 并处理替换
            Word.Application wordApp = null;
            Word.Document doc = null;

            try
            {
                wordApp = new Word.Application();
                doc = wordApp.Documents.Open(templatePath, ReadOnly: false, Visible: false);
                wordApp.Visible = false;

                List<string> placeholderList = ExtractPlaceholders(doc);

                foreach (string placeholder in placeholderList)
                {
                    string value = dataMap.ContainsKey(placeholder) ? dataMap[placeholder] : null;
                    FindAndReplace(doc, placeholder, value, highlightIfMissing: true);
                }

                doc.SaveAs2(outputPath, fileFormat);
                MessageBox.Show(string.Format("Word 报告已成功生成！\n{0}", outputPath));
            }
            catch (Exception ex)
            {
                MessageBox.Show("生成 Word 报告时出错：" + ex.Message);
            }
            finally
            {
                if (doc != null)
                {
                    doc.Close(false);
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(doc);
                }

                if (wordApp != null)
                {
                    wordApp.Quit(false);
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(wordApp);
                }
                Marshal.ReleaseComObject(doc);
                Marshal.ReleaseComObject(wordApp);
                GC.Collect();
                GC.WaitForPendingFinalizers();
            }
        }

        /// <summary>
        /// 获取单元格的显示值，专门处理公式单元格和####显示问题
        /// </summary>
        /// <param name="cell">要获取值的单元格</param>
        /// <returns>单元格的实际显示值</returns>
        private static string GetCellDisplayValue(Excel.Range cell)
        {
            if (cell == null) return "";

            try
            {
                // 首先检查是否显示为####，如果是则自动调整列宽
                string displayText = cell.Text?.ToString()?.Trim();
                if (!string.IsNullOrEmpty(displayText) && displayText == "####")
                {
                    try
                    {
                        // 自动调整列宽
                        AutoFitColumnWidth(cell);
                        // 重新获取显示文本
                        displayText = cell.Text?.ToString()?.Trim();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"自动调整列宽失败: {ex.Message}");
                    }
                }

                // 获取单元格的原始值用于格式化
                object cellValue = null;

                // 如果单元格包含公式，获取公式的计算结果
                if (cell.HasFormula)
                {
                    try
                    {
                        // 使用Value属性获取公式的计算结果
                        cellValue = cell.Value;
                        if (cellValue == null)
                        {
                            // 如果Value为空，尝试Value2
                            cellValue = cell.Value2;
                        }
                    }
                    catch
                    {
                        // 如果获取公式结果失败，尝试Value2
                        cellValue = cell.Value2;
                    }
                }
                else
                {
                    // 如果不是公式，直接获取值
                    cellValue = cell.Value2;
                }

                // 如果获取到了值，使用FormatCellValue方法统一格式化
                if (cellValue != null)
                {
                    string formattedValue = FormatCellValue(cellValue, cell);

                    // 确保数值格式化后再次检查是否需要调整列宽
                    if (IsNumericValue(cellValue) && formattedValue.Length > 8)
                    {
                        try
                        {
                            // 对于长数值，确保列宽足够
                            EnsureColumnWidth(cell, formattedValue.Length);
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"调整列宽失败: {ex.Message}");
                        }
                    }

                    return formattedValue;
                }

                // 如果无法获取值，尝试获取显示文本作为备选
                if (!string.IsNullOrEmpty(displayText) && displayText != "####")
                {
                    // 对于显示文本，也尝试解析为数值进行格式化
                    if (double.TryParse(displayText.Replace(",", ""), out double parsedValue))
                    {
                        return FormatCellValue(parsedValue, cell);
                    }
                    return displayText;
                }

                return "";
            }
            catch (Exception ex)
            {
                // 如果所有方法都失败，返回空字符串并记录错误
                System.Diagnostics.Debug.WriteLine($"获取单元格显示值失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 自动调整列宽以适应内容
        /// </summary>
        /// <param name="cell">需要调整列宽的单元格</param>
        private static void AutoFitColumnWidth(Excel.Range cell)
        {
            try
            {
                // 获取当前列
                Excel.Range column = cell.EntireColumn;

                // 自动调整列宽
                column.AutoFit();

                // 如果自动调整后仍然太窄，设置最小宽度
                if (column.ColumnWidth < 12)
                {
                    column.ColumnWidth = 12;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"AutoFit列宽失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 确保列宽足够显示指定长度的内容
        /// </summary>
        /// <param name="cell">单元格</param>
        /// <param name="contentLength">内容长度</param>
        private static void EnsureColumnWidth(Excel.Range cell, int contentLength)
        {
            try
            {
                Excel.Range column = cell.EntireColumn;
                double currentWidth = column.ColumnWidth;

                // 根据内容长度估算需要的宽度（每个字符约1.2个单位宽度）
                double requiredWidth = contentLength * 1.2;

                // 如果当前宽度不够，调整列宽
                if (currentWidth < requiredWidth)
                {
                    column.ColumnWidth = Math.Min(requiredWidth, 50); // 最大不超过50个单位
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"调整列宽失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查值是否为数值类型
        /// </summary>
        /// <param name="value">要检查的值</param>
        /// <returns>是否为数值</returns>
        private static bool IsNumericValue(object value)
        {
            return value is double || value is float || value is decimal ||
                   value is int || value is long || value is short ||
                   (value is string str && double.TryParse(str.Replace(",", ""), out _));
        }

        /// <summary>
        /// 检查字符串是否为数值格式
        /// </summary>
        /// <param name="str">要检查的字符串</param>
        /// <returns>是否为数值格式</returns>
        private static bool IsNumericString(string str)
        {
            if (string.IsNullOrWhiteSpace(str))
                return false;

            // 移除千分号和空格，然后尝试解析
            string cleanStr = str.Replace(",", "").Replace(" ", "").Trim();
            return double.TryParse(cleanStr, out _);
        }

        /// <summary>
        /// 格式化单元格值，对数值类型保留两位小数，正确处理日期格式
        /// </summary>
        /// <param name="cellValue">单元格的原始值</param>
        /// <param name="sourceCell">源单元格对象（可选，用于获取格式信息）</param>
        /// <returns>格式化后的字符串</returns>
        public static string FormatCellValue(object cellValue, Excel.Range sourceCell = null)
        {
            if (cellValue == null)
                return "";

            // 首先尝试将值转换为数值进行格式化（优先处理数值）
            if (TryConvertToNumber(cellValue, out double numericValue))
            {
                // 检查是否为日期格式（只有在确实是日期格式时才特殊处理）
                if (sourceCell != null && IsDateFormat(sourceCell) && IsValidDateValue(numericValue))
                {
                    try
                    {
                        // 如果是日期格式，使用Excel单元格的显示文本（保持原有格式）
                        string displayText = sourceCell.Text?.ToString()?.Trim();
                        if (!string.IsNullOrEmpty(displayText) && displayText != "####")
                        {
                            return displayText; // 返回Excel中显示的格式化日期文本
                        }

                        // 如果Text属性获取失败，则尝试格式化日期值
                        DateTime date = DateTime.FromOADate(numericValue);
                        // 尝试根据Excel的NumberFormat来格式化
                        string numberFormat = sourceCell.NumberFormat?.ToString();
                        if (!string.IsNullOrEmpty(numberFormat))
                        {
                            return FormatDateByExcelFormat(date, numberFormat);
                        }
                        // 默认使用中文日期格式
                        return date.ToString("yyyy年M月d日");
                    }
                    catch
                    {
                        // 如果日期转换失败，继续按数值处理
                    }
                }

                // 所有数值都格式化为带千分号的两位小数
                return numericValue.ToString("N2");
            }

            // 如果不是数值，直接返回字符串
            return cellValue.ToString().Trim();
        }

        /// <summary>
        /// 尝试将对象转换为数值
        /// </summary>
        /// <param name="value">要转换的值</param>
        /// <param name="result">转换结果</param>
        /// <returns>是否转换成功</returns>
        private static bool TryConvertToNumber(object value, out double result)
        {
            result = 0;

            if (value == null) return false;

            // 直接的数值类型
            if (value is double d) { result = d; return true; }
            if (value is float f) { result = f; return true; }
            if (value is decimal dec) { result = (double)dec; return true; }
            if (value is int i) { result = i; return true; }
            if (value is long l) { result = l; return true; }
            if (value is short s) { result = s; return true; }
            if (value is byte b) { result = b; return true; }

            // 字符串类型尝试解析
            if (value is string str)
            {
                str = str.Trim().Replace(",", ""); // 移除千分号
                return double.TryParse(str, out result);
            }

            return false;
        }

        /// <summary>
        /// 检查数值是否为有效的日期值
        /// </summary>
        /// <param name="value">数值</param>
        /// <returns>是否为有效日期值</returns>
        private static bool IsValidDateValue(double value)
        {
            try
            {
                // Excel日期值通常在1到2958465之间（1900-1-1到9999-12-31）
                return value >= 1 && value <= 2958465;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 根据Excel的NumberFormat格式化日期
        /// </summary>
        /// <param name="date">要格式化的日期</param>
        /// <param name="excelFormat">Excel的NumberFormat</param>
        /// <returns>格式化后的日期字符串</returns>
        private static string FormatDateByExcelFormat(DateTime date, string excelFormat)
        {
            try
            {
                // 将Excel的日期格式转换为.NET的日期格式
                string netFormat = excelFormat.ToLower()
                    .Replace("yyyy", "yyyy")
                    .Replace("yy", "yy")
                    .Replace("mm", "MM")  // 月份
                    .Replace("dd", "dd")
                    .Replace("m", "M")    // 单个月份
                    .Replace("d", "d");   // 单个日期

                // 处理中文格式
                if (excelFormat.Contains("年"))
                {
                    return date.ToString("yyyy年M月d日");
                }
                else if (excelFormat.Contains("/"))
                {
                    return date.ToString("yyyy/M/d");
                }
                else if (excelFormat.Contains("-"))
                {
                    return date.ToString("yyyy-M-d");
                }
                else
                {
                    // 默认中文格式
                    return date.ToString("yyyy年M月d日");
                }
            }
            catch
            {
                // 如果格式转换失败，使用默认中文格式
                return date.ToString("yyyy年M月d日");
            }
        }

        /// <summary>
        /// 检查单元格是否为日期格式
        /// </summary>
        /// <param name="cell">要检查的单元格</param>
        /// <returns>如果是日期格式返回true</returns>
        public static bool IsDateFormat(Excel.Range cell)
        {
            try
            {
                if (cell == null) return false;

                string numberFormat = cell.NumberFormat.ToString().ToLower();

                // 检查常见的日期格式
                return numberFormat.Contains("yyyy") ||
                       numberFormat.Contains("mm") ||
                       numberFormat.Contains("dd") ||
                       numberFormat.Contains("m/d") ||
                       numberFormat.Contains("d/m") ||
                       numberFormat.Contains("h:mm") ||
                       numberFormat.Contains("[$-") && (numberFormat.Contains("d") || numberFormat.Contains("m") || numberFormat.Contains("y"));
            }
            catch
            {
                return false;
            }
        }

        // 替换占位符，如果未找到则加粗并标黄

        private void FindAndReplace(Word.Document doc, string findText, string replaceText, bool highlightIfMissing = false)
        {
            Word.Range range = doc.Content;
            Word.Find findObject = range.Find;

            findObject.ClearFormatting();
            findObject.Text = findText;
            findObject.Replacement.ClearFormatting();
            findObject.Forward = true;
            findObject.Wrap = Word.WdFindWrap.wdFindStop;

            while (findObject.Execute())
            {
                Word.Range foundRange = doc.Range(findObject.Parent.Start, findObject.Parent.End);

                if (!string.IsNullOrEmpty(replaceText))
                {
                    // 替换文本
                    foundRange.Text = replaceText;

                    if (replaceText == "0" || replaceText == "-")
                    {
                        foundRange.Font.Bold = 1;
                        foundRange.HighlightColorIndex = Word.WdColorIndex.wdYellow;
                    }
                }
                else if (highlightIfMissing)
                {
                    // 无替换内容时高亮占位符
                    foundRange.Font.Bold = 1;
                    foundRange.HighlightColorIndex = Word.WdColorIndex.wdYellow;
                }
            }

        }

        // 从 Word 文档中提取所有【xxx】格式的占位符
        private List<string> ExtractPlaceholders(Word.Document doc)
        {
            List<string> placeholders = new List<string>();
            string pattern = "【(.*?)】";
            MatchCollection matches = Regex.Matches(doc.Content.Text, pattern);

            foreach (Match match in matches)
            {
                string placeholder = match.Value;
                if (!placeholders.Contains(placeholder))
                {
                    placeholders.Add(placeholder);
                }
            }
            return placeholders;
        }

        // 自定义输入窗口
        public class InputForm : Form
        {
            private TextBox txtIndexRow;
            private TextBox txtDataRow;
            private Button btnOK;
            private Button btnCancel;

            public int IndexRow { get; private set; }
            public int DataRow { get; private set; }

            public InputForm(int defaultRow)
            {
                this.Text = "设置索引行和数据行";
                this.Width = 300;
                this.Height = 180;
                this.FormBorderStyle = FormBorderStyle.FixedDialog;
                this.StartPosition = FormStartPosition.CenterScreen;

                Label lblIndexRow = new Label { Text = "索引行号:", Left = 10, Top = 20, Width = 80 };
                txtIndexRow = new TextBox { Left = 100, Top = 20, Width = 150, Text = "1" };

                Label lblDataRow = new Label { Text = "数据行号:", Left = 10, Top = 50, Width = 80 };
                txtDataRow = new TextBox { Left = 100, Top = 50, Width = 150, Text = defaultRow.ToString() };

                btnOK = new Button { Text = "确定", Left = 50, Width = 80, Top = 90 };
                btnOK.Click += (sender, e) =>
                {
                    if (int.TryParse(txtIndexRow.Text, out int index) && int.TryParse(txtDataRow.Text, out int data))
                    {
                        IndexRow = index;
                        DataRow = data;
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                    else
                    {
                        MessageBox.Show("请输入有效的行号！");
                    }
                };

                btnCancel = new Button { Text = "取消", Left = 150, Width = 80, Top = 90 };
                btnCancel.Click += (sender, e) => { this.DialogResult = DialogResult.Cancel; this.Close(); };

                this.Controls.Add(lblIndexRow);
                this.Controls.Add(txtIndexRow);
                this.Controls.Add(lblDataRow);
                this.Controls.Add(txtDataRow);
                this.Controls.Add(btnOK);
                this.Controls.Add(btnCancel);
            }
        }

        private void button9_Click(object sender, RibbonControlEventArgs e)
        {
            // 获取当前Excel应用程序实例
            Excel.Application excelApp = Globals.ThisAddIn.Application;

            // 检查是否有选中的范围
            if (excelApp.Selection is Excel.Range selectedRange)
            {
                try
                {
                    // 获取Word应用程序实例
                    Word.Application wordApp = null;
                    Word.Document wordDoc = null;

                    // 尝试获取当前已经打开的Word文档
                    try
                    {
                        wordApp = (Word.Application)System.Runtime.InteropServices.Marshal.GetActiveObject("Word.Application");
                        wordDoc = wordApp.ActiveDocument; // 获取当前活动的Word文档
                    }
                    catch
                    {
                        MessageBox.Show("未找到已打开的Word文档，请先打开一个Word文档！");
                        return;
                    }

                    // 将Excel表格复制到剪贴板
                    selectedRange.Copy();

                    // 将剪贴板内容粘贴到Word文档的光标位置
                    Word.Range wordRange = wordApp.Selection.Range;
                    wordRange.PasteExcelTable(false, false, false);

                    // 释放资源
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(wordRange);
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(wordDoc);
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(wordApp);

                    MessageBox.Show("表格已插入到Word文档中！");
                }
                catch (Exception ex)
                {
                    MessageBox.Show("操作失败: " + ex.Message);
                }
            }
            else
            {
                MessageBox.Show("请先选中一个表格范围！");
            }

        }

        private void button10_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 获取Excel当前选中的单元格位置
                Excel.Application excelApp = Globals.ThisAddIn.Application;
                Excel.Range targetRange = excelApp.Selection as Excel.Range;
                if (targetRange == null)
                {
                    MessageBox.Show("请在Excel中选择一个插入位置！");
                    return;
                }

                // 获取当前打开的Word应用程序实例
                Word.Application wordApp = null;
                Word.Document wordDoc = null;
                try
                {
                    wordApp = (Word.Application)System.Runtime.InteropServices.Marshal.GetActiveObject("Word.Application");
                    wordDoc = wordApp.ActiveDocument;
                }
                catch
                {
                    MessageBox.Show("未找到已打开的Word文档！");
                    return;
                }

                // 检查Word中是否选中了表格
                Word.Selection wordSelection = wordApp.Selection;
                if (wordSelection.Tables.Count == 0)
                {
                    MessageBox.Show("请在Word中选中一个表格！");
                    return;
                }

                // 获取选中的Word表格
                Word.Table wordTable = wordSelection.Tables[1];
                int rowCount = wordTable.Rows.Count;
                int colCount = wordTable.Columns.Count;

                // 将Word表格数据写入Excel
                for (int i = 1; i <= rowCount; i++)
                {
                    for (int j = 1; j <= colCount; j++)
                    {
                        // 获取Word表格单元格内容
                        string cellText = wordTable.Cell(i, j).Range.Text.TrimEnd('\r', '\a');
                        // 写入Excel单元格
                        targetRange.Cells[i, j].Value2 = cellText;
                    }
                }

                // 释放COM对象
                System.Runtime.InteropServices.Marshal.ReleaseComObject(wordTable);
                System.Runtime.InteropServices.Marshal.ReleaseComObject(wordSelection);
                System.Runtime.InteropServices.Marshal.ReleaseComObject(wordDoc);
                System.Runtime.InteropServices.Marshal.ReleaseComObject(wordApp);

                MessageBox.Show("表格已插入到Excel！");
            }
            catch (Exception ex)
            {
                MessageBox.Show("操作失败: " + ex.Message);
            }


        }

        private void button4_Click(object sender, RibbonControlEventArgs e)
        {
            Excel.Application excelApp = Globals.ThisAddIn.Application;
            Excel.Workbook workbook = excelApp.ActiveWorkbook;
            Excel.Worksheet worksheet = workbook.ActiveSheet;
            Excel.Range selectedCell = excelApp.Selection as Excel.Range;

            if (selectedCell == null)
            {
                MessageBox.Show("请先选择一个 Excel 单元格所在的行！");
                return;
            }

            // 用户输入索引行和数据行（支持多行）
            InputForm1 inputForm = new InputForm1();
            if (inputForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            int indexRow = inputForm.IndexRow;
            List<int> dataRows = inputForm.DataRows;

            Excel.Range indexRowRange = worksheet.Rows[indexRow];

            // 选择 Word 模板
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Title = "选择 Word 模板文件",
                Filter = "Word 文件 (*.doc;*.docx)|*.doc;*.docx",
                Multiselect = false
            };

            if (openFileDialog.ShowDialog() != DialogResult.OK)
            {
                MessageBox.Show("未选择 Word 模板文件，操作取消！");
                return;
            }
            string templatePath = openFileDialog.FileName;

            // 选择保存文件夹
            FolderBrowserDialog folderDialog = new FolderBrowserDialog
            {
                Description = "选择 Word 报告保存文件夹"
            };

            if (folderDialog.ShowDialog() != DialogResult.OK)
            {
                MessageBox.Show("未选择保存位置，操作取消！");
                return;
            }
            string saveFolderPath = folderDialog.SelectedPath;

            // 遍历每个数据行生成报告
            foreach (int rowIndex in dataRows)
            {
                Excel.Range dataRowRange = worksheet.Rows[rowIndex];

                Dictionary<string, string> dataMap = new Dictionary<string, string>();
                for (int col = 1; col <= indexRowRange.Columns.Count; col++)
                {
                    // 使用 Value2 获取实际值，避免列太窄时显示 ####
                    object columnNameObj = indexRowRange.Cells[1, col].Value2;
                    Excel.Range dataCell = dataRowRange.Cells[1, col];
                    object cellValueObj = dataCell.Value2;

                    string columnName = (columnNameObj != null) ? columnNameObj.ToString().Trim() : "";
                    string cellValue = FormatCellValue(cellValueObj, dataCell); // 传入源单元格以正确处理日期

                    if (!string.IsNullOrEmpty(columnName))
                    {
                        dataMap[string.Format("【{0}】", columnName)] = cellValue;
                    }
                }

                string outputPath = Path.Combine(saveFolderPath, string.Format("报告_{0}_{1}.docx", rowIndex, DateTime.Now.ToString("yyyyMMdd_HHmmss")));
                GenerateWordReport(templatePath, outputPath, dataMap);
            }

            MessageBox.Show(string.Format("所有 Word 报告已生成！\n保存位置：{0}", saveFolderPath));
        }

        // 生成 Word 报告（替换+高亮未匹配）
        private void GenerateWordReport(string templatePath, string outputPath, Dictionary<string, string> dataMap)
        {
            Word.Application wordApp = new Word.Application();
            Word.Document doc = wordApp.Documents.Open(templatePath);
            wordApp.Visible = false;

            List<string> placeholders = ExtractPlaceholders(doc);

            foreach (string placeholder in placeholders)
            {
                string value = dataMap.ContainsKey(placeholder) ? dataMap[placeholder] : null;
                FindAndReplace(doc, placeholder, value, highlightIfMissing: true);
            }

            // 在保存前设置文档的打印属性
            try
            {
                // 设置文档的打印选项
                doc.Application.Options.PrintComments = false;
                doc.Application.Options.PrintHiddenText = false;
                doc.Application.Options.PrintDrawingObjects = true;
                doc.Application.Options.PrintBackgrounds = false;

                // 设置文档的打印选项
                doc.Application.Options.PrintComments = false;
            }
            catch (Exception savePrintEx)
            {
                System.Diagnostics.Debug.WriteLine(string.Format("保存前设置打印选项失败: {0}", savePrintEx.Message));
            }

            doc.SaveAs2(outputPath, Word.WdSaveFormat.wdFormatXMLDocument);
            doc.Close();
            wordApp.Quit();
        }
        public class InputForm1 : Form
        {
            private TextBox txtIndexRow;
            private TextBox txtDataRows;
            private Button btnOK;
            private Button btnCancel;

            public int IndexRow { get; private set; }
            public List<int> DataRows { get; private set; }

            public InputForm1()
            {
                DataRows = new List<int>();
                this.Text = "输入索引行和数据行";
                this.Width = 400;
                this.Height = 200;
                this.FormBorderStyle = FormBorderStyle.FixedDialog;
                this.StartPosition = FormStartPosition.CenterScreen;

                Label lblIndexRow = new Label { Text = "索引行:", Left = 10, Top = 20, Width = 80 };
                txtIndexRow = new TextBox { Left = 100, Top = 20, Width = 250 };

                Label lblDataRows = new Label { Text = "数据行 (逗号分隔):", Left = 10, Top = 60, Width = 150 };
                txtDataRows = new TextBox { Left = 160, Top = 60, Width = 190 };

                btnOK = new Button { Text = "确定", Left = 70, Width = 80, Top = 120 };
                btnOK.Click += BtnOK_Click;

                btnCancel = new Button { Text = "取消", Left = 180, Width = 80, Top = 120 };
                btnCancel.Click += (sender, e) => { this.DialogResult = DialogResult.Cancel; this.Close(); };

                this.Controls.Add(lblIndexRow);
                this.Controls.Add(txtIndexRow);
                this.Controls.Add(lblDataRows);
                this.Controls.Add(txtDataRows);
                this.Controls.Add(btnOK);
                this.Controls.Add(btnCancel);
            }

            private void BtnOK_Click(object sender, EventArgs e)
            {
                if (!int.TryParse(txtIndexRow.Text, out int indexRow) || indexRow <= 0)
                {
                    MessageBox.Show("请输入有效的索引行号！");
                    return;
                }

                List<int> dataRows = new List<int>();
                string[] rowStrings = txtDataRows.Text.Split(new char[] { ',', '，' }, StringSplitOptions.RemoveEmptyEntries);

                foreach (string rowStr in rowStrings)
                {
                    if (int.TryParse(rowStr.Trim(), out int rowNum) && rowNum > 0)
                    {
                        dataRows.Add(rowNum);
                    }
                }

                if (dataRows.Count == 0)
                {
                    MessageBox.Show("请输入至少一个有效的数据行号！");
                    return;
                }

                IndexRow = indexRow;
                DataRows = dataRows;
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void button2_Click(object sender, RibbonControlEventArgs e)
        {
            Excel.Application excelApp = null;
            Excel.Workbook workbook = null;
            Excel.Worksheet worksheet = null;
            Excel.Range selectedCell = null;
            Excel.Range usedRange = null;

            Word.Application wordApp = null;
            Word.Document doc = null;

            try
            {
                // 初始化Excel对象
                excelApp = Globals.ThisAddIn.Application;
                workbook = excelApp.ActiveWorkbook;
                worksheet = workbook.ActiveSheet;
                selectedCell = excelApp.Selection as Excel.Range;

                if (selectedCell == null)
                {
                    MessageBox.Show("请先选择一个 Excel 单元格所在的列！");
                    return;
                }

                // 获取用户输入的起始行号
                InputForm2 inputForm = new InputForm2(2);
                if (inputForm.ShowDialog() != DialogResult.OK)
                {
                    return;
                }
                int startRow = inputForm.IndexRow;

                // 读取竖排数据（假设键在A列，值在B列）
                Dictionary<string, string> dataMap = new Dictionary<string, string>();
                usedRange = worksheet.UsedRange;
                int maxRow = usedRange.Rows.Count;

                for (int row = startRow; row <= maxRow; row++)
                {
                    Excel.Range keyCell = null;
                    Excel.Range valueCell = null;
                    try
                    {
                        keyCell = worksheet.Cells[row, 1];
                        valueCell = worksheet.Cells[row, 2];

                        // 使用 Value2 获取实际值，避免列太窄时显示 ####
                        object keyObj = keyCell.Value2;
                        object valueObj = valueCell.Value2;

                        string key = (keyObj != null) ? keyObj.ToString().Trim() : "";  // A列
                        string value = FormatCellValue(valueObj, valueCell); // B列，格式化数值，传入源单元格

                        if (!string.IsNullOrEmpty(key))
                        {
                            dataMap[string.Format("【{0}】", key)] = value; // 确保键带【】符号
                        }
                    }
                    finally
                    {
                        if (keyCell != null) Marshal.ReleaseComObject(keyCell);
                        if (valueCell != null) Marshal.ReleaseComObject(valueCell);
                    }
                }

                // 选择Word模板
                OpenFileDialog openFileDialog = new OpenFileDialog
                {
                    Title = "选择 Word 模板文件",
                    Filter = "Word 文件 (*.doc;*.docx)|*.doc;*.docx",
                    Multiselect = false
                };

                if (openFileDialog.ShowDialog() != DialogResult.OK)
                {
                    MessageBox.Show("未选择 Word 模板文件，操作取消！");
                    return;
                }
                string templatePath = openFileDialog.FileName;

                // 选择保存路径
                SaveFileDialog saveFileDialog = new SaveFileDialog
                {
                    Title = "保存生成的 Word 报告",
                    Filter = "Word 2007+ (*.docx)|*.docx|Word 97-2003 (*.doc)|*.doc",
                    DefaultExt = "docx",
                    FileName = string.Format("生成报告_{0}", DateTime.Now.ToString("yyyyMMdd_HHmmss"))
                };

                if (saveFileDialog.ShowDialog() != DialogResult.OK)
                {
                    MessageBox.Show("未选择保存位置，操作取消！");
                    return;
                }
                string outputPath = saveFileDialog.FileName;

                // 初始化Word对象
                wordApp = new Word.Application();
                doc = wordApp.Documents.Open(templatePath);
                wordApp.Visible = true; // 调试时可见

                // 处理占位符
                ProcessPlaceholders(doc, dataMap);

                // 保存文件
                object fileFormat = Path.GetExtension(outputPath).ToLower() == ".docx"
                    ? Word.WdSaveFormat.wdFormatXMLDocument
                    : Word.WdSaveFormat.wdFormatDocument97;

                doc.SaveAs2(outputPath, fileFormat);
                MessageBox.Show(string.Format("Word 报告已生成：\n{0}", outputPath));
            }
            catch (Exception ex)
            {
                MessageBox.Show(string.Format("发生错误：\n{0}", ex.Message));
            }
            finally
            {
                // 释放资源 - 严格按照创建的反顺序释放
                if (doc != null)
                {
                    doc.Close(false);
                    Marshal.ReleaseComObject(doc);
                }
                if (wordApp != null)
                {
                    wordApp.Quit();
                    Marshal.ReleaseComObject(wordApp);
                }

                if (usedRange != null) Marshal.ReleaseComObject(usedRange);
                if (selectedCell != null) Marshal.ReleaseComObject(selectedCell);
                if (worksheet != null) Marshal.ReleaseComObject(worksheet);
                if (workbook != null) Marshal.ReleaseComObject(workbook);
                // excelApp 是全局对象，不应在这里释放

                GC.Collect();
                GC.WaitForPendingFinalizers();
            }
        }

        private void ProcessPlaceholders(Word.Document doc, Dictionary<string, string> dataMap)
        {
            // 先处理所有能找到的占位符（替换）
            foreach (var item in dataMap)
            {
                if (!string.IsNullOrEmpty(item.Value))
                {
                    SafeFindAndReplace(doc, item.Key, item.Value);
                }
            }

            // 然后处理所有未替换的占位符（高亮）
            List<string> allPlaceholders = SafeExtractPlaceholders(doc);
            foreach (string placeholder in allPlaceholders)
            {
                if (!dataMap.ContainsKey(placeholder))
                {
                    SafeHighlightPlaceholder(doc, placeholder);
                }
            }
        }

        private void SafeFindAndReplace(Word.Document doc, string findText, string replaceText)
        {
            Word.Range range = null;
            try
            {
                range = doc.Content;
                object replaceAll = Word.WdReplace.wdReplaceAll;

                range.Find.Execute(
                    FindText: findText,
                    ReplaceWith: replaceText,
                    Replace: replaceAll
                );
            }
            finally
            {
                if (range != null) Marshal.ReleaseComObject(range);
            }
        }

        private void SafeHighlightPlaceholder(Word.Document doc, string placeholder)
        {
            Word.Range range = null;
            try
            {
                range = doc.Content;
                while (range.Find.Execute(placeholder))
                {
                    // 保存原有格式
                    string originalName = range.Font.Name;

                    // 应用高亮样式
                    range.Font.Bold = 1;
                    range.HighlightColorIndex = Word.WdColorIndex.wdYellow;

                    // 恢复原有字体设置
                    range.Font.Name = originalName;

                    Word.Range nextRange = range;
                    range.Collapse(Word.WdCollapseDirection.wdCollapseEnd);
                    if (nextRange != range && nextRange != null)
                    {
                        Marshal.ReleaseComObject(nextRange);
                    }
                }
            }
            finally
            {
                if (range != null) Marshal.ReleaseComObject(range);
            }
        }

        private List<string> SafeExtractPlaceholders(Word.Document doc)
        {
            Word.Range range = null;
            try
            {
                range = doc.Content;
                string docText = range.Text;

                List<string> placeholders = new List<string>();
                MatchCollection matches = Regex.Matches(docText, @"【(.*?)】");
                foreach (Match match in matches)
                {
                    if (!placeholders.Contains(match.Value))
                    {
                        placeholders.Add(match.Value);
                    }
                }
                return placeholders;
            }
            finally
            {
                if (range != null) Marshal.ReleaseComObject(range);
            }
        }


        public class InputForm2 : Form
        {
            public int IndexRow { get; private set; }

            public InputForm2(int defaultRow)
            {
                InitializeComponents(defaultRow);
            }

            private void InitializeComponents(int defaultRow)
            {
                this.Text = "设置起始行号";
                this.Width = 300;
                this.Height = 140;
                this.FormBorderStyle = FormBorderStyle.FixedDialog;
                this.StartPosition = FormStartPosition.CenterScreen;

                var lblIndexRow = new Label { Text = "起始行号:", Left = 10, Top = 20, Width = 80 };
                var txtIndexRow = new TextBox { Left = 100, Top = 20, Width = 150, Text = defaultRow.ToString() };

                var btnOK = new Button { Text = "确定", Left = 50, Width = 80, Top = 60 };
                btnOK.Click += (sender, e) =>
                {
                    if (int.TryParse(txtIndexRow.Text, out int index) && index > 0)
                    {
                        IndexRow = index;
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                    else
                    {
                        MessageBox.Show("请输入有效的正整数行号！");
                    }
                };

                var btnCancel = new Button { Text = "取消", Left = 150, Width = 80, Top = 60 };
                btnCancel.Click += (sender, e) => { this.DialogResult = DialogResult.Cancel; this.Close(); };

                this.Controls.AddRange(new Control[] { lblIndexRow, txtIndexRow, btnOK, btnCancel });
            }
        }


        private void CleanWordTables()
        {
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Title = "选择 Word 文件",
                Filter = "Word 文件 (*.doc;*.docx)|*.doc;*.docx",
                Multiselect = false
            };

            if (openFileDialog.ShowDialog() != DialogResult.OK)
            {
                MessageBox.Show("未选择文件，操作取消！");
                return;
            }

            string filePath = openFileDialog.FileName;

            Word.Application wordApp = null;
            Word.Document doc = null;

            try
            {
                wordApp = new Word.Application();
                doc = wordApp.Documents.Open(filePath, ReadOnly: false, Visible: false);
                wordApp.Visible = false;

                int totalDeleted = 0;

                foreach (Word.Table table in doc.Tables)
                {
                    int rowCount = table.Rows.Count;
                    int colCount = table.Columns.Count;

                    for (int i = rowCount; i >= 1; i--) // 倒序遍历
                    {
                        int cellCount = 0;
                        int zeroOrDashCount = 0;
                        int emptyCount = 0;

                        for (int j = 1; j <= colCount; j++)
                        {
                            try
                            {
                                if (i > table.Rows.Count || j > table.Columns.Count)
                                    continue; // 安全边界判断

                                Word.Cell cell = table.Cell(i, j);
                                if (cell == null)
                                    continue;

                                string text = cell.Range.Text?.Replace("\r\a", "").Trim();

                                cellCount++;

                                if (string.IsNullOrEmpty(text))
                                    emptyCount++;
                                else if (text == "0" || text == "-")
                                    zeroOrDashCount++;
                            }
                            catch (COMException comEx)
                            {
                                // 忽略单元格错误，但可记录日志调试
                                System.Diagnostics.Debug.WriteLine(string.Format("跳过 Cell({0},{1})：{2}", i, j, comEx.Message));
                            }
                            catch (Exception ex)
                            {
                                MessageBox.Show(string.Format("读取 Cell({0},{1}) 出错：{2}", i, j, ex.Message));
                                return;
                            }
                        }

                        // 删除规则
                        if (cellCount > 0 && (
                            emptyCount == cellCount ||
                            zeroOrDashCount >= cellCount / 2 ||
                            (zeroOrDashCount + emptyCount) >= (cellCount * 0.7)))
                        {
                            try
                            {
                                table.Rows[i].Delete();
                                totalDeleted++;
                            }
                            catch (Exception ex)
                            {
                                MessageBox.Show(string.Format("删除第 {0} 行失败：{1}", i, ex.Message));
                            }
                        }
                    }
                }

                doc.Save();
                MessageBox.Show(string.Format("清理完成！共删除 {0} 行。\n文件路径：{1}", totalDeleted, filePath));
            }
            catch (COMException comEx)
            {
                MessageBox.Show("COM 调用失败：" + comEx.Message);
            }
            catch (Exception ex)
            {
                MessageBox.Show("处理过程中出错：" + ex.Message);
            }
            finally
            {
                try
                {
                    if (doc != null)
                    {
                        doc.Close(false);
                        System.Runtime.InteropServices.Marshal.ReleaseComObject(doc);
                    }

                    if (wordApp != null)
                    {
                        wordApp.Quit(false);
                        System.Runtime.InteropServices.Marshal.ReleaseComObject(wordApp);
                    }

                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                }
                catch { }
            }
        }


        private void button12_Click(object sender, RibbonControlEventArgs e)
        {
            CleanWordTables();
        }

        private void button13_Click(object sender, RibbonControlEventArgs e)
        {
            // 在方法级别声明Excel属性变量，以便在finally块中访问
            bool originalScreenUpdating = true;
            bool originalDisplayAlerts = true;
            Excel.XlCalculation originalCalculation = Excel.XlCalculation.xlCalculationAutomatic;
            Excel.Application excelApp = null;

            try
            {
                // 固定参数设置
                const string sourceSheetName1 = "4-1-1工程竣工费用明细定案表";
                const string targetSheetName1 = "附表4-工程竣工决算审计定案明细表";
                const string targetSheetName2 = "附表3-工程竣工决算审计定案汇总表";
                const string targetSheetName3 = "附表1-工程建设概况总表";
                const string targetSheetName4 = "附表2-工程验收及移交资产情况明细表";
                const string targetSheetName5 = "附表5-工程建设规模统计总表";
                const string targetSheetName6 = "附表6-报告信息表"; // 新增附表6

                // 源工作表名称
                const string sourceSheetName2 = "附表1-工程建设概况总表";
                const string sourceSheetName3 = "附表2-工程验收及移交资产情况明细表";
                const string sourceSheetName5 = "附表5-工程建设规模统计总表";
                const string sourceSheetName6 = "核心数据表"; // 新增核心数据表

                const int sourceStartRow = 5; // 源数据从第5行开始
                const int targetStartRow1 = 8; // 目标数据从第8行开始
                const int targetStartRow5 = 9; // 附表5目标数据从第9行开始
                const string filterCondition1 = "一"; // 筛选A列为"一"的行
                List<string> filterConditions2 = new List<string> { "（一）", "（二）", "（三）", "（四）" };

                // 1. 让用户选择要合并的Excel文件
                OpenFileDialog sourceFileDialog = new OpenFileDialog();
                sourceFileDialog.Filter = "Excel Files|*.xls;*.xlsx;*.xlsm";
                sourceFileDialog.Multiselect = true;
                sourceFileDialog.Title = "选择要合并的源Excel文件";

                if (sourceFileDialog.ShowDialog() != DialogResult.OK) return;

                // 2. 让用户选择模板文件
                OpenFileDialog templateFileDialog = new OpenFileDialog();
                templateFileDialog.Filter = "Excel Files|*.xls;*.xlsx;*.xlsm";
                templateFileDialog.Title = "选择模板文件";

                if (templateFileDialog.ShowDialog() != DialogResult.OK) return;

                // 获取Excel应用程序对象
                excelApp = Globals.ThisAddIn.Application;

                // 优化性能：关闭屏幕更新、告警和自动计算（添加异常处理）
                try
                {
                    originalScreenUpdating = excelApp.ScreenUpdating;
                    originalDisplayAlerts = excelApp.DisplayAlerts;
                    originalCalculation = excelApp.Calculation;

                    excelApp.ScreenUpdating = false;
                    excelApp.DisplayAlerts = false;
                    excelApp.Calculation = Excel.XlCalculation.xlCalculationManual;
                }
                catch (Exception settingsEx)
                {
                    // 如果设置Excel属性失败，记录错误但继续执行
                    System.Diagnostics.Debug.WriteLine($"设置Excel属性失败: {settingsEx.Message}");
                }

                // 打开模板文件
                Excel.Workbook templateWorkbook = excelApp.Workbooks.Open(templateFileDialog.FileName);
                Excel.Worksheet targetSheet1 = null;
                Excel.Worksheet targetSheet2 = null;
                Excel.Worksheet targetSheet3 = null;
                Excel.Worksheet targetSheet4 = null;
                Excel.Worksheet targetSheet5 = null;
                Excel.Worksheet targetSheet6 = null; // 新增附表6

                // 查找目标工作表
                foreach (Excel.Worksheet sheet in templateWorkbook.Sheets)
                {
                    if (sheet.Name == targetSheetName1)
                    {
                        targetSheet1 = sheet;
                    }
                    else if (sheet.Name == targetSheetName2)
                    {
                        targetSheet2 = sheet;
                    }
                    else if (sheet.Name == targetSheetName3)
                    {
                        targetSheet3 = sheet;
                    }
                    else if (sheet.Name == targetSheetName4)
                    {
                        targetSheet4 = sheet;
                    }
                    else if (sheet.Name == targetSheetName5)
                    {
                        targetSheet5 = sheet;
                    }
                    else if (sheet.Name == targetSheetName6)
                    {
                        targetSheet6 = sheet; // 新增附表6查找
                    }
                }

                if (targetSheet1 == null)
                {
                    MessageBox.Show(string.Format("模板文件中未找到工作表 '{0}'", targetSheetName1), "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    templateWorkbook.Close(false);
                    return;
                }

                if (targetSheet2 == null)
                {
                    MessageBox.Show(string.Format("模板文件中未找到工作表 '{0}'", targetSheetName2), "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    templateWorkbook.Close(false);
                    return;
                }

                int targetRowOffset1 = targetStartRow1;
                int successCount1 = 0;
                int successCount2 = 0;
                int successCount3 = 0;
                int successCount4 = 0;
                int successCount5 = 0;
                int successCount6 = 0; // 新增附表6成功计数
                StringBuilder processLog = new StringBuilder();

                // 新增功能：初始化汇总数据字典
                Dictionary<string, Dictionary<string, double>> summaryValues1 = new Dictionary<string, Dictionary<string, double>>
{
    { "（一）", new Dictionary<string, double> { { "C", 0 }, { "D", 0 }, { "E", 0 }, { "F", 0 }, { "H", 0 }, { "I", 0 }, { "L", 0 }, { "M", 0 } } },
    { "（二）", new Dictionary<string, double> { { "C", 0 }, { "D", 0 }, { "E", 0 }, { "F", 0 }, { "H", 0 }, { "I", 0 }, { "L", 0 }, { "M", 0 } } },
    { "（三）", new Dictionary<string, double> { { "C", 0 }, { "D", 0 }, { "E", 0 }, { "F", 0 }, { "H", 0 }, { "I", 0 }, { "L", 0 }, { "M", 0 } } },
    { "（四）", new Dictionary<string, double> { { "C", 0 }, { "D", 0 }, { "E", 0 }, { "F", 0 }, { "H", 0 }, { "I", 0 }, { "L", 0 }, { "M", 0 } } }
};

                // 处理每个源文件
                foreach (string filePath in sourceFileDialog.FileNames)
                {
                    Excel.Workbook sourceWorkbook = null;
                    try
                    {
                        sourceWorkbook = excelApp.Workbooks.Open(filePath);

                        Excel.Worksheet sourceSheet1 = null;
                        Excel.Worksheet sourceSheet2 = null;
                        Excel.Worksheet sourceSheet3 = null;
                        Excel.Worksheet sourceSheet5 = null;
                        Excel.Worksheet sourceSheet6 = null; // 新增核心数据表

                        // 查找源工作表
                        foreach (Excel.Worksheet sheet in sourceWorkbook.Sheets)
                        {
                            if (sheet.Name == sourceSheetName1)
                            {
                                sourceSheet1 = sheet;
                            }
                            else if (sheet.Name == sourceSheetName2)
                            {
                                sourceSheet2 = sheet;
                            }
                            else if (sheet.Name == sourceSheetName3)
                            {
                                sourceSheet3 = sheet;
                            }
                            else if (sheet.Name == sourceSheetName5)
                            {
                                sourceSheet5 = sheet;
                            }
                            else if (sheet.Name == sourceSheetName6)
                            {
                                sourceSheet6 = sheet; // 新增核心数据表查找
                            }
                        }

                        if (sourceSheet1 == null)
                        {
                            processLog.AppendLine(string.Format("[跳过] 文件 {0} 未找到工作表 '{1}'", System.IO.Path.GetFileName(filePath), sourceSheetName1));
                            continue;
                        }

                        Excel.Range usedRange1 = sourceSheet1.UsedRange;
                        int totalRows1 = usedRange1.Rows.Count;

                        if (totalRows1 < sourceStartRow)
                        {
                            processLog.AppendLine(string.Format("[跳过] 文件 {0} 数据行不足(少于{1}行)", System.IO.Path.GetFileName(filePath), sourceStartRow));
                            continue;
                        }

                        // 处理附表1（第6行，B列起，追加到模板附表1第6行起）
                        if (sourceSheet2 != null && targetSheet3 != null)
                        {
                            int lastRowTgt = targetSheet3.Cells[targetSheet3.Rows.Count, 2].End(Excel.XlDirection.xlUp).Row;
                            int insertRow = lastRowTgt < 6 ? 6 : lastRowTgt + 1;

                            Excel.Range srcRow = sourceSheet2.Range[sourceSheet2.Cells[6, 2], sourceSheet2.Cells[6, 20]]; // B-T
                            Excel.Range tgtRow = targetSheet3.Range[targetSheet3.Cells[insertRow, 2], targetSheet3.Cells[insertRow, 20]];

                            for (int col = 1; col <= 19; col++) // B-T 共19列
                            {
                                // 获取源单元格的值并进行格式化处理
                                Excel.Range srcCell = srcRow.Cells[1, col];
                                string formattedValue = GetCellDisplayValue(srcCell);
                                tgtRow.Cells[1, col].Value = formattedValue;
                            }
                            successCount1++;
                        }

                        //处理附表2

                        if (sourceSheet3 != null && targetSheet4 != null)
                        {
                            int targetStartRow4 = 7;  // 模板表附表2数据起始行
                            int sourceRowIndex = 7;   // 源表取第7行（B列开始）

                            // 找模板表 B 列最后有数据的行（汇总行所在或最后数据行）
                            int lastRowTgt = targetSheet4.Cells[targetSheet4.Rows.Count, 2]
                                             .End(Excel.XlDirection.xlUp).Row;

                            // 确定写入行：如果模板表还没数据，从起始行开始写；否则在最后一行数据下一行写入
                            int writeRow = lastRowTgt < targetStartRow4 ? targetStartRow4 : lastRowTgt + 1;

                            // 源表第7行，从B列到最后一列（动态确定范围）
                            int srcLastCol = sourceSheet3.UsedRange.Columns.Count;
                            Excel.Range srcRow = sourceSheet3.Range[
                                sourceSheet3.Cells[sourceRowIndex, 2],
                                sourceSheet3.Cells[sourceRowIndex, srcLastCol]];

                            // 直接写入目标表格（不新建行结构，仅写入单元格值）
                            for (int col = 1; col <= srcRow.Columns.Count; col++)
                            {
                                // 获取源单元格的值并进行格式化处理
                                Excel.Range srcCell = srcRow.Cells[1, col];
                                string formattedValue = GetCellDisplayValue(srcCell);
                                // B列对应 col=1，所以目标列= B列起 col+1
                                targetSheet4.Cells[writeRow, col + 1].Value = formattedValue;
                            }

                            successCount2++;

                        }


                        // 处理附表5（第9行，B列起，追加到模板附表5第9行起）
                        if (sourceSheet5 != null && targetSheet5 != null)
                        {
                            int lastRowTgt = targetSheet5.Cells[targetSheet5.Rows.Count, 2].End(Excel.XlDirection.xlUp).Row;
                            int insertRow = lastRowTgt < targetStartRow5 ? targetStartRow5 : lastRowTgt + 1;

                            Excel.Range srcRow = sourceSheet5.Range[sourceSheet5.Cells[9, 2], sourceSheet5.Cells[9, 20]]; // B-T
                            Excel.Range tgtRow = targetSheet5.Range[targetSheet5.Cells[insertRow, 2], targetSheet5.Cells[insertRow, 20]];

                            for (int col = 1; col <= 19; col++) // B-T 共19列
                            {
                                // 获取源单元格的值并进行格式化处理
                                Excel.Range srcCell = srcRow.Cells[1, col];
                                string formattedValue = GetCellDisplayValue(srcCell);
                                tgtRow.Cells[1, col].Value = formattedValue;
                            }
                            successCount5++;
                        }

                        // 处理附表6-报告信息表（根据第2行字段名与核心数据表字段一一对应）
                        if (sourceSheet6 != null && targetSheet6 != null)
                        {
                            try
                            {
                                // 确定插入行号：查找附表6中第一列（A列）最后有数据的行
                                int lastRowTgt6 = targetSheet6.Cells[targetSheet6.Rows.Count, 1].End(Excel.XlDirection.xlUp).Row;
                                int insertRow6 = lastRowTgt6 < 3 ? 3 : lastRowTgt6 + 1; // 从第3行开始，如果已有数据则在下一行插入

                                // 读取附表6第2行的字段名（作为索引）
                                Dictionary<string, int> targetFieldMap = new Dictionary<string, int>();
                                Excel.Range targetHeaderRange = targetSheet6.Rows[2];
                                int targetMaxCol = targetSheet6.UsedRange.Columns.Count;

                                for (int col = 1; col <= targetMaxCol; col++)
                                {
                                    Excel.Range headerCell = targetHeaderRange.Cells[1, col];
                                    object headerObj = headerCell.Value2;
                                    string headerName = (headerObj != null) ? headerObj.ToString().Trim() : "";

                                    if (!string.IsNullOrEmpty(headerName))
                                    {
                                        targetFieldMap[headerName] = col;
                                    }
                                }

                                // 读取核心数据表的第一行字段名和第二行数据
                                Dictionary<string, string> sourceDataMap = new Dictionary<string, string>();
                                Excel.Range sourceHeaderRange = sourceSheet6.Rows[1];
                                Excel.Range sourceDataRange = sourceSheet6.Rows[2];
                                int sourceMaxCol = sourceSheet6.UsedRange.Columns.Count;

                                for (int col = 1; col <= sourceMaxCol; col++)
                                {
                                    Excel.Range headerCell = sourceHeaderRange.Cells[1, col];
                                    Excel.Range dataCell = sourceDataRange.Cells[1, col];

                                    object headerObj = headerCell.Value2;
                                    string headerName = (headerObj != null) ? headerObj.ToString().Trim() : "";

                                    // 优化数据获取：确保获取公式的计算结果而不是公式本身
                                    string dataValue = GetCellDisplayValue(dataCell);

                                    if (!string.IsNullOrEmpty(headerName))
                                    {
                                        sourceDataMap[headerName] = dataValue;
                                    }
                                }

                                // 根据字段名匹配，将数据写入附表6动态行号对应位置
                                int matchedFields = 0;
                                foreach (var targetField in targetFieldMap)
                                {
                                    string fieldName = targetField.Key;
                                    int targetCol = targetField.Value;

                                    if (sourceDataMap.ContainsKey(fieldName))
                                    {
                                        string dataValue = sourceDataMap[fieldName];
                                        Excel.Range targetCell = targetSheet6.Cells[insertRow6, targetCol]; // 使用动态行号

                                        // 设置单元格的值
                                        targetCell.Value = dataValue;

                                        // 如果是数值，设置数字格式为千分号+两位小数
                                        if (IsNumericString(dataValue))
                                        {
                                            try
                                            {
                                                targetCell.NumberFormat = "#,##0.00";
                                            }
                                            catch (Exception formatEx)
                                            {
                                                System.Diagnostics.Debug.WriteLine($"设置数字格式失败: {formatEx.Message}");
                                            }
                                        }

                                        matchedFields++;
                                    }
                                }

                                if (matchedFields > 0)
                                {
                                    successCount6++;
                                    processLog.AppendLine(string.Format("[附表6] 成功匹配 {0} 个字段", matchedFields));
                                }
                                else
                                {
                                    processLog.AppendLine("[附表6] 未找到匹配的字段");
                                }
                            }
                            catch (Exception ex)
                            {
                                processLog.AppendLine(string.Format("[附表6] 处理出错: {0}", ex.Message));
                            }
                        }

                        // 筛选A列为"一"的行（主表数据）
                        for (int row = sourceStartRow; row <= totalRows1; row++)
                        {
                            Excel.Range cellA1 = sourceSheet1.Cells[row, 1] as Excel.Range;
                            if (cellA1.Value != null && cellA1.Value.ToString().Trim() == filterCondition1)
                            {
                                // 获取源表的数据列范围（从B列到最后一列有数据的列）
                                int lastSourceCol = sourceSheet1.UsedRange.Columns.Count;

                                // 复制B列到最后一列的数据
                                Excel.Range sourceRange1 = sourceSheet1.Range[
                                    sourceSheet1.Cells[row, 2],  // B列
                                    sourceSheet1.Cells[row, lastSourceCol]  // 最后一列
                                ];

                                // 获取目标表的列范围（同样从B列开始）
                                int lastTargetCol = targetSheet1.UsedRange.Columns.Count;
                                lastTargetCol = Math.Max(lastTargetCol, 2); // 确保至少从B列开始

                                Excel.Range targetRange1 = targetSheet1.Range[
                                    targetSheet1.Cells[targetRowOffset1, 2],  // B列
                                    targetSheet1.Cells[targetRowOffset1, lastTargetCol]  // 最后一列
                                ];

                                // 逐个单元格复制值
                                for (int col = 1; col <= sourceRange1.Columns.Count; col++)
                                {
                                    // 只复制到目标表存在的列
                                    if (col <= targetRange1.Columns.Count)
                                    {
                                        targetRange1.Cells[1, col].Value = sourceRange1.Cells[1, col].Value;
                                    }
                                }

                                targetRowOffset1++;
                                successCount4++;
                            }
                        }

                        // 筛选A列为"（一）"、"（二）"、"（三）"和"（四）"的行（汇总数据）
                        // 筛选A列为"（一）"、"（二）"、"（三）"和"（四）"的行（汇总数据）
                        for (int row = sourceStartRow; row <= totalRows1; row++)
                        {
                            Excel.Range cellA1 = sourceSheet1.Cells[row, 1] as Excel.Range;
                            if (cellA1.Value != null && filterConditions2.Contains(cellA1.Value.ToString().Trim()))
                            {
                                string condition = cellA1.Value.ToString().Trim();

                                // 处理C-F列
                                for (int col = 3; col <= 6; col++)
                                {
                                    Excel.Range cell = sourceSheet1.Cells[row, col] as Excel.Range;
                                    if (cell.Value != null && cell.Value is double)
                                    {
                                        summaryValues1[condition][string.Format("{0}", (char)('A' + col - 1))] += (double)cell.Value;
                                    }
                                }

                                // 处理H列(8)
                                Excel.Range cellH = sourceSheet1.Cells[row, 8] as Excel.Range;
                                if (cellH.Value != null && cellH.Value is double)
                                {
                                    summaryValues1[condition]["H"] += (double)cellH.Value;
                                }

                                // 处理I列(9)
                                Excel.Range cellI = sourceSheet1.Cells[row, 9] as Excel.Range;
                                if (cellI.Value != null && cellI.Value is double)
                                {
                                    summaryValues1[condition]["I"] += (double)cellI.Value;
                                }

                                // ✅ 新增：处理L列(12) - 增值税进项税
                                Excel.Range cellL = sourceSheet1.Cells[row, 12] as Excel.Range;
                                if (cellL.Value != null && cellL.Value is double)
                                {
                                    summaryValues1[condition]["L"] += (double)cellL.Value;
                                }

                                // 处理M列(13)
                                Excel.Range cellM = sourceSheet1.Cells[row, 13] as Excel.Range;
                                if (cellM.Value != null && cellM.Value is double)
                                {
                                    summaryValues1[condition]["M"] += (double)cellM.Value;
                                }
                            }
                            successCount3++;
                        }

                        processLog.AppendLine(string.Format("[成功] 处理文件 {0}", System.IO.Path.GetFileName(filePath)));
                    }
                    catch (Exception ex)
                    {
                        processLog.AppendLine(string.Format("[错误] 处理文件 {0} 时出错: {1}", System.IO.Path.GetFileName(filePath), ex.Message));
                    }
                    finally
                    {
                        if (sourceWorkbook != null)
                        {
                            sourceWorkbook.Close(false);
                        }
                    }
                }

                // ▼▼▼ 新增的合计行功能 ▼▼▼
                if (successCount4 > 0)
                {
                    // 计算合计行位置（最后一行数据+1）
                    int totalRow1 = targetStartRow1 + successCount1;

                    // 1. 合并A和B列并写入"合计"
                    Excel.Range mergeRange1 = targetSheet1.Range[
                        targetSheet1.Cells[totalRow1, 1], // A列
                        targetSheet1.Cells[totalRow1, 2]  // B列
                    ];
                    mergeRange1.Merge();
                    mergeRange1.Value = "合计";
                    mergeRange1.HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter;
                    mergeRange1.Font.Bold = true;

                    // 2. 在C到N列添加求和公式
                    for (int col = 3; col <= 14; col++) // C(3)到N(14)列
                    {
                        Excel.Range sumRange1 = targetSheet1.Range[
                            targetSheet1.Cells[targetStartRow1, col], // 起始行
                            targetSheet1.Cells[totalRow1 - 1, col]     // 结束行(上一行)
                        ];

                        targetSheet1.Cells[totalRow1, col].Formula = string.Format("=SUM({0})", sumRange1.Address);
                        targetSheet1.Cells[totalRow1, col].Font.Bold = true;

                        // 设置数字格式（根据列类型调整）
                        if (col >= 3 && col <= 13) // 假设这些列是数值
                        {
                            targetSheet1.Cells[totalRow1, col].NumberFormat = "#,##0.00";
                        }
                    }

                    // 3. 在O列添加公式 (N列/C列)*100
                    Excel.Range oCell1 = targetSheet1.Cells[totalRow1, 15]; // O列
                    oCell1.Formula = string.Format("=IF({0}<>0,({0}/{1})*100,0)", targetSheet1.Cells[totalRow1, 14].Address, targetSheet1.Cells[totalRow1, 3].Address);
                    oCell1.Font.Bold = true;

                    // 设置合计行边框
                    Excel.Range totalRowRange1 = targetSheet1.Range[
                        targetSheet1.Cells[totalRow1, 1],
                        targetSheet1.Cells[totalRow1, 15]
                    ];
                    totalRowRange1.Borders.LineStyle = Excel.XlLineStyle.xlContinuous;
                    totalRowRange1.Borders.Weight = Excel.XlBorderWeight.xlThin;

                    // 删除合计行之后的所有行
                    if (totalRow1 + 1 <= targetSheet1.Rows.Count)
                    {
                        Excel.Range rowsToDelete = targetSheet1.Rows[string.Format("{0}:{1}", totalRow1 + 1, targetSheet1.Rows.Count)];
                        rowsToDelete.Delete(Excel.XlDeleteShiftDirection.xlShiftUp);
                        Marshal.ReleaseComObject(rowsToDelete);
                        Marshal.ReleaseComObject(totalRowRange1);
                    }
                }

                // 在附表1-工程建设概况总表添加合计行
                if (targetSheet3 != null && successCount1 > 0)
                {
                    int lastRow = targetSheet3.Cells[targetSheet3.Rows.Count, 2].End(Excel.XlDirection.xlUp).Row;
                    int totalRow = lastRow + 1;

                    // 获取最后一列的列号
                    int lastCol = targetSheet3.UsedRange.Columns.Count;

                    // 合并前两列并写入"合计"
                    Excel.Range mergeRange = targetSheet3.Range[
                        targetSheet3.Cells[totalRow, 1],
                        targetSheet3.Cells[totalRow, 2]
                    ];
                    mergeRange.Merge();
                    mergeRange.Value = "合计";
                    mergeRange.HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter;
                    mergeRange.Font.Bold = true;

                    // 对E、G、I列求合计
                    int[] sumCols = { 5, 7, 9 }; // E、G、I列的索引
                    foreach (int col in sumCols)
                    {
                        Excel.Range sumRange = targetSheet3.Range[
                            targetSheet3.Cells[6, col], // 起始行(第6行)
                            targetSheet3.Cells[lastRow, col] // 结束行(最后一行数据)
                        ];
                        targetSheet3.Cells[totalRow, col].Formula = string.Format("=SUM({0})", sumRange.Address);
                        targetSheet3.Cells[totalRow, col].Font.Bold = true;
                        targetSheet3.Cells[totalRow, col].NumberFormat = "#,##0.00";
                    }

                    // 设置合计行边框（使用动态获取的最后一列）
                    Excel.Range totalRowRange = targetSheet3.Range[
                        targetSheet3.Cells[totalRow, 1],
                        targetSheet3.Cells[totalRow, lastCol]
                    ];
                    totalRowRange.Borders.LineStyle = Excel.XlLineStyle.xlContinuous;
                    totalRowRange.Borders.Weight = Excel.XlBorderWeight.xlThin;

                    // 删除合计行之后的所有行
                    if (totalRow + 1 <= targetSheet3.Rows.Count)
                    {
                        Excel.Range rowsToDelete = targetSheet3.Rows[string.Format("{0}:{1}", totalRow + 1, targetSheet3.Rows.Count)];
                        rowsToDelete.Delete(Excel.XlDeleteShiftDirection.xlShiftUp);
                        Marshal.ReleaseComObject(rowsToDelete);
                        Marshal.ReleaseComObject(totalRowRange);
                    }
                }

                // 在附表5-工程建设规模统计总表添加合计行
                if (targetSheet5 != null && successCount5 > 0)
                {
                    int lastRow = targetSheet5.Cells[targetSheet5.Rows.Count, 2].End(Excel.XlDirection.xlUp).Row;
                    int totalRow = lastRow + 1;

                    // 合并前两列并写入"合计"
                    Excel.Range mergeRange = targetSheet5.Range[
                        targetSheet5.Cells[totalRow, 1],
                        targetSheet5.Cells[totalRow, 2]
                    ];
                    mergeRange.Merge();
                    mergeRange.Value = "合计";
                    mergeRange.HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter;
                    mergeRange.Font.Bold = true;

                    // 对C列及之后的列求合计
                    int lastCol = targetSheet5.UsedRange.Columns.Count;
                    for (int col = 3; col <= lastCol; col++)
                    {
                        Excel.Range sumRange = targetSheet5.Range[
                            targetSheet5.Cells[9, col], // 起始行(第9行)
                            targetSheet5.Cells[lastRow, col] // 结束行(最后一行数据)
                        ];
                        targetSheet5.Cells[totalRow, col].Formula = string.Format("=SUM({0})", sumRange.Address);
                        targetSheet5.Cells[totalRow, col].Font.Bold = true;
                        targetSheet5.Cells[totalRow, col].NumberFormat = "#,##0.00";
                    }

                    // 设置合计行边框
                    Excel.Range totalRowRange = targetSheet5.Range[
                        targetSheet5.Cells[totalRow, 1],
                        targetSheet5.Cells[totalRow, lastCol]
                    ];
                    totalRowRange.Borders.LineStyle = Excel.XlLineStyle.xlContinuous;
                    totalRowRange.Borders.Weight = Excel.XlBorderWeight.xlThin;

                    // 删除合计行之后的所有行
                    if (totalRow + 1 <= targetSheet5.Rows.Count)
                    {
                        Excel.Range rowsToDelete = targetSheet5.Rows[string.Format("{0}:{1}", totalRow + 1, targetSheet5.Rows.Count)];
                        rowsToDelete.Delete(Excel.XlDeleteShiftDirection.xlShiftUp);
                        Marshal.ReleaseComObject(rowsToDelete);
                        Marshal.ReleaseComObject(totalRowRange);
                    }
                }


                // 将求和结果填充到汇总表
                // 将求和结果填充到汇总表
                int rowOffset = 8; // 汇总表起始行
                foreach (var condition in filterConditions2)
                {
                    var values = summaryValues1[condition];

                    // 填充C-F列
                    targetSheet2.Cells[rowOffset, 3].Value = values["C"]; // C列
                    targetSheet2.Cells[rowOffset, 4].Value = values["D"]; // D列
                    targetSheet2.Cells[rowOffset, 5].Value = values["E"]; // E列
                    targetSheet2.Cells[rowOffset, 6].Value = values["F"]; // F列

                    // 填充H列
                    targetSheet2.Cells[rowOffset, 8].Value = values["H"]; // H列

                    // 填充I列
                    targetSheet2.Cells[rowOffset, 9].Value = values["I"]; // I列

                    // ✅ 新增：填充L列（增值税进项税）
                    targetSheet2.Cells[rowOffset, 12].Value = values["L"]; // L列

                    // 填充M列
                    targetSheet2.Cells[rowOffset, 13].Value = values["M"]; // M列

                    // 设置单元格格式为数字格式，确保负数显示负号
                    for (int col = 3; col <= 6; col++) // C-F列
                    {
                        targetSheet2.Cells[rowOffset, col].NumberFormat = "#,##0.00";
                    }
                    targetSheet2.Cells[rowOffset, 8].NumberFormat = "#,##0.00"; // H列
                    targetSheet2.Cells[rowOffset, 9].NumberFormat = "#,##0.00"; // I列
                    targetSheet2.Cells[rowOffset, 12].NumberFormat = "#,##0.00"; // ✅ L列
                    targetSheet2.Cells[rowOffset, 13].NumberFormat = "#,##0.00"; // M列

                    rowOffset++;
                }

                // 保存模板文件(另存为新文件)
                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = "Excel Files|*.xlsx";
                saveFileDialog.Title = "保存结果文件";
                saveFileDialog.FileName = string.Format("汇总表_{0}.xlsx", DateTime.Now.ToString("yyyyMMdd_HHmmss"));

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    // 保存文件
                    templateWorkbook.SaveAs(saveFileDialog.FileName);
                    templateWorkbook.Close(false);

                    // 显示处理结果
                    string resultMessage = string.Format("处理完成!\n附表1: 共找到 {0} 条符合条件的记录\n附表2: 共找到 {1} 条符合条件的记录\n附表3: 共找到 {2} 条符合条件的记录\n附表4: 共找到 {3} 条符合条件的记录\n附表5: 共找到 {4} 条符合条件的记录\n附表6: 共处理 {5} 个文件的报告信息\n结果文件已保存，可以手动打开查看。", successCount1, successCount2, successCount3, successCount4, successCount5, successCount6);
                    MessageBox.Show(resultMessage, "完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    templateWorkbook.Close(false);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(string.Format("处理过程中出错: {0}", ex.Message), "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 确保恢复Excel设置
                if (excelApp != null)
                {
                    try
                    {
                        excelApp.ScreenUpdating = originalScreenUpdating;
                        excelApp.DisplayAlerts = originalDisplayAlerts;
                        excelApp.Calculation = originalCalculation;
                    }
                    catch
                    {
                        // 如果恢复Excel属性失败，使用默认值
                        try
                        {
                            excelApp.ScreenUpdating = true;
                            excelApp.DisplayAlerts = true;
                            excelApp.Calculation = Excel.XlCalculation.xlCalculationAutomatic;
                        }
                        catch
                        {
                            // 忽略恢复失败的错误
                        }
                    }
                }
            }
        }

        //数据抓取合并
        private void button14_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 1. 选择模板文件
                OpenFileDialog templateDialog = new OpenFileDialog
                {
                    Filter = "Excel Files|*.xls;*.xlsx;*.xlsm",
                    Title = "选择模板文件"
                };
                if (templateDialog.ShowDialog() != DialogResult.OK) return;

                Excel.Application excelApp = Globals.ThisAddIn.Application;
                Excel.Workbook templateWorkbook = excelApp.Workbooks.Open(templateDialog.FileName);

                // 2. 选择模板工作表
                var sheetSelector = new Form { Text = "选择模板工作表", Width = 300 };
                ComboBox sheetCombo = new ComboBox { Dock = DockStyle.Top };
                Button confirmBtn = new Button { Text = "确定", Dock = DockStyle.Bottom };

                foreach (Excel.Worksheet sheet in templateWorkbook.Sheets)
                {
                    sheetCombo.Items.Add(sheet.Name);
                }
                sheetCombo.SelectedIndex = 0;
                sheetSelector.Controls.Add(sheetCombo);
                sheetSelector.Controls.Add(confirmBtn);
                confirmBtn.Click += (s, args) => sheetSelector.DialogResult = DialogResult.OK;
                if (sheetSelector.ShowDialog() != DialogResult.OK) return;

                string targetSheetName = sheetCombo.SelectedItem.ToString();
                Excel.Worksheet targetSheet = templateWorkbook.Sheets[targetSheetName];

                // 3. 选择模板数据起始位置
                int startRow = 1;
                int startCol = 1;
                using (Form positionForm = new Form { Text = "选择开始填充位置", Width = 300 })
                {
                    Label rowLabel = new Label { Text = "开始行号:", Dock = DockStyle.Top };
                    NumericUpDown rowNum = new NumericUpDown { Minimum = 1, Maximum = 10000, Value = 1, Dock = DockStyle.Top };
                    Label colLabel = new Label { Text = "开始列号:", Dock = DockStyle.Top };
                    NumericUpDown colNum = new NumericUpDown { Minimum = 1, Maximum = 100, Value = 1, Dock = DockStyle.Top };
                    Button posConfirm = new Button { Text = "确定", Dock = DockStyle.Bottom };

                    posConfirm.Click += (s2, e2) =>
                    {
                        startRow = (int)rowNum.Value;
                        startCol = (int)colNum.Value;
                        positionForm.DialogResult = DialogResult.OK;
                    };

                    positionForm.Controls.Add(posConfirm);
                    positionForm.Controls.Add(colNum);
                    positionForm.Controls.Add(colLabel);
                    positionForm.Controls.Add(rowNum);
                    positionForm.Controls.Add(rowLabel);

                    if (positionForm.ShowDialog() != DialogResult.OK) return;
                }

                // 4. 选择多个源文件
                OpenFileDialog sourceDialog = new OpenFileDialog
                {
                    Filter = "Excel Files|*.xls;*.xlsx;*.xlsm",
                    Multiselect = true,
                    Title = "选择要合并的源文件"
                };
                if (sourceDialog.ShowDialog() != DialogResult.OK) return;

                // 5. 配置一次所有文件共用的 Sheet 和区域
                var mergeConfigs = new List<MergeConfig>();
                var commonConfig = new MergeConfig();

                Excel.Workbook sampleWorkbook = excelApp.Workbooks.Open(sourceDialog.FileNames[0]);

                var configForm = new Form { Text = "配置源数据区域", Width = 400 };
                ComboBox sourceSheetCombo = new ComboBox { Dock = DockStyle.Top };
                foreach (Excel.Worksheet sheet in sampleWorkbook.Sheets)
                {
                    sourceSheetCombo.Items.Add(sheet.Name);
                }
                sourceSheetCombo.SelectedIndex = 0;

                NumericUpDown startRowNum = new NumericUpDown { Minimum = 1, Maximum = 10000, Value = 1, Dock = DockStyle.Top };
                NumericUpDown startColNum = new NumericUpDown { Minimum = 1, Maximum = 100, Value = 1, Dock = DockStyle.Top };
                NumericUpDown endRowNum = new NumericUpDown { Minimum = 1, Maximum = 10000, Value = 1, Dock = DockStyle.Top };
                NumericUpDown endColNum = new NumericUpDown { Minimum = 1, Maximum = 100, Value = 1, Dock = DockStyle.Top };

                Button saveBtn = new Button { Text = "保存配置", Dock = DockStyle.Bottom };
                configForm.Controls.Add(saveBtn);
                configForm.Controls.Add(endColNum);
                configForm.Controls.Add(new Label { Text = "结束列号:", Dock = DockStyle.Top });
                configForm.Controls.Add(endRowNum);
                configForm.Controls.Add(new Label { Text = "结束行号:", Dock = DockStyle.Top });
                configForm.Controls.Add(startColNum);
                configForm.Controls.Add(new Label { Text = "起始列号:", Dock = DockStyle.Top });
                configForm.Controls.Add(startRowNum);
                configForm.Controls.Add(new Label { Text = "起始行号:", Dock = DockStyle.Top });
                configForm.Controls.Add(sourceSheetCombo);
                configForm.Controls.Add(new Label { Text = "选择工作表:", Dock = DockStyle.Top });

                saveBtn.Click += (s, args) =>
                {
                    commonConfig.SheetName = sourceSheetCombo.SelectedItem.ToString();
                    commonConfig.StartRow = (int)startRowNum.Value;
                    commonConfig.StartCol = (int)startColNum.Value;
                    commonConfig.EndRow = (int)endRowNum.Value;
                    commonConfig.EndCol = (int)endColNum.Value;
                    configForm.DialogResult = DialogResult.OK;
                };

                if (configForm.ShowDialog() != DialogResult.OK)
                {
                    sampleWorkbook.Close(false);
                    return;
                }
                sampleWorkbook.Close(false);

                foreach (string filePath in sourceDialog.FileNames)
                {
                    mergeConfigs.Add(new MergeConfig
                    {
                        FilePath = filePath,
                        SheetName = commonConfig.SheetName,
                        StartRow = commonConfig.StartRow,
                        StartCol = commonConfig.StartCol,
                        EndRow = commonConfig.EndRow,
                        EndCol = commonConfig.EndCol
                    });
                }

                // 6. 合并数据到模板
                excelApp.ScreenUpdating = false;
                excelApp.DisplayAlerts = false;

                int targetRow = startRow;
                int targetCol = startCol;

                foreach (var config in mergeConfigs)
                {
                    Excel.Workbook sourceWorkbook = excelApp.Workbooks.Open(config.FilePath);
                    Excel.Worksheet sourceSheet = sourceWorkbook.Sheets[config.SheetName];

                    for (int row = config.StartRow; row <= config.EndRow; row++)
                    {
                        for (int col = config.StartCol; col <= config.EndCol; col++)
                        {
                            Excel.Range targetCell = targetSheet.Cells[targetRow, targetCol + (col - config.StartCol)];

                            // 如果模板单元格有公式，则跳过（保留公式）
                            if (targetCell.HasFormula)
                            {
                                continue;
                            }

                            Excel.Range sourceCell = sourceSheet.Cells[row, col];
                            object value = sourceCell.Value;

                            if (value != null && double.TryParse(value.ToString(), out double number))
                            {
                                // 数字格式化：千位分隔符+两位小数
                                targetCell.Value = number.ToString("N2");
                            }
                            else
                            {
                                //处理没有公式的单元格
                                targetCell.Value = value;
                            }
                        }
                        targetRow++;
                    }

                    sourceWorkbook.Close(false);
                }

                // 7. 保存合并结果
                SaveFileDialog saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx",
                    Title = "保存合并结果",
                    FileName = string.Format("合并结果_{0}.xlsx", DateTime.Now.ToString("yyyyMMdd_HHmmss"))
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    templateWorkbook.SaveAs(saveDialog.FileName);
                    MessageBox.Show("合并完成!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                templateWorkbook.Close(false);
            }
            catch (Exception ex)
            {
                MessageBox.Show(string.Format("合并过程中出错: {0}", ex.Message), "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                Excel.Application excelApp = Globals.ThisAddIn.Application;
                excelApp.ScreenUpdating = true;
                excelApp.DisplayAlerts = true;
            }
        }
        // 配置类
        public class MergeConfig
        {
            public string FilePath { get; set; }
            public string SheetName { get; set; }
            public int StartRow { get; set; }
            public int StartCol { get; set; }
            public int EndRow { get; set; }
            public int EndCol { get; set; }
        }
        private void button15_Click(object sender, RibbonControlEventArgs e)
        {
            Excel.Application excelApp = Globals.ThisAddIn.Application;
            Excel.Workbook templateWorkbook = null;
            List<Excel.Workbook> sourceWorkbooks = new List<Excel.Workbook>();

            try
            {
                // 1. 选择模板文件
                OpenFileDialog templateDialog = new OpenFileDialog();
                templateDialog.Filter = "Excel文件|*.xls;*.xlsx;*.xlsm";
                templateDialog.Title = "选择模板文件";
                if (templateDialog.ShowDialog() != DialogResult.OK) return;

                // 2. 打开模板文件
                templateWorkbook = excelApp.Workbooks.Open(templateDialog.FileName);

                // 3. 选择模板工作表和起始位置
                var targetConfig = new TemplateConfigForm(templateWorkbook);
                if (targetConfig.ShowDialog() != DialogResult.OK) return;

                Excel.Worksheet targetSheet = templateWorkbook.Sheets[targetConfig.SelectedSheetName];
                int targetStartRow = targetConfig.StartRow;
                int targetStartCol = targetConfig.StartCol;

                // 4. 选择源文件
                OpenFileDialog sourceDialog = new OpenFileDialog();
                sourceDialog.Multiselect = true;
                sourceDialog.Filter = "Excel文件|*.xls;*.xlsx;*.xlsm";
                sourceDialog.Title = "选择要合并的源文件（可多选）";
                if (sourceDialog.ShowDialog() != DialogResult.OK) return;

                // 5. 批量配置所有源文件设置
                var batchConfig = new BatchConfigForm(sourceDialog.FileNames);
                if (batchConfig.ShowDialog() != DialogResult.OK) return;

                // 6. 执行合并与汇总
                excelApp.ScreenUpdating = false;
                excelApp.DisplayAlerts = false;

                Dictionary<int, Dictionary<int, double>> summaryData = new Dictionary<int, Dictionary<int, double>>();
                Dictionary<int, Dictionary<int, string>> textData = new Dictionary<int, Dictionary<int, string>>();

                // 记录源行号 -> 模板目标行号的映射
                Dictionary<int, int> rowMapping = new Dictionary<int, int>();
                int currentTargetRow = targetStartRow;

                foreach (var config in batchConfig.Configurations)
                {
                    Excel.Workbook sourceWorkbook = excelApp.Workbooks.Open(config.FilePath);
                    sourceWorkbooks.Add(sourceWorkbook);

                    Excel.Worksheet sourceSheet = sourceWorkbook.Sheets[config.SheetName];

                    foreach (int row in config.SelectedRows)
                    {
                        if (row < 1 || row > sourceSheet.UsedRange.Rows.Count) continue;

                        if (!rowMapping.ContainsKey(row))
                        {
                            rowMapping[row] = currentTargetRow++;
                        }
                        int targetRow = rowMapping[row];

                        Excel.Range sourceRowRange = sourceSheet.Range[
                            sourceSheet.Cells[row, 1],
                            sourceSheet.Cells[row, sourceSheet.UsedRange.Columns.Count]
                        ];

                        for (int c = 1; c <= sourceRowRange.Columns.Count; c++)
                        {
                            Excel.Range targetCell = targetSheet.Cells[targetRow, c];

                            // 如果模板单元格有公式，则跳过（保留公式）
                            if (targetCell.HasFormula)
                            {
                                continue;
                            }

                            Excel.Range sourceCell = sourceRowRange.Cells[1, c];
                            object value = sourceCell.Value;

                            if (value is double || value is int)
                            {
                                if (!summaryData.ContainsKey(targetRow))
                                    summaryData[targetRow] = new Dictionary<int, double>();
                                if (!summaryData[targetRow].ContainsKey(c))
                                    summaryData[targetRow][c] = 0;

                                summaryData[targetRow][c] += Convert.ToDouble(value);
                            }
                            else if (value != null)
                            {
                                if (!textData.ContainsKey(targetRow))
                                    textData[targetRow] = new Dictionary<int, string>();

                                if (!textData[targetRow].ContainsKey(c) || string.IsNullOrEmpty(textData[targetRow][c]))
                                {
                                    textData[targetRow][c] = FormatCellValue(value, sourceCell);
                                }
                            }
                        }
                    }
                }

                // 7. 写入合并数据到模板
                foreach (var row in summaryData.Keys)
                {
                    foreach (var col in summaryData[row].Keys)
                    {
                        // 再次检查目标单元格是否有公式（以防在循环过程中被修改）
                        if (!targetSheet.Cells[row, col].HasFormula)
                        {
                            //targetSheet.Cells[row, col].Value = summaryData[row][col];
                            double val = summaryData[row][col];
                            Excel.Range cell = targetSheet.Cells[row, col];
                            cell.Value = val;
                            // 设置单元格数字格式，千位分隔符 + 两位小数
                            cell.NumberFormat = "#,##0.00";
                        }
                    }
                }

                foreach (var row in textData.Keys)
                {
                    foreach (var col in textData[row].Keys)
                    {
                        // 再次检查目标单元格是否有公式（以防在循环过程中被修改）
                        if (!targetSheet.Cells[row, col].HasFormula && targetSheet.Cells[row, col].Value == null)
                        {
                            targetSheet.Cells[row, col].Value = textData[row][col];
                        }
                    }
                }

                // 8. 保存文件
                SaveFileDialog saveDialog = new SaveFileDialog();
                saveDialog.Filter = "Excel文件|*.xlsx";
                saveDialog.Title = "保存合并结果";
                saveDialog.FileName = string.Format("合并汇总_{0}.xlsx", DateTime.Now.ToString("yyyyMMdd_HHmmss"));

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    templateWorkbook.SaveAs(saveDialog.FileName);
                    MessageBox.Show(string.Format("成功合并 {0} 个文件！", batchConfig.Configurations.Count), "完成",
                                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(string.Format("处理过程中出错:\n{0}", ex.Message), "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                excelApp.ScreenUpdating = true;
                excelApp.DisplayAlerts = true;

                foreach (var wb in sourceWorkbooks)
                {
                    wb.Close(false);
                }

                templateWorkbook?.Close(false);
            }
        }

        public class TemplateConfigForm : Form
        {
            public string SelectedSheetName { get; private set; }
            public int StartRow { get; private set; }
            public int StartCol { get; private set; }

            private ComboBox sheetComboBox;
            private NumericUpDown rowNumeric;
            private NumericUpDown colNumeric;

            public TemplateConfigForm(Excel.Workbook workbook)
            {
                this.Text = "模板配置";
                this.Width = 400;
                this.Height = 250;
                this.FormBorderStyle = FormBorderStyle.FixedDialog;
                this.StartPosition = FormStartPosition.CenterScreen;

                Label sheetLabel = new Label { Text = "选择工作表：", Top = 20, Left = 20, Width = 100 };
                sheetComboBox = new ComboBox { Left = 130, Top = 18, Width = 200, DropDownStyle = ComboBoxStyle.DropDownList };

                Label rowLabel = new Label { Text = "起始行号：", Top = 60, Left = 20, Width = 100 };
                rowNumeric = new NumericUpDown { Left = 130, Top = 58, Width = 80, Minimum = 1, Maximum = 10000, Value = 2 };

                Label colLabel = new Label { Text = "起始列号：", Top = 100, Left = 20, Width = 100 };
                colNumeric = new NumericUpDown { Left = 130, Top = 98, Width = 80, Minimum = 1, Maximum = 100, Value = 1 };

                Button okButton = new Button { Text = "确定", Left = 80, Top = 150, Width = 100 };
                Button cancelButton = new Button { Text = "取消", Left = 200, Top = 150, Width = 100 };

                okButton.Click += (s, e) =>
                {
                    if (sheetComboBox.SelectedItem == null)
                    {
                        MessageBox.Show("请选择工作表", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    SelectedSheetName = sheetComboBox.SelectedItem.ToString();
                    StartRow = (int)rowNumeric.Value;
                    StartCol = (int)colNumeric.Value;
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                };

                cancelButton.Click += (s, e) =>
                {
                    this.DialogResult = DialogResult.Cancel;
                    this.Close();
                };

                // 加载工作表名
                foreach (Excel.Worksheet sheet in workbook.Sheets)
                {
                    sheetComboBox.Items.Add(sheet.Name);
                }

                if (sheetComboBox.Items.Count > 0)
                    sheetComboBox.SelectedIndex = 0;

                this.Controls.AddRange(new Control[]
                {
            sheetLabel, sheetComboBox,
            rowLabel, rowNumeric,
            colLabel, colNumeric,
            okButton, cancelButton
                });
            }
        }
        public class SourceConfig
        {
            public string FilePath { get; set; }
            public string SheetName { get; set; }
            public List<int> SelectedRows { get; set; }
        }

        public class BatchConfigForm : Form
        {
            public List<SourceConfig> Configurations { get; private set; }
            private string[] filePaths;
            private ComboBox sheetCombo;
            private CheckedListBox rowsList;
            private DataGridView filesGrid;

            public BatchConfigForm(string[] filePaths)
            {
                Configurations = new List<SourceConfig>();
                this.filePaths = filePaths;
                this.Text = "批量配置源文件";
                this.Width = 800;
                this.Height = 600;
                this.StartPosition = FormStartPosition.CenterScreen;

                InitializeUI();
            }

            private void InitializeUI()
            {
                TableLayoutPanel mainPanel = new TableLayoutPanel
                {
                    Dock = DockStyle.Fill,
                    ColumnCount = 1,
                    RowCount = 4
                };
                mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));
                mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 30));
                mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 60));
                mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));

                Label titleLabel = new Label
                {
                    Text = "批量配置所有源文件设置",
                    Dock = DockStyle.Fill,
                    TextAlign = ContentAlignment.MiddleCenter,
                    Font = new Font("Microsoft YaHei", 10, FontStyle.Bold)
                };
                mainPanel.Controls.Add(titleLabel, 0, 0);

                filesGrid = new DataGridView
                {
                    Dock = DockStyle.Fill,
                    AllowUserToAddRows = false,
                    MultiSelect = true,
                    SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                    ReadOnly = true
                };
                filesGrid.Columns.Add("FileName", "文件名");
                filesGrid.Columns.Add("SheetName", "工作表");
                filesGrid.Columns.Add("SelectedRows", "选择的行");

                foreach (var file in filePaths)
                {
                    filesGrid.Rows.Add(Path.GetFileName(file), "Sheet1", "未配置");
                }

                Panel configPanel = new Panel { Dock = DockStyle.Fill };
                Label sheetLabel = new Label { Text = "工作表:", Top = 10, Left = 20 };

                sheetCombo = new ComboBox
                {
                    Top = 8,
                    Left = 80,
                    Width = 200,
                    DropDownStyle = ComboBoxStyle.DropDownList
                };

                sheetCombo.SelectedIndexChanged += (s, e) =>
                {
                    if (filesGrid.SelectedRows.Count > 0)
                    {
                        string filePath = filePaths[filesGrid.SelectedRows[0].Index];
                        string sheetName = sheetCombo.SelectedItem.ToString();
                        LoadRowPreview(filePath, sheetName);
                    }
                };

                Label rowsLabel = new Label { Text = "选择行:", Top = 40, Left = 20 };
                rowsList = new CheckedListBox
                {
                    Top = 70,
                    Left = 20,
                    Width = 500,
                    Height = 200,
                    CheckOnClick = true
                };

                Button selectAllBtn = new Button { Text = "全选", Top = 280, Left = 20, Width = 80 };
                Button clearBtn = new Button { Text = "清空", Top = 280, Left = 110, Width = 80 };
                Button applyBtn = new Button { Text = "应用到所有文件", Top = 280, Left = 200, Width = 120 };

                selectAllBtn.Click += (s, e) =>
                {
                    for (int i = 0; i < rowsList.Items.Count; i++)
                        rowsList.SetItemChecked(i, true);
                };

                clearBtn.Click += (s, e) =>
                {
                    for (int i = 0; i < rowsList.Items.Count; i++)
                        rowsList.SetItemChecked(i, false);
                };

                applyBtn.Click += (s, e) =>
                {
                    if (sheetCombo.SelectedItem == null)
                    {
                        MessageBox.Show("请先选择工作表", "提示");
                        return;
                    }

                    List<int> selectedRows = new List<int>();
                    for (int i = 0; i < rowsList.Items.Count; i++)
                    {
                        if (rowsList.GetItemChecked(i))
                        {
                            selectedRows.Add(i + 1);
                        }
                    }

                    if (selectedRows.Count == 0)
                    {
                        MessageBox.Show("请至少选择一行", "提示");
                        return;
                    }

                    foreach (DataGridViewRow row in filesGrid.Rows)
                    {
                        row.Cells["SheetName"].Value = sheetCombo.SelectedItem.ToString();
                        row.Cells["SelectedRows"].Value = string.Join(",", selectedRows);
                    }

                    MessageBox.Show("配置已应用到所有文件", "提示");
                };

                configPanel.Controls.AddRange(new Control[] {
            sheetLabel, sheetCombo,
            rowsLabel, rowsList,
            selectAllBtn, clearBtn, applyBtn
        });

                Panel buttonPanel = new Panel { Dock = DockStyle.Fill };
                Button okBtn = new Button { Text = "确定", Width = 100, Top = 5, Left = 200 };

                okBtn.Click += (s, e) =>
                {
                    for (int i = 0; i < filesGrid.Rows.Count; i++)
                    {
                        var selectedRowsStr = filesGrid.Rows[i].Cells["SelectedRows"].Value.ToString();
                        if (selectedRowsStr == "未配置")
                        {
                            MessageBox.Show(string.Format("文件 {0} 未配置", filesGrid.Rows[i].Cells["FileName"].Value), "错误");
                            return;
                        }

                        var rows = selectedRowsStr.Split(',').Select(int.Parse).ToList();

                        Configurations.Add(new SourceConfig
                        {
                            FilePath = filePaths[i],
                            SheetName = filesGrid.Rows[i].Cells["SheetName"].Value.ToString(),
                            SelectedRows = rows
                        });
                    }

                    this.DialogResult = DialogResult.OK;
                };

                buttonPanel.Controls.Add(okBtn);

                filesGrid.SelectionChanged += (s, e) =>
                {
                    if (filesGrid.SelectedRows.Count > 0)
                    {
                        LoadSheetList(sheetCombo, filePaths[filesGrid.SelectedRows[0].Index]);
                    }
                };

                mainPanel.Controls.Add(filesGrid, 0, 1);
                mainPanel.Controls.Add(configPanel, 0, 2);
                mainPanel.Controls.Add(buttonPanel, 0, 3);

                this.Controls.Add(mainPanel);

                if (filePaths.Length > 0)
                {
                    LoadSheetList(sheetCombo, filePaths[0]);
                }
            }

            private void LoadSheetList(ComboBox comboBox, string filePath)
            {
                comboBox.Items.Clear();

                Excel.Application excelApp = Globals.ThisAddIn.Application;
                Excel.Workbook workbook = null;

                try
                {
                    workbook = excelApp.Workbooks.Open(filePath, ReadOnly: true);

                    foreach (Excel.Worksheet sheet in workbook.Sheets)
                    {
                        comboBox.Items.Add(sheet.Name);
                    }

                    if (comboBox.Items.Count > 0)
                        comboBox.SelectedIndex = 0;
                }
                finally
                {
                    workbook?.Close(false);
                }
            }

            private void LoadRowPreview(string filePath, string sheetName)
            {
                rowsList.Items.Clear();

                Excel.Application excelApp = Globals.ThisAddIn.Application;
                Excel.Workbook workbook = null;

                try
                {
                    workbook = excelApp.Workbooks.Open(filePath, ReadOnly: true);
                    Excel.Worksheet sheet = workbook.Sheets[sheetName];

                    for (int row = 1; row <= 500; row++)
                    {
                        // 使用 Value2 获取实际值，避免列太窄时显示 ####
                        Excel.Range col1Cell = (Excel.Range)sheet.Cells[row, 1];
                        Excel.Range col2Cell = (Excel.Range)sheet.Cells[row, 2];
                        object col1Obj = col1Cell.Value2;
                        object col2Obj = col2Cell.Value2;

                        string col1 = Ribbon1.FormatCellValue(col1Obj, col1Cell);
                        string col2 = Ribbon1.FormatCellValue(col2Obj, col2Cell);

                        string label = string.Format("第{0}行：{1} | {2}", row, col1, col2);
                        rowsList.Items.Add(label);
                    }
                }
                finally
                {
                    workbook?.Close(false);
                }
            }
        }

        private void button16_Click(object sender, RibbonControlEventArgs e)
        {
            _ = UpdateChecker.CheckForUpdateAsync(true); // 手动检查更新
        }

        // 数据转置功能
        private void buttonTranspose_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                Excel.Application excelApp = Globals.ThisAddIn.Application;
                Excel.Workbook workbook = excelApp.ActiveWorkbook;
                Excel.Worksheet worksheet = workbook.ActiveSheet;

                if (workbook == null || worksheet == null)
                {
                    MessageBox.Show("请先打开一个Excel工作簿！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 显示转置设置窗体
                using (TransposeForm transposeForm = new TransposeForm())
                {
                    if (transposeForm.ShowDialog() == DialogResult.OK)
                    {
                        PerformTranspose(worksheet, transposeForm.SourceRange, transposeForm.StartRow, transposeForm.TargetRow, transposeForm.TargetColumn);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("转置操作失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 执行数据转置操作
        /// </summary>
        /// <param name="worksheet">工作表</param>
        /// <param name="sourceRange">源范围</param>
        /// <param name="startRow">开始转置的行号</param>
        /// <param name="targetRow">目标起始行</param>
        /// <param name="targetColumn">目标列</param>
        private void PerformTranspose(Excel.Worksheet worksheet, string sourceRange, int startRow, int targetRow, string targetColumn)
        {
            try
            {
                // 解析源范围
                Excel.Range sourceRangeObj = worksheet.Range[sourceRange];
                if (sourceRangeObj == null)
                {
                    MessageBox.Show("无效的源范围！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 获取源范围的列数和行数
                int sourceColumns = sourceRangeObj.Columns.Count;
                int sourceRows = sourceRangeObj.Rows.Count;

                // 获取目标列的列号
                int targetColumnNumber = GetColumnNumber(targetColumn);

                // 检查开始行是否有效
                if (startRow < 1 || startRow > sourceRows)
                {
                    MessageBox.Show($"开始行号 {startRow} 超出源范围！\n源范围行数：1-{sourceRows}",
                        "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 先找到每列最后一个有数据的行号
                int actualEndRow = startRow - 1; // 初始化为开始行之前
                for (int col = 1; col <= sourceColumns; col++)
                {
                    for (int row = sourceRows; row >= startRow; row--) // 从最后一行向前查找
                    {
                        try
                        {
                            Excel.Range checkCell = sourceRangeObj.Cells[row, col];
                            object checkValue = checkCell?.Value2;

                            // 判断是否有数据（不删除空格，只判断是否为null或完全空白）
                            if (checkValue != null && !string.IsNullOrEmpty(checkValue.ToString()))
                            {
                                actualEndRow = Math.Max(actualEndRow, row);
                                if (checkCell != null)
                                {
                                    System.Runtime.InteropServices.Marshal.ReleaseComObject(checkCell);
                                }
                                break; // 找到该列最后有数据的行，跳出内层循环
                            }

                            if (checkCell != null)
                            {
                                System.Runtime.InteropServices.Marshal.ReleaseComObject(checkCell);
                            }
                        }
                        catch (Exception cellEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"检查单元格 [{row},{col}] 时出错: {cellEx.Message}");
                        }
                    }
                }

                // 如果没有找到任何数据，使用开始行作为结束行
                if (actualEndRow < startRow)
                {
                    actualEndRow = startRow;
                }

                // 收集从开始行到实际结束行的所有数据（包括空单元格，保留数据中的空格）
                List<object> dataToTranspose = new List<object>();

                // 按行优先的方式收集数据：从指定开始行到实际结束行
                for (int row = startRow; row <= actualEndRow; row++)
                {
                    // 遍历当前行的每一列
                    for (int col = 1; col <= sourceColumns; col++)
                    {
                        try
                        {
                            Excel.Range sourceCell = sourceRangeObj.Cells[row, col];
                            object cellValue = sourceCell?.Value2;

                            // 收集所有数据，包括空单元格，保留数据中的空格
                            dataToTranspose.Add(cellValue);

                            // 释放COM对象引用
                            if (sourceCell != null)
                            {
                                System.Runtime.InteropServices.Marshal.ReleaseComObject(sourceCell);
                            }
                        }
                        catch (Exception cellEx)
                        {
                            // 如果单个单元格访问失败，添加null值并继续
                            dataToTranspose.Add(null);
                            System.Diagnostics.Debug.WriteLine($"访问单元格 [{row},{col}] 时出错: {cellEx.Message}");
                        }
                    }
                }

                // 将收集的数据写入目标位置
                int currentRow = targetRow;
                int maxRow = 1048576; // Excel最大行数限制
                int writtenCount = 0; // 记录实际写入的数据数量

                foreach (object data in dataToTranspose)
                {
                    // 检查是否超出Excel行数限制
                    if (currentRow > maxRow)
                    {
                        MessageBox.Show($"目标行数超出Excel限制！最大行数：{maxRow}，当前尝试写入行：{currentRow}",
                            "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        break;
                    }

                    try
                    {
                        Excel.Range targetCell = worksheet.Cells[currentRow, targetColumnNumber];
                        if (targetCell != null)
                        {
                            // 设置单元格值（包括null值，保留原始数据结构）
                            targetCell.Value2 = data;
                            writtenCount++;

                            // 释放COM对象引用
                            System.Runtime.InteropServices.Marshal.ReleaseComObject(targetCell);
                        }
                        currentRow++;
                    }
                    catch (Exception cellEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"写入目标单元格 [{currentRow},{targetColumnNumber}] 时出错: {cellEx.Message}");
                        // 出错时停止写入，避免无限循环
                        MessageBox.Show($"写入目标单元格时出错，已停止转置操作。\n错误位置：第{currentRow}行，第{targetColumnNumber}列\n错误信息：{cellEx.Message}",
                            "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        break;
                    }
                }

                // 转置完成，源数据保持不变

                // 清除剪贴板
                try
                {
                    Globals.ThisAddIn.Application.CutCopyMode = 0;
                }
                catch { }

                // 释放源范围COM对象
                if (sourceRangeObj != null)
                {
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(sourceRangeObj);
                }

                string message = $"转置完成！\n" +
                    $"• 源范围：从第 {startRow} 行到第 {actualEndRow} 行\n" +
                    $"• 共处理了 {dataToTranspose.Count} 个单元格（包括空单元格）\n" +
                    $"• 转置到列 {targetColumn}，从第 {targetRow} 行开始\n" +
                    $"• 实际写入了 {writtenCount} 个单元格\n" +
                    $"• 源数据已保留，包含空格的数据已正确转置";

                MessageBox.Show(message, "转置完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show("执行转置时出错: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 将列字母转换为列号
        /// </summary>
        /// <param name="columnLetter">列字母</param>
        /// <returns>列号</returns>
        private int GetColumnNumber(string columnLetter)
        {
            columnLetter = columnLetter.ToUpper();
            int columnNumber = 0;
            for (int i = 0; i < columnLetter.Length; i++)
            {
                columnNumber = columnNumber * 26 + (columnLetter[i] - 'A' + 1);
            }
            return columnNumber;
        }

        /// <summary>
        /// 试用天数按钮点击事件
        /// </summary>
        private void trialDaysButton_Click(object sender, RibbonControlEventArgs e)
        {
            // 获取试用天数
            int remainingDays = TrialHelper.GetRemainingDays();
            MessageBox.Show(string.Format("试用期剩余天数：{0} 天", remainingDays), "试用信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// button17点击事件
        /// </summary>
        //链接报告（横排）
        private void button17_Click(object sender, RibbonControlEventArgs e)
        {
            Excel.Application excelApp = Globals.ThisAddIn.Application;
            Excel.Workbook workbook = excelApp.ActiveWorkbook;
            Excel.Worksheet worksheet = workbook.ActiveSheet;

            if (workbook == null || worksheet == null)
            {
                MessageBox.Show("请先打开一个Excel工作簿！");
                return;
            }

            // 固定使用第2行作为索引行，第3行作为数据行
            int indexRow = 2;
            int dataRow = 3;

            Excel.Range indexRowRange = worksheet.Rows[indexRow];
            Excel.Range dataRowRange = worksheet.Rows[dataRow];

            // 读取索引行和数据行
            Dictionary<string, Excel.Range> dataMap = new Dictionary<string, Excel.Range>();
            int colCount = indexRowRange.Columns.Count;

            for (int col = 1; col <= colCount; col++)
            {
                // 使用 Value2 获取实际值，避免列太窄时显示 ####
                object columnNameObj = indexRowRange.Cells[1, col].Value2;
                string columnName = (columnNameObj != null) ? columnNameObj.ToString().Trim() : "";

                if (!string.IsNullOrEmpty(columnName))
                {
                    Excel.Range dataCell = dataRowRange.Cells[1, col];
                    dataMap[string.Format("【{0}】", columnName)] = dataCell;
                }
            }

            // 选择 Word 模板
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Title = "选择 Word 模板文件",
                Filter = "Word 文件 (*.doc;*.docx)|*.doc;*.docx",
                Multiselect = false
            };

            if (openFileDialog.ShowDialog() != DialogResult.OK)
            {
                MessageBox.Show("未选择 Word 模板文件，操作取消！");
                return;
            }
            string templatePath = openFileDialog.FileName;

            // 选择保存路径，默认文件名与Excel文件名相同
            SaveFileDialog saveFileDialog = new SaveFileDialog
            {
                Title = "保存生成的 Word 报告",
                Filter = "Word 2007+ (*.docx)|*.docx|Word 97-2003 (*.doc)|*.doc",
                DefaultExt = "docx",
                FileName = Path.GetFileNameWithoutExtension(workbook.Name)
            };

            if (saveFileDialog.ShowDialog() != DialogResult.OK)
            {
                MessageBox.Show("未选择保存位置，操作取消！");
                return;
            }
            string outputPath = saveFileDialog.FileName;

            // 确定文件格式
            string fileExt = Path.GetExtension(outputPath).ToLower();
            object fileFormat = fileExt == ".docx" ?
                Word.WdSaveFormat.wdFormatXMLDocument :
                Word.WdSaveFormat.wdFormatDocument97;

            try
            {
                // 打开 Word 并处理替换
                Word.Application wordApp = new Word.Application();
                Word.Document doc = wordApp.Documents.Open(templatePath, ReadOnly: false, Visible: false);
                wordApp.Visible = false;

                // 提取文档中所有【xxx】格式的占位符
                List<string> placeholderList = ExtractPlaceholders(doc);

                // 遍历每个占位符，创建链接
                foreach (string placeholder in placeholderList)
                {
                    if (dataMap.ContainsKey(placeholder))
                    {
                        Excel.Range sourceCell = dataMap[placeholder];
                        CreateLinkedPlaceholder(doc, placeholder, sourceCell, workbook.FullName, wordApp);
                    }
                    else
                    {
                        // 如果没有对应数据，高亮显示
                        HighlightMissingPlaceholder(doc, placeholder);
                    }
                }

                // 保存并关闭
                doc.SaveAs2(outputPath, fileFormat);
                doc.Close();
                wordApp.Quit();

                MessageBox.Show(string.Format("链接报告已成功生成！\n{0}", outputPath));
            }
            catch (Exception ex)
            {
                MessageBox.Show("生成链接报告时出错：" + ex.Message);
            }
        }

        /// <summary>
        /// 创建带链接的占位符替换
        /// </summary>
        private void CreateLinkedPlaceholder(Word.Document doc, string placeholder, Excel.Range sourceCell, string workbookPath, Word.Application wordApp)
        {
            try
            {
                // 查找占位符在文档中的所有位置
                Word.Range searchRange = doc.Content;
                Word.Find findObject = searchRange.Find;
                findObject.ClearFormatting();
                findObject.Text = placeholder;
                findObject.Forward = true;
                findObject.Wrap = Word.WdFindWrap.wdFindContinue;
                // 设置精确匹配，避免匹配到部分文本
                findObject.MatchWholeWord = false; // 保持false，因为占位符包含特殊字符
                findObject.MatchCase = false;

                while (findObject.Execute())
                {
                    // 获取找到的范围
                    Word.Range foundRange = doc.Range(findObject.Parent.Start, findObject.Parent.End);

                    // 验证找到的文本是否完全匹配占位符（防止部分匹配）
                    string foundText = foundRange.Text;
                    if (foundText != placeholder)
                    {
                        // 如果不是完全匹配，继续搜索下一个
                        if (foundRange.End < doc.Content.End)
                        {
                            searchRange = doc.Range(foundRange.End, doc.Content.End);
                            findObject = searchRange.Find;
                            findObject.ClearFormatting();
                            findObject.Text = placeholder;
                            findObject.Forward = true;
                            findObject.Wrap = Word.WdFindWrap.wdFindStop;
                            findObject.MatchWholeWord = false;
                            findObject.MatchCase = false;
                            continue;
                        }
                        else
                        {
                            break;
                        }
                    }

                    int originalStart = foundRange.Start;
                    int originalEnd = foundRange.End;

                    // 保存原始格式
                    string originalFontName = foundRange.Font.Name;
                    float originalFontSize = foundRange.Font.Size;
                    int originalBold = foundRange.Font.Bold;
                    int originalItalic = foundRange.Font.Italic;
                    Word.WdColor originalColor = (Word.WdColor)foundRange.Font.Color;
                    Word.WdUnderline originalUnderline = foundRange.Font.Underline;

                    // 清空占位符文本
                    foundRange.Text = "";

                    // 选中源单元格并复制
                    sourceCell.Copy();

                    // 记录原始位置信息（关键：用于后续精确定位）
                    int targetStart = foundRange.Start;
                    int targetEnd = foundRange.End;
                    string originalPlaceholderText = foundRange.Text; // 保存原始占位符文本

                    // 选中目标位置
                    foundRange.Select();

                    // 记录粘贴前的Selection位置
                    Word.Range targetRange = wordApp.Selection.Range.Duplicate;

                    try
                    {
                        // 准备批注信息（在粘贴前准备，避免后续操作影响）
                        string cellValue = FormatCellValue(sourceCell.Value2, sourceCell);
                        string cellAddress = sourceCell.Address;
                        string sheetName = sourceCell.Worksheet.Name;
                        string commentText = string.Format("链接字段：{0}\n来源：{1}!{2}\n当前值：{3}",
                            placeholder.Replace("【", "").Replace("】", ""),
                            sheetName,
                            cellAddress,
                            cellValue);

                        // 创建链接
                        object link = true;
                        object dataType = Word.WdPasteDataType.wdPasteText;
                        wordApp.Selection.PasteSpecial(Link: ref link, DataType: ref dataType);

                        // 等待链接创建完成
                        System.Threading.Thread.Sleep(200);

                        // 关键修复：立即获取粘贴后的Selection范围，不进行任何其他操作
                        Word.Range pastedRange = wordApp.Selection.Range.Duplicate;

                        // 立即添加批注，避免任何可能导致范围偏移的操作
                        try
                        {
                            Word.Comment comment = pastedRange.Comments.Add(pastedRange, commentText);

                            System.Diagnostics.Debug.WriteLine(string.Format("已创建链接和批注：{0} -> {1}!{2} (位置: {3}-{4}, 内容: '{5}')",
                                placeholder, sheetName, cellAddress, pastedRange.Start, pastedRange.End, pastedRange.Text?.Trim()));
                        }
                        catch (Exception commentEx)
                        {
                            System.Diagnostics.Debug.WriteLine(string.Format("添加批注失败：{0} - {1}", placeholder, commentEx.Message));
                        }

                        // 恢复格式（在添加批注后进行，避免影响批注位置）
                        try
                        {
                            pastedRange.Font.Name = originalFontName;
                            pastedRange.Font.Size = originalFontSize;
                            pastedRange.Font.Bold = originalBold;
                            pastedRange.Font.Italic = originalItalic;
                            pastedRange.Font.Color = originalColor;
                            pastedRange.Font.Underline = originalUnderline;
                        }
                        catch (Exception formatEx)
                        {
                            System.Diagnostics.Debug.WriteLine(string.Format("恢复格式失败：{0}", formatEx.Message));
                        }

                        // 更新搜索起始位置
                        foundRange = pastedRange;
                    }
                    catch (Exception linkEx)
                    {
                        System.Diagnostics.Debug.WriteLine(string.Format("创建链接失败：{0} - {1}", placeholder, linkEx.Message));

                        // 如果链接创建失败，至少插入文本值
                        string cellValue = FormatCellValue(sourceCell.Value2, sourceCell);
                        targetRange.Text = cellValue;

                        // 恢复格式
                        targetRange.Font.Name = originalFontName;
                        targetRange.Font.Size = originalFontSize;
                        targetRange.Font.Bold = originalBold;
                        targetRange.Font.Italic = originalItalic;
                        targetRange.Font.Color = originalColor;
                        targetRange.Font.Underline = originalUnderline;

                        // 在失败情况下也添加批注说明
                        try
                        {
                            string cellValue2 = FormatCellValue(sourceCell.Value2, sourceCell);
                            string cellAddress = sourceCell.Address;
                            string sheetName = sourceCell.Worksheet.Name;
                            string commentText = string.Format("数据字段：{0}\n来源：{1}!{2}\n当前值：{3}\n注意：链接创建失败，显示为静态值",
                                placeholder.Replace("【", "").Replace("】", ""),
                                sheetName,
                                cellAddress,
                                cellValue2);
                            targetRange.Comments.Add(targetRange, commentText);
                        }
                        catch (Exception commentEx)
                        {
                            System.Diagnostics.Debug.WriteLine(string.Format("添加失败批注时出错：{0}", commentEx.Message));
                        }

                        foundRange = targetRange;
                    }

                    // 重新设置搜索范围，从当前位置之后继续搜索
                    if (foundRange.End < doc.Content.End)
                    {
                        searchRange = doc.Range(foundRange.End, doc.Content.End);
                        findObject = searchRange.Find;
                        findObject.ClearFormatting();
                        findObject.Text = placeholder;
                        findObject.Forward = true;
                        findObject.Wrap = Word.WdFindWrap.wdFindStop;
                        findObject.MatchWholeWord = false;
                        findObject.MatchCase = false;
                    }
                    else
                    {
                        break; // 已到文档末尾
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine(string.Format("处理占位符失败：{0} - {1}", placeholder, ex.Message));
            }
        }

        /// <summary>
        /// 高亮显示缺失的占位符
        /// </summary>
        private void HighlightMissingPlaceholder(Word.Document doc, string placeholder)
        {
            try
            {
                Word.Range searchRange = doc.Content;
                Word.Find findObject = searchRange.Find;
                findObject.ClearFormatting();
                findObject.Text = placeholder;
                findObject.Forward = true;
                findObject.Wrap = Word.WdFindWrap.wdFindContinue;
                // 设置精确匹配
                findObject.MatchWholeWord = false;
                findObject.MatchCase = false;

                while (findObject.Execute())
                {
                    // 获取找到的确切范围
                    Word.Range foundRange = doc.Range(findObject.Parent.Start, findObject.Parent.End);

                    // 验证找到的文本是否完全匹配占位符
                    string foundText = foundRange.Text;
                    if (foundText != placeholder)
                    {
                        // 如果不是完全匹配，继续搜索下一个
                        if (foundRange.End < doc.Content.End)
                        {
                            searchRange = doc.Range(foundRange.End, doc.Content.End);
                            findObject = searchRange.Find;
                            findObject.ClearFormatting();
                            findObject.Text = placeholder;
                            findObject.Forward = true;
                            findObject.Wrap = Word.WdFindWrap.wdFindStop;
                            findObject.MatchWholeWord = false;
                            findObject.MatchCase = false;
                            continue;
                        }
                        else
                        {
                            break;
                        }
                    }

                    // 应用高亮格式
                    foundRange.Font.Bold = 1;
                    foundRange.HighlightColorIndex = Word.WdColorIndex.wdYellow;

                    // 在正确的位置添加批注说明缺失数据
                    try
                    {
                        string commentText = string.Format("缺失数据字段：{0}\n请检查Excel索引行是否包含此字段",
                            placeholder.Replace("【", "").Replace("】", ""));
                        foundRange.Comments.Add(foundRange, commentText);

                        System.Diagnostics.Debug.WriteLine(string.Format("已为缺失字段添加批注：{0}", placeholder));
                    }
                    catch (Exception commentEx)
                    {
                        System.Diagnostics.Debug.WriteLine(string.Format("添加缺失字段批注失败：{0} - {1}", placeholder, commentEx.Message));
                    }

                    // 继续搜索下一个，确保搜索范围正确
                    if (foundRange.End < doc.Content.End)
                    {
                        searchRange = doc.Range(foundRange.End, doc.Content.End);
                        findObject = searchRange.Find;
                        findObject.ClearFormatting();
                        findObject.Text = placeholder;
                        findObject.Forward = true;
                        findObject.Wrap = Word.WdFindWrap.wdFindStop;
                        findObject.MatchWholeWord = false;
                        findObject.MatchCase = false;
                    }
                    else
                    {
                        break; // 已到文档末尾
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine(string.Format("处理缺失占位符失败：{0} - {1}", placeholder, ex.Message));
            }
        }

        /// <summary>
        /// 费用归并按钮点击事件
        /// </summary>
        private void buttonExpenseMerge_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                Excel.Application excelApp = Globals.ThisAddIn.Application;
                Excel.Workbook workbook = excelApp.ActiveWorkbook;
                Excel.Worksheet worksheet = workbook.ActiveSheet;

                if (workbook == null || worksheet == null)
                {
                    MessageBox.Show("请先打开一个Excel工作簿！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 显示费用归并配置窗体
                ExpenseMergeConfigForm configForm = new ExpenseMergeConfigForm();
                if (configForm.ShowDialog() != DialogResult.OK)
                {
                    return;
                }

                // 执行费用归并操作
                PerformExpenseMerge(configForm.Config);

                MessageBox.Show("费用归并完成！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"费用归并失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 西安汇总按钮点击事件
        /// </summary>
        private void buttonXianSummary_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 检查试用期
                if (!TrialHelper.IsTrialValid())
                {
                    MessageBox.Show("试用期已过，请联系管理员获取正式版本。", "试用期限制", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 显示西安汇总配置窗体
                XianSummaryConfigForm configForm = new XianSummaryConfigForm();
                if (configForm.ShowDialog() == DialogResult.OK)
                {
                    // 执行汇总处理
                    ProcessXianSummary(configForm.GetConfiguration());
                    MessageBox.Show("西安汇总完成！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"西安汇总功能执行失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 执行西安汇总处理
        /// </summary>
        /// <param name="config">汇总配置</param>
        private void ProcessXianSummary(XianSummaryConfig config)
        {
            Excel.Application excelApp = Globals.ThisAddIn.Application;
            Excel.Workbook templateWorkbook = null;

            try
            {
                // 1. 打开模板文件
                templateWorkbook = excelApp.Workbooks.Open(config.TemplateFilePath);
                Excel.Worksheet templateSheet = templateWorkbook.ActiveSheet;

                // 2. 处理每个单体表文件
                int processedCount = 0;
                foreach (string sourceFilePath in config.SourceFilePaths)
                {
                    try
                    {
                        ProcessSingleSourceFile(templateWorkbook, sourceFilePath, config, processedCount);
                        processedCount++;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"处理文件 {System.IO.Path.GetFileName(sourceFilePath)} 失败：{ex.Message}",
                                      "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }

                // 3. 保存结果文件
                string resultFileName = $"西安汇总_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                string resultPath = System.IO.Path.Combine(System.IO.Path.GetDirectoryName(config.TemplateFilePath), resultFileName);
                templateWorkbook.SaveAs(resultPath);

                MessageBox.Show($"汇总完成！已处理 {processedCount} 个文件。\n结果保存至：{resultPath}",
                              "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            finally
            {
                if (templateWorkbook != null)
                {
                    templateWorkbook.Close(false);
                }
            }
        }

        /// <summary>
        /// 固定汇总按钮点击事件
        /// </summary>
        private void buttonFixedSummary_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 检查试用期
                if (!TrialHelper.IsTrialValid())
                {
                    MessageBox.Show("试用期已过，请联系管理员获取正式版本。", "试用期限制", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 显示固定汇总配置窗体
                FixedSummaryConfigForm configForm = new FixedSummaryConfigForm();
                if (configForm.ShowDialog() == DialogResult.OK)
                {
                    // 执行固定汇总处理
                    ProcessFixedSummary(configForm.GetConfiguration());
                    MessageBox.Show("固定汇总完成！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"固定汇总功能执行失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 执行固定汇总处理
        /// </summary>
        /// <param name="config">固定汇总配置</param>
        private void ProcessFixedSummary(FixedSummaryConfig config)
        {
            Excel.Application excelApp = Globals.ThisAddIn.Application;
            Excel.Workbook templateWorkbook = null;

            try
            {
                // 1. 打开模板文件
                templateWorkbook = excelApp.Workbooks.Open(config.TemplateFilePath);

                // 2. 获取固定汇总规则
                List<FixedSummaryRule> rules = FixedSummaryRule.GetFixedRules();

                // 3. 为汇总模式（附表3）初始化累加器
                Dictionary<string, Dictionary<int, Dictionary<int, double>>> summaryAccumulator =
                    new Dictionary<string, Dictionary<int, Dictionary<int, double>>>();

                // 4. 处理每个单体表文件
                int processedCount = 0;
                foreach (string sourceFilePath in config.SourceFilePaths)
                {
                    try
                    {
                        ProcessSingleSourceFileForFixed(templateWorkbook, sourceFilePath, rules,
                                                       summaryAccumulator, processedCount);
                        processedCount++;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"处理文件 {System.IO.Path.GetFileName(sourceFilePath)} 失败：{ex.Message}",
                                      "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }

                // 5. 处理汇总数据（附表3的数值相加）
                ProcessSummaryData(templateWorkbook, summaryAccumulator);

                // 6. 保存结果文件
                string resultFileName = $"固定汇总_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                string resultPath = System.IO.Path.Combine(System.IO.Path.GetDirectoryName(config.TemplateFilePath), resultFileName);
                templateWorkbook.SaveAs(resultPath);

                MessageBox.Show($"固定汇总完成！已处理 {processedCount} 个文件。\n结果保存至：{resultPath}",
                              "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            finally
            {
                if (templateWorkbook != null)
                {
                    templateWorkbook.Close(false);
                }
            }
        }

        /// <summary>
        /// 处理单个源文件（固定汇总）
        /// </summary>
        /// <param name="templateWorkbook">模板工作簿</param>
        /// <param name="sourceFilePath">源文件路径</param>
        /// <param name="rules">固定汇总规则</param>
        /// <param name="summaryAccumulator">汇总累加器</param>
        /// <param name="fileIndex">文件索引</param>
        private void ProcessSingleSourceFileForFixed(Excel.Workbook templateWorkbook, string sourceFilePath,
                                                    List<FixedSummaryRule> rules,
                                                    Dictionary<string, Dictionary<int, Dictionary<int, double>>> summaryAccumulator,
                                                    int fileIndex)
        {
            Excel.Application excelApp = Globals.ThisAddIn.Application;
            Excel.Workbook sourceWorkbook = null;

            try
            {
                // 打开源文件
                sourceWorkbook = excelApp.Workbooks.Open(sourceFilePath, ReadOnly: true);
                Excel.Worksheet sourceSheet = sourceWorkbook.ActiveSheet;

                // 处理每个规则
                foreach (FixedSummaryRule rule in rules)
                {
                    try
                    {
                        // 查找目标工作表
                        Excel.Worksheet targetSheet = FindWorksheetByName(templateWorkbook, rule.TargetSheetName);
                        if (targetSheet == null)
                        {
                            System.Diagnostics.Debug.WriteLine($"未找到目标工作表：{rule.TargetSheetName}");
                            continue;
                        }

                        // 提取源数据
                        var sourceData = ExtractSourceData(sourceSheet, rule);
                        if (sourceData.Count == 0)
                        {
                            System.Diagnostics.Debug.WriteLine($"未提取到数据：{rule.Description}");
                            continue;
                        }

                        // 根据规则类型处理数据
                        if (rule.IsSummaryMode)
                        {
                            // 汇总模式：累加到累加器中
                            AccumulateSummaryData(summaryAccumulator, rule.TargetSheetName, sourceData, rule);
                        }
                        else
                        {
                            // 普通模式：直接插入到目标位置
                            InsertDataToTarget(targetSheet, sourceData, rule, fileIndex);
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"处理规则失败：{rule.Description}，错误：{ex.Message}");
                    }
                }
            }
            finally
            {
                if (sourceWorkbook != null)
                {
                    sourceWorkbook.Close(false);
                }
            }
        }

        /// <summary>
        /// 查找工作表
        /// </summary>
        /// <param name="workbook">工作簿</param>
        /// <param name="sheetName">工作表名称</param>
        /// <returns>工作表对象</returns>
        private Excel.Worksheet FindWorksheetByName(Excel.Workbook workbook, string sheetName)
        {
            foreach (Excel.Worksheet sheet in workbook.Sheets)
            {
                if (sheet.Name.Equals(sheetName, StringComparison.OrdinalIgnoreCase))
                {
                    return sheet;
                }
            }
            return null;
        }

        /// <summary>
        /// 提取源数据
        /// </summary>
        /// <param name="sourceSheet">源工作表</param>
        /// <param name="rule">提取规则</param>
        /// <returns>提取的数据</returns>
        private List<List<string>> ExtractSourceData(Excel.Worksheet sourceSheet, FixedSummaryRule rule)
        {
            List<List<string>> data = new List<List<string>>();

            try
            {
                int startCol = ColumnLetterToNumber(rule.SourceStartColumn);
                int endCol = ColumnLetterToNumber(rule.SourceEndColumn);

                for (int row = rule.SourceStartRow; row <= rule.SourceEndRow; row++)
                {
                    List<string> rowData = new List<string>();
                    for (int col = startCol; col <= endCol; col++)
                    {
                        Excel.Range cell = sourceSheet.Cells[row, col];
                        string cellValue = GetCellDisplayValue(cell);
                        rowData.Add(cellValue);
                    }
                    data.Add(rowData);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"提取源数据失败：{ex.Message}");
            }

            return data;
        }

        /// <summary>
        /// 将列字母转换为数字
        /// </summary>
        /// <param name="columnLetter">列字母</param>
        /// <returns>列数字</returns>
        private int ColumnLetterToNumber(string columnLetter)
        {
            int columnNumber = 0;
            for (int i = 0; i < columnLetter.Length; i++)
            {
                columnNumber = columnNumber * 26 + (columnLetter[i] - 'A' + 1);
            }
            return columnNumber;
        }

        /// <summary>
        /// 累加汇总数据
        /// </summary>
        /// <param name="accumulator">累加器</param>
        /// <param name="sheetName">工作表名称</param>
        /// <param name="sourceData">源数据</param>
        /// <param name="rule">规则</param>
        private void AccumulateSummaryData(Dictionary<string, Dictionary<int, Dictionary<int, double>>> accumulator,
                                         string sheetName, List<List<string>> sourceData, FixedSummaryRule rule)
        {
            if (!accumulator.ContainsKey(sheetName))
            {
                accumulator[sheetName] = new Dictionary<int, Dictionary<int, double>>();
            }

            int targetStartCol = ColumnLetterToNumber(rule.TargetStartColumn);

            for (int rowIndex = 0; rowIndex < sourceData.Count; rowIndex++)
            {
                int targetRow = rule.TargetStartRow + rowIndex;

                if (!accumulator[sheetName].ContainsKey(targetRow))
                {
                    accumulator[sheetName][targetRow] = new Dictionary<int, double>();
                }

                for (int colIndex = 0; colIndex < sourceData[rowIndex].Count; colIndex++)
                {
                    int targetCol = targetStartCol + colIndex;
                    string cellValue = sourceData[rowIndex][colIndex];

                    // 尝试转换为数值
                    if (double.TryParse(cellValue, out double numericValue))
                    {
                        if (!accumulator[sheetName][targetRow].ContainsKey(targetCol))
                        {
                            accumulator[sheetName][targetRow][targetCol] = 0;
                        }
                        accumulator[sheetName][targetRow][targetCol] += numericValue;
                    }
                }
            }
        }

        /// <summary>
        /// 插入数据到目标位置
        /// </summary>
        /// <param name="targetSheet">目标工作表</param>
        /// <param name="sourceData">源数据</param>
        /// <param name="rule">规则</param>
        /// <param name="fileIndex">文件索引</param>
        private void InsertDataToTarget(Excel.Worksheet targetSheet, List<List<string>> sourceData,
                                      FixedSummaryRule rule, int fileIndex)
        {
            try
            {
                int targetStartCol = ColumnLetterToNumber(rule.TargetStartColumn);
                int currentTargetRow = rule.TargetStartRow + fileIndex;

                for (int rowIndex = 0; rowIndex < sourceData.Count; rowIndex++)
                {
                    for (int colIndex = 0; colIndex < sourceData[rowIndex].Count; colIndex++)
                    {
                        int targetRow = currentTargetRow + rowIndex;
                        int targetCol = targetStartCol + colIndex;

                        Excel.Range targetCell = targetSheet.Cells[targetRow, targetCol];
                        string cellValue = sourceData[rowIndex][colIndex];

                        // 尝试保持数值格式并设置千分号+两位小数
                        if (double.TryParse(cellValue, out double numericValue))
                        {
                            targetCell.Value2 = numericValue;
                            // 设置数字格式为千分号+两位小数
                            targetCell.NumberFormat = "#,##0.00";
                        }
                        else
                        {
                            targetCell.Value2 = cellValue;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"插入数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 处理汇总数据
        /// </summary>
        /// <param name="templateWorkbook">模板工作簿</param>
        /// <param name="summaryAccumulator">汇总累加器</param>
        private void ProcessSummaryData(Excel.Workbook templateWorkbook,
                                      Dictionary<string, Dictionary<int, Dictionary<int, double>>> summaryAccumulator)
        {
            foreach (var sheetData in summaryAccumulator)
            {
                string sheetName = sheetData.Key;
                Excel.Worksheet targetSheet = FindWorksheetByName(templateWorkbook, sheetName);

                if (targetSheet == null)
                {
                    System.Diagnostics.Debug.WriteLine($"未找到目标工作表：{sheetName}");
                    continue;
                }

                foreach (var rowData in sheetData.Value)
                {
                    int row = rowData.Key;
                    foreach (var colData in rowData.Value)
                    {
                        int col = colData.Key;
                        double value = colData.Value;

                        Excel.Range targetCell = targetSheet.Cells[row, col];
                        targetCell.Value2 = value;
                        // 设置数字格式为千分号+两位小数
                        targetCell.NumberFormat = "#,##0.00";
                    }
                }
            }
        }

        /// <summary>
        /// 处理单个源文件
        /// </summary>
        /// <param name="templateWorkbook">模板工作簿</param>
        /// <param name="sourceFilePath">源文件路径</param>
        /// <param name="config">配置信息</param>
        /// <param name="fileIndex">文件索引</param>
        private void ProcessSingleSourceFile(Excel.Workbook templateWorkbook, string sourceFilePath,
                                           XianSummaryConfig config, int fileIndex)
        {
            Excel.Application excelApp = Globals.ThisAddIn.Application;
            Excel.Workbook sourceWorkbook = null;

            try
            {
                // 打开源文件，兼容WPS和Excel
                try
                {
                    // 首先尝试简单的只读打开方式（兼容WPS）
                    sourceWorkbook = excelApp.Workbooks.Open(sourceFilePath, ReadOnly: true);
                }
                catch (Exception ex1)
                {
                    System.Diagnostics.Debug.WriteLine($"简单打开失败，尝试完整参数打开: {ex1.Message}");
                    try
                    {
                        // 如果简单方式失败，尝试完整参数（适用于Excel）
                        sourceWorkbook = excelApp.Workbooks.Open(
                            Filename: sourceFilePath,
                            UpdateLinks: 0,
                            ReadOnly: true,
                            Format: 5,
                            Password: Type.Missing,
                            WriteResPassword: Type.Missing,
                            IgnoreReadOnlyRecommended: true,
                            Origin: Type.Missing,
                            Delimiter: Type.Missing,
                            Editable: false,
                            Notify: false,
                            Converter: Type.Missing,
                            AddToMru: false,
                            Local: Type.Missing,
                            CorruptLoad: Type.Missing
                        );
                    }
                    catch (Exception ex2)
                    {
                        System.Diagnostics.Debug.WriteLine($"完整参数打开也失败，尝试最基本方式: {ex2.Message}");
                        // 最后尝试最基本的打开方式
                        sourceWorkbook = excelApp.Workbooks.Open(sourceFilePath);
                    }
                }

                // 对每个索引行配置进行处理
                foreach (var indexConfig in config.IndexConfigs)
                {
                    System.Diagnostics.Debug.WriteLine($"=== 开始处理索引配置 ===");
                    System.Diagnostics.Debug.WriteLine($"目标附表: {indexConfig.TargetSheetName}");
                    System.Diagnostics.Debug.WriteLine($"源附表: {indexConfig.SourceSheetName}");
                    System.Diagnostics.Debug.WriteLine($"字段名称: {string.Join(", ", indexConfig.FieldNames)}");
                    System.Diagnostics.Debug.WriteLine($"抓取范围: 行{indexConfig.StartRow}-{indexConfig.EndRow}, 列{indexConfig.StartColumn}-{indexConfig.EndColumn}");
                    System.Diagnostics.Debug.WriteLine($"插入位置: 行{indexConfig.InsertRow}, 列{indexConfig.InsertColumn}");

                    // 获取模板中的目标工作表
                    Excel.Worksheet templateSheet = GetWorksheetByName(templateWorkbook, indexConfig.TargetSheetName);
                    if (templateSheet == null)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ 模板中未找到工作表: {indexConfig.TargetSheetName}");
                        continue;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ 找到目标附表: {templateSheet.Name}");
                    }

                    // 在源文件中查找指定的源工作表
                    Excel.Worksheet sourceSheet = GetWorksheetByName(sourceWorkbook, indexConfig.SourceSheetName);
                    if (sourceSheet == null)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ 源文件中未找到工作表: {indexConfig.SourceSheetName}");
                        continue;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ 找到源附表: {sourceSheet.Name}");
                    }

                    // 根据索引行和字段名称查找数据
                    var fieldDataMap = FindDataByIndexRowAndColumn(sourceSheet, indexConfig);

                    if (fieldDataMap.Count == 0)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ 未找到任何数据");
                    }
                    else
                    {
                        int totalRows = fieldDataMap.Values.FirstOrDefault()?.Count ?? 0;
                        System.Diagnostics.Debug.WriteLine($"✅ 找到数据，字段数: {fieldDataMap.Count}, 数据行数: {totalRows}");
                    }

                    // 将数据插入到模板的指定位置
                    InsertMultiRowDataToTemplate(templateSheet, fieldDataMap, indexConfig, fileIndex);

                    System.Diagnostics.Debug.WriteLine($"=== 索引配置处理完成 ===");
                }
            }
            finally
            {
                if (sourceWorkbook != null)
                {
                    sourceWorkbook.Close(false);
                }
            }
        }

        /// <summary>
        /// 根据名称获取工作表
        /// </summary>
        /// <param name="workbook">工作簿</param>
        /// <param name="sheetName">工作表名称</param>
        /// <returns>工作表对象，如果未找到返回null</returns>
        private Excel.Worksheet GetWorksheetByName(Excel.Workbook workbook, string sheetName)
        {
            if (string.IsNullOrEmpty(sheetName)) return null;

            try
            {
                foreach (Excel.Worksheet sheet in workbook.Worksheets)
                {
                    if (sheet.Name.Equals(sheetName, StringComparison.OrdinalIgnoreCase))
                    {
                        return sheet;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"查找工作表失败: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// 根据配置的字段名称在源文件中查找数据
        /// 逻辑：
        /// 1. 字段名称来自模板文件的索引范围选择（indexConfig.FieldNames）
        /// 2. 在源文件的抓取范围内查找这些字段名称对应的列
        /// 3. 从数据行中提取对应列的数据
        /// </summary>
        /// <param name="sourceSheet">源工作表</param>
        /// <param name="indexConfig">索引配置</param>
        /// <returns>字段名称和数据列表的字典</returns>
        private Dictionary<string, List<string>> FindDataByIndexRowAndColumn(Excel.Worksheet sourceSheet, XianSummaryIndexConfig indexConfig)
        {
            var fieldDataMap = new Dictionary<string, List<string>>();

            try
            {
                System.Diagnostics.Debug.WriteLine($"开始处理源文件: {sourceSheet.Name}");
                System.Diagnostics.Debug.WriteLine($"抓取范围: 行{indexConfig.StartRow}-{indexConfig.EndRow}, 列{indexConfig.StartColumn}-{indexConfig.EndColumn}");
                System.Diagnostics.Debug.WriteLine($"要查找的字段: {string.Join(", ", indexConfig.FieldNames)}");

                // 在源文件的抓取范围内查找字段名称对应的列
                var fieldColumnMap = new Dictionary<string, int>();

                // 在抓取范围内查找字段名称（通常在第一行或前几行）
                for (int searchRow = indexConfig.StartRow; searchRow <= Math.Min(indexConfig.StartRow + 5, indexConfig.EndRow); searchRow++)
                {
                    for (int col = indexConfig.StartColumn; col <= indexConfig.EndColumn; col++)
                    {
                        Excel.Range cell = sourceSheet.Cells[searchRow, col];
                        object cellValue = cell.Value2;

                        if (cellValue != null)
                        {
                            string cellText = cellValue.ToString().Trim();
                            if (!string.IsNullOrEmpty(cellText))
                            {
                                // 检查是否是我们要找的字段之一
                                foreach (string targetField in indexConfig.FieldNames)
                                {
                                    // 跳过空字段名称
                                    if (string.IsNullOrEmpty(targetField))
                                        continue;

                                    if (cellText.Equals(targetField, StringComparison.OrdinalIgnoreCase) ||
                                        cellText.Contains(targetField) || targetField.Contains(cellText))
                                    {
                                        if (!fieldColumnMap.ContainsKey(targetField))
                                        {
                                            fieldColumnMap[targetField] = col;
                                            System.Diagnostics.Debug.WriteLine($"在源文件中找到字段 '{targetField}' 对应 '{cellText}' 在列 {col}，行 {searchRow}");
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"字段映射结果: {string.Join(", ", fieldColumnMap.Select(kv => $"{kv.Key}->列{kv.Value}"))}");

                // 初始化字段数据列表
                foreach (string fieldName in indexConfig.FieldNames)
                {
                    fieldDataMap[fieldName] = new List<string>();
                }

                // 确定数据开始行（跳过所有包含字段名称的行）
                // 在多行索引的情况下，需要找到第一个不包含字段名称的数据行
                int dataStartRow = FindDataStartRow(sourceSheet, indexConfig, fieldColumnMap);

                // 从数据开始行逐行读取数据，遇到空行就结束
                for (int dataRow = dataStartRow; dataRow <= indexConfig.EndRow; dataRow++)
                {
                    // 检查当前行是否为空行
                    bool isEmptyRow = true;
                    for (int checkCol = indexConfig.StartColumn; checkCol <= indexConfig.EndColumn; checkCol++)
                    {
                        Excel.Range checkCell = sourceSheet.Cells[dataRow, checkCol];
                        string checkValue = GetCellDisplayValue(checkCell);
                        if (!string.IsNullOrEmpty(checkValue))
                        {
                            isEmptyRow = false;
                            break;
                        }
                    }

                    if (isEmptyRow)
                    {
                        System.Diagnostics.Debug.WriteLine($"遇到空行 {dataRow}，结束数据抓取");
                        break;
                    }

                    // 按照字段顺序提取数据
                    foreach (string fieldName in indexConfig.FieldNames)
                    {
                        string dataValue = "";

                        // 如果字段名称为空，直接添加空值
                        if (string.IsNullOrEmpty(fieldName))
                        {
                            System.Diagnostics.Debug.WriteLine($"字段名称为空，添加空值");
                        }
                        else if (fieldColumnMap.ContainsKey(fieldName))
                        {
                            int col = fieldColumnMap[fieldName];
                            Excel.Range dataCell = sourceSheet.Cells[dataRow, col];
                            dataValue = GetCellDisplayValue(dataCell) ?? "";
                            System.Diagnostics.Debug.WriteLine($"字段 '{fieldName}' 第{fieldDataMap[fieldName].Count + 1}行数据: '{dataValue}' 位置({dataRow},{col})");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"字段 '{fieldName}' 在源文件中未找到对应列，添加空值");
                        }

                        fieldDataMap[fieldName].Add(dataValue);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"查找数据失败: {ex.Message}");
                MessageBox.Show($"查找数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }

            int totalRows = fieldDataMap.Values.FirstOrDefault()?.Count ?? 0;
            System.Diagnostics.Debug.WriteLine($"最终找到 {fieldDataMap.Count} 个字段，共 {totalRows} 行数据");
            return fieldDataMap;
        }

        /// <summary>
        /// 找到数据开始行（跳过所有包含字段名称的行）
        /// 逻辑：从抓取范围开始，找到第一个不包含任何字段名称的行作为数据开始行
        /// </summary>
        /// <param name="sourceSheet">源工作表</param>
        /// <param name="indexConfig">索引配置</param>
        /// <param name="fieldColumnMap">字段列映射</param>
        /// <returns>数据开始行号</returns>
        private int FindDataStartRow(Excel.Worksheet sourceSheet, XianSummaryIndexConfig indexConfig, Dictionary<string, int> fieldColumnMap)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始查找数据开始行，抓取范围：行{indexConfig.StartRow}-{indexConfig.EndRow}");

                // 从抓取范围的开始行逐行检查
                for (int row = indexConfig.StartRow; row <= indexConfig.EndRow; row++)
                {
                    bool isFieldNameRow = false;

                    // 检查当前行是否包含任何字段名称
                    for (int col = indexConfig.StartColumn; col <= indexConfig.EndColumn; col++)
                    {
                        Excel.Range cell = sourceSheet.Cells[row, col];
                        string cellText = GetCellDisplayValue(cell)?.Trim();

                        if (!string.IsNullOrEmpty(cellText))
                        {
                            // 检查是否是字段名称
                            foreach (string fieldName in indexConfig.FieldNames)
                            {
                                if (!string.IsNullOrEmpty(fieldName) &&
                                    (cellText.Equals(fieldName, StringComparison.OrdinalIgnoreCase) ||
                                     cellText.Contains(fieldName) || fieldName.Contains(cellText)))
                                {
                                    isFieldNameRow = true;
                                    System.Diagnostics.Debug.WriteLine($"行{row}包含字段名称'{fieldName}'，对应单元格内容'{cellText}'");
                                    break;
                                }
                            }

                            if (isFieldNameRow) break;
                        }
                    }

                    // 如果当前行不包含任何字段名称，则认为是数据开始行
                    if (!isFieldNameRow)
                    {
                        System.Diagnostics.Debug.WriteLine($"找到数据开始行：{row}");
                        return row;
                    }
                }

                // 如果没有找到纯数据行，默认从抓取范围的最后一行开始
                int defaultStartRow = indexConfig.EndRow;
                System.Diagnostics.Debug.WriteLine($"未找到明确的数据开始行，使用默认值：{defaultStartRow}");
                return defaultStartRow;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"查找数据开始行时出错：{ex.Message}");
                // 出错时返回默认值
                return indexConfig.StartRow + 1;
            }
        }

        /// <summary>
        /// 将多行数据插入到模板的指定位置
        /// 确保数据与索引字段在同一列下对应
        /// </summary>
        /// <param name="templateSheet">模板工作表</param>
        /// <param name="fieldDataMap">字段数据映射（每个字段对应多行数据）</param>
        /// <param name="indexConfig">索引配置</param>
        /// <param name="fileIndex">文件索引</param>
        private void InsertMultiRowDataToTemplate(Excel.Worksheet templateSheet,
                                                 Dictionary<string, List<string>> fieldDataMap,
                                                 XianSummaryIndexConfig indexConfig, int fileIndex)
        {
            try
            {
                if (fieldDataMap.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("没有找到任何数据，跳过插入");
                    return;
                }

                // 获取数据行数（所有字段的数据行数应该相同）
                int dataRowCount = fieldDataMap.Values.FirstOrDefault()?.Count ?? 0;
                if (dataRowCount == 0)
                {
                    System.Diagnostics.Debug.WriteLine("数据行数为0，跳过插入");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"开始插入数据到模板: 附表={templateSheet.Name}");
                System.Diagnostics.Debug.WriteLine($"要插入的字段数量: {indexConfig.FieldNames.Count}, 数据行数: {dataRowCount}");

                // 首先插入索引行（字段名称）到模板
                if (fileIndex == 0) // 只在处理第一个文件时插入索引行
                {
                    int indexRowInTemplate = indexConfig.InsertRow - 1; // 索引行在数据行的上一行
                    int currentColumn = indexConfig.InsertColumn;

                    System.Diagnostics.Debug.WriteLine($"插入索引行到行 {indexRowInTemplate}");

                    foreach (string fieldName in indexConfig.FieldNames)
                    {
                        Excel.Range indexCell = templateSheet.Cells[indexRowInTemplate, currentColumn];
                        indexCell.Value = fieldName;

                        // 设置索引行格式（加粗）
                        try
                        {
                            indexCell.Font.Bold = true;
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"设置索引行格式失败: {ex.Message}");
                        }

                        System.Diagnostics.Debug.WriteLine($"插入索引字段: '{fieldName}' 位置=({indexRowInTemplate},{currentColumn})");
                        currentColumn++;
                    }
                }

                // 插入数据行
                for (int dataRowIndex = 0; dataRowIndex < dataRowCount; dataRowIndex++)
                {
                    // 计算实际插入的行号（基础行号 + 文件索引 * 数据行数 + 当前数据行索引）
                    int actualInsertRow = indexConfig.InsertRow + (fileIndex * dataRowCount) + dataRowIndex;
                    int currentColumn = indexConfig.InsertColumn;

                    System.Diagnostics.Debug.WriteLine($"插入第 {dataRowIndex + 1} 行数据到行 {actualInsertRow}");

                    // 按照索引字段的顺序插入数据，确保字段与数据在同一列
                    foreach (string fieldName in indexConfig.FieldNames)
                    {
                        string dataValue = "";

                        if (fieldDataMap.ContainsKey(fieldName) && dataRowIndex < fieldDataMap[fieldName].Count)
                        {
                            dataValue = fieldDataMap[fieldName][dataRowIndex] ?? "";
                        }

                        // 插入到模板的指定位置
                        Excel.Range targetCell = templateSheet.Cells[actualInsertRow, currentColumn];
                        targetCell.Value = dataValue;

                        System.Diagnostics.Debug.WriteLine($"插入数据: 字段='{fieldName}', 值='{dataValue}', 位置=({actualInsertRow},{currentColumn})");

                        // 如果是数值，设置格式
                        if (!string.IsNullOrEmpty(dataValue) && IsNumericString(dataValue))
                        {
                            try
                            {
                                targetCell.NumberFormat = "#,##0.00";
                                System.Diagnostics.Debug.WriteLine($"应用数值格式: {dataValue}");
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"设置数字格式失败: {ex.Message}");
                            }
                        }

                        // 移动到下一列
                        currentColumn++;
                    }
                }

                System.Diagnostics.Debug.WriteLine($"数据插入完成，共插入 {dataRowCount} 行，{indexConfig.FieldNames.Count} 个字段");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"插入数据失败: {ex.Message}");
                MessageBox.Show($"插入数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 执行费用归并操作
        /// </summary>
        private void PerformExpenseMerge(ExpenseMergeConfig config)
        {
            Excel.Application excelApp = Globals.ThisAddIn.Application;
            Excel.Workbook targetWorkbook = excelApp.ActiveWorkbook;
            Excel.Worksheet targetSheet = targetWorkbook.Sheets[config.TargetSheetName];
            Excel.Worksheet indexSheet = targetWorkbook.Sheets[config.IndexSheetName];

            // 使用配置中的输出位置设置
            int fileNameRow = config.OutputFileNameRow;
            int fileNameStartCol = config.OutputFileNameStartCol;
            int dataStartCol = config.OutputDataStartCol;

            // 读取索引字段数据（从索引工作表读取）
            Dictionary<string, string> indexData = ReadIndexData(indexSheet, config.IndexStartRow, config.IndexStartCol, config.IndexEndRow, config.IndexEndCol);

            if (indexData.Count == 0)
            {
                MessageBox.Show($"在指定范围内没有找到索引数据！\n\n" +
                              $"指定范围: 第{config.IndexStartRow}-{config.IndexEndRow}行，第{config.IndexStartCol}-{config.IndexEndCol}列\n" +
                              $"请检查:\n" +
                              $"1. 索引字段范围设置是否正确\n" +
                              $"2. 指定范围内是否有数据\n" +
                              $"3. 工作表是否正确",
                              "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 显示读取到的索引数据信息
            var indexPreview = string.Join(", ", indexData.Values.Take(5));
            if (indexData.Count > 5) indexPreview += $"... (共{indexData.Count}项)";

            var confirmResult = MessageBox.Show(
                $"读取到 {indexData.Count} 个索引项，将在 {config.SourceFiles.Count} 个数据源文件中查找匹配数据。\n\n" +
                $"索引数据示例: {indexPreview}\n\n" +
                $"确认继续？",
                "确认索引数据",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Information
            );

            if (confirmResult != DialogResult.Yes)
                return;

            // 显示进度
            int totalFiles = config.SourceFiles.Count;
            int processedFiles = 0;
            var processResults = new List<ProcessResult>();

            // 处理每个数据源文件
            for (int fileIndex = 0; fileIndex < config.SourceFiles.Count; fileIndex++)
            {
                var sourceFile = config.SourceFiles[fileIndex];
                int currentCol = dataStartCol + fileIndex;

                // 写入文件名到指定位置
                int fileNameCol = fileNameStartCol + fileIndex;
                targetSheet.Cells[fileNameRow, fileNameCol] = Path.GetFileNameWithoutExtension(sourceFile.FilePath);

                // 打开数据源文件
                Excel.Workbook sourceWorkbook = null;
                try
                {
                    sourceWorkbook = excelApp.Workbooks.Open(sourceFile.FilePath, ReadOnly: true);
                    Excel.Worksheet sourceSheet = sourceWorkbook.Sheets[sourceFile.SheetName];

                    // 在数据源中查找匹配的数据并写入目标表
                    int matchedCount = WriteMatchedData(sourceSheet, targetSheet, indexData, currentCol, config.DataColumn);

                    processedFiles++;
                    processResults.Add(new ProcessResult
                    {
                        FileName = Path.GetFileName(sourceFile.FilePath),
                        MatchCount = matchedCount
                    });

                    // 更新状态栏或显示进度
                    excelApp.StatusBar = $"正在处理文件 {processedFiles}/{totalFiles}: {Path.GetFileName(sourceFile.FilePath)} (匹配 {matchedCount} 项)";
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"处理文件 {Path.GetFileName(sourceFile.FilePath)} 时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                finally
                {
                    sourceWorkbook?.Close(false);
                }
            }

            // 清除状态栏
            excelApp.StatusBar = false;

            // 显示详细报告
            StringBuilder report = new StringBuilder();
            report.AppendLine($"费用归并完成！");
            report.AppendLine($"--------------------");
            report.AppendLine($"索引数据: {indexData.Count} 项");
            report.AppendLine($"数据源文件: {config.SourceFiles.Count} 个");
            report.AppendLine($"--------------------");
            report.AppendLine($"处理结果:");

            int totalMatched = 0;
            foreach (var result in processResults)
            {
                report.AppendLine($"- {result.FileName}: 匹配 {result.MatchCount} 项");
                totalMatched += result.MatchCount;
            }

            report.AppendLine($"--------------------");
            report.AppendLine($"总计匹配: {totalMatched} 项");
            report.AppendLine($"匹配率: {(indexData.Count > 0 ? (double)totalMatched / indexData.Count * 100 : 0):F2}%");

            MessageBox.Show(report.ToString(), "费用归并完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 读取索引字段数据
        /// </summary>
        private Dictionary<string, string> ReadIndexData(Excel.Worksheet sheet, int startRow, int startCol, int endRow, int endCol)
        {
            Dictionary<string, string> indexData = new Dictionary<string, string>();

            try
            {
                if (sheet == null)
                {
                    System.Diagnostics.Debug.WriteLine("工作表对象为空");
                    return indexData;
                }

                // 检查工作表的实际使用范围
                Excel.Range usedRange = null;
                try
                {
                    usedRange = sheet.UsedRange;
                }
                catch (Exception rangeEx)
                {
                    System.Diagnostics.Debug.WriteLine($"获取工作表使用范围时出错: {rangeEx.Message}");
                    return indexData;
                }

                if (usedRange == null)
                {
                    System.Diagnostics.Debug.WriteLine("工作表使用范围为空");
                    return indexData;
                }

                // 获取实际的行列数（注意：UsedRange是从第一个有数据的单元格开始的）
                int usedStartRow = usedRange.Row;
                int usedStartCol = usedRange.Column;
                int actualMaxRow = usedStartRow + usedRange.Rows.Count - 1;
                int actualMaxCol = usedStartCol + usedRange.Columns.Count - 1;

                // 调整范围以避免越界
                int safeStartRow = Math.Max(startRow, usedStartRow);
                int safeStartCol = Math.Max(startCol, usedStartCol);
                int safeEndRow = Math.Min(endRow, actualMaxRow);
                int safeEndCol = Math.Min(endCol, actualMaxCol);

                if (safeStartRow > actualMaxRow || safeStartCol > actualMaxCol)
                {
                    System.Diagnostics.Debug.WriteLine($"指定的起始位置超出了工作表的数据范围: 起始({safeStartRow},{safeStartCol}) 最大({actualMaxRow},{actualMaxCol})");
                    return indexData;
                }

                for (int row = safeStartRow; row <= safeEndRow; row++)
                {
                    for (int col = safeStartCol; col <= safeEndCol; col++)
                    {
                        try
                        {
                            Excel.Range cell = sheet.Cells[row, col] as Excel.Range;
                            if (cell != null)
                            {
                                // 使用 Value2 获取实际值，避免列太窄时显示 ####
                                object cellValueObj = cell.Value2;
                                string cellValue = FormatCellValue(cellValueObj, cell); // 传入源单元格

                                if (!string.IsNullOrEmpty(cellValue))
                                {
                                    string key = $"{row}_{col}";
                                    indexData[key] = cellValue;
                                }
                            }
                        }
                        catch (Exception cellEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"读取单元格 [{row},{col}] 时出错: {cellEx.Message}");
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"成功读取索引数据: {indexData.Count} 个项目");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"读取索引数据时出错: {ex.Message}");
            }

            return indexData;
        }

        /// <summary>
        /// 在数据源中查找匹配数据并写入目标表
        /// </summary>
        private int WriteMatchedData(Excel.Worksheet sourceSheet, Excel.Worksheet targetSheet, Dictionary<string, string> indexData, int targetCol, int sourceDataCol)
        {
            int matchedCount = 0;

            System.Diagnostics.Debug.WriteLine($"开始匹配数据，索引数据数量: {indexData.Count}，目标列: {targetCol}");

            // 遍历索引数据，在数据源中查找匹配项
            foreach (var indexItem in indexData)
            {
                string[] position = indexItem.Key.Split('_');
                int targetRow = int.Parse(position[0]);
                string searchValue = indexItem.Value;

                System.Diagnostics.Debug.WriteLine($"正在查找: '{searchValue}' (位置: 第{targetRow}行)");

                // 在数据源中查找匹配值
                string matchedValue = FindMatchingValue(sourceSheet, searchValue, sourceDataCol);

                if (!string.IsNullOrEmpty(matchedValue))
                {
                    System.Diagnostics.Debug.WriteLine($"找到匹配: '{searchValue}' -> '{matchedValue}'");
                    // 将匹配到的值写入目标表的对应位置
                    targetSheet.Cells[targetRow, targetCol] = matchedValue;
                    matchedCount++;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"未找到匹配: '{searchValue}'");
                }
            }

            System.Diagnostics.Debug.WriteLine($"匹配完成，共匹配 {matchedCount} 项");
            return matchedCount;
        }

        /// <summary>
        /// 将列号转换为Excel列字母（如1->A, 2->B, 26->Z, 27->AA）
        /// </summary>
        private static string ColumnNumberToLetter(int columnNumber)
        {
            string columnName = "";
            while (columnNumber > 0)
            {
                int modulo = (columnNumber - 1) % 26;
                columnName = Convert.ToChar(65 + modulo) + columnName;
                columnNumber = (columnNumber - modulo) / 26;
            }
            return columnName;
        }

        /// <summary>
        /// 在指定列中查找匹配值
        /// </summary>
        private string FindMatchingValue(Excel.Worksheet sheet, string searchValue, int sourceDataCol)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchValue))
                {
                    System.Diagnostics.Debug.WriteLine("搜索值为空");
                    return string.Empty;
                }

                Excel.Range usedRange = sheet.UsedRange;
                if (usedRange == null)
                {
                    System.Diagnostics.Debug.WriteLine("工作表没有使用范围");
                    return string.Empty;
                }

                // 修正：获取实际的行列范围
                int usedStartRow = usedRange.Row;
                int usedStartCol = usedRange.Column;
                int actualLastRow = usedStartRow + usedRange.Rows.Count - 1;
                int actualLastCol = usedStartCol + usedRange.Columns.Count - 1;

                System.Diagnostics.Debug.WriteLine($"数据源范围: 第{usedStartRow}-{actualLastRow}行，第{usedStartCol}-{actualLastCol}列");
                System.Diagnostics.Debug.WriteLine($"正在查找: '{searchValue}'");

                // 严格匹配：只进行精确匹配，避免误匹配
                System.Diagnostics.Debug.WriteLine("开始精确匹配");

                for (int row = usedStartRow; row <= actualLastRow; row++)
                {
                    // 在整行中查找匹配值
                    for (int col = usedStartCol; col <= actualLastCol; col++)
                    {
                        try
                        {
                            Excel.Range cell = sheet.Cells[row, col] as Excel.Range;
                            if (cell == null) continue;

                            // 使用 Value2 获取实际值，避免列太窄时显示 ####
                            object cellValueObj = cell.Value2;
                            string cellValue = FormatCellValue(cellValueObj, cell); // 传入源单元格
                            if (string.IsNullOrEmpty(cellValue)) continue;

                            // 只进行精确匹配，避免误匹配
                            bool isMatch = string.Equals(cellValue, searchValue, StringComparison.OrdinalIgnoreCase);

                            if (isMatch)
                            {
                                System.Diagnostics.Debug.WriteLine($"在第{row}行第{col}列找到精确匹配: '{cellValue}' == '{searchValue}'");

                                // 找到匹配项，返回指定列的值（如果指定列存在且在范围内）
                                if (sourceDataCol >= usedStartCol && sourceDataCol <= actualLastCol)
                                {
                                    try
                                    {
                                        Excel.Range dataCell = sheet.Cells[row, sourceDataCol] as Excel.Range;
                                        if (dataCell != null)
                                        {
                                            // 使用 Value2 获取实际值，避免列太窄时显示 ####
                                            object dataColumnValueObj = dataCell.Value2;
                                            string dataColumnValue = FormatCellValue(dataColumnValueObj, dataCell); // 传入源单元格

                                            if (!string.IsNullOrEmpty(dataColumnValue))
                                            {
                                                System.Diagnostics.Debug.WriteLine($"返回{ColumnNumberToLetter(sourceDataCol)}列值: '{dataColumnValue}'");
                                                return dataColumnValue;
                                            }
                                        }
                                    }
                                    catch (Exception dataEx)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"读取{ColumnNumberToLetter(sourceDataCol)}列时出错: {dataEx.Message}");
                                    }
                                }

                                // 如果指定列没有值，返回匹配行的最后一个有值的列
                                for (int returnCol = actualLastCol; returnCol >= usedStartCol; returnCol--)
                                {
                                    if (returnCol == col) continue; // 不返回匹配的那个单元格本身

                                    try
                                    {
                                        Excel.Range returnCell = sheet.Cells[row, returnCol] as Excel.Range;
                                        if (returnCell != null)
                                        {
                                            // 使用 Value2 获取实际值，避免列太窄时显示 ####
                                            object returnValueObj = returnCell.Value2;
                                            string returnValue = FormatCellValue(returnValueObj, returnCell); // 传入源单元格

                                            if (!string.IsNullOrEmpty(returnValue))
                                            {
                                                System.Diagnostics.Debug.WriteLine($"返回第{returnCol}列值: '{returnValue}'");
                                                return returnValue;
                                            }
                                        }
                                    }
                                    catch (Exception retEx)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"读取第{returnCol}列时出错: {retEx.Message}");
                                    }
                                }
                            }
                        }
                        catch (Exception cellEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"读取单元格[{row},{col}]时出错: {cellEx.Message}");
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"未找到匹配项: '{searchValue}'");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"查找匹配值时出错: {ex.Message}");
            }

            return string.Empty;
        }

        /// <summary>
        /// 测试用的公共方法，用于在预览中调用ReadIndexData
        /// </summary>
        public Dictionary<string, string> TestReadIndexData(Excel.Worksheet sheet, int startRow, int startCol, int endRow, int endCol)
        {
            return ReadIndexData(sheet, startRow, startCol, endRow, endCol);
        }

    }

    /// <summary>
    /// 费用归并配置信息
    /// </summary>
    public class ExpenseMergeConfig
    {
        public string TargetSheetName { get; set; }
        public string IndexSheetName { get; set; }
        public int IndexStartRow { get; set; }
        public int IndexStartCol { get; set; }
        public int IndexEndRow { get; set; }
        public int IndexEndCol { get; set; }
        public int DataColumn { get; set; } = 4; // 取数列（默认D列，用户可选择）
        public int OutputDataStartCol { get; set; } = 3; // 数据输出起始列（默认C列）
        public int OutputFileNameRow { get; set; } = 7; // 文件名输出行（默认第7行）
        public int OutputFileNameStartCol { get; set; } = 3; // 文件名输出起始列（默认C列）
        public List<SourceFileInfo> SourceFiles { get; set; } = new List<SourceFileInfo>();
    }

    /// <summary>
    /// 数据源文件信息
    /// </summary>
    public class SourceFileInfo
    {
        public string FilePath { get; set; }
        public string SheetName { get; set; }
    }

    /// <summary>
    /// 处理结果信息
    /// </summary>
    public class ProcessResult
    {
        public string FileName { get; set; }
        public int MatchCount { get; set; }
    }

    /// <summary>
    /// 费用归并配置窗体
    /// </summary>
    public class ExpenseMergeConfigForm : Form
    {
        private ComboBox cmbTargetSheet, cmbIndexSheet, cmbDataColumn;
        private NumericUpDown nudIndexStartRow, nudIndexStartCol, nudIndexEndRow, nudIndexEndCol;
        private NumericUpDown nudOutputDataStartCol, nudOutputFileNameRow, nudOutputFileNameStartCol;
        private ListBox lstSourceFiles;
        private Button btnAddSource, btnRemoveSource, btnOK, btnCancel, btnPreview;
        private TextBox txtPreview;

        public ExpenseMergeConfig Config { get; private set; }

        /// <summary>
        /// 将列号转换为Excel列字母（如1->A, 2->B, 26->Z, 27->AA）
        /// </summary>
        private string ColumnNumberToLetter(int columnNumber)
        {
            string columnName = "";
            while (columnNumber > 0)
            {
                int modulo = (columnNumber - 1) % 26;
                columnName = Convert.ToChar(65 + modulo) + columnName;
                columnNumber = (columnNumber - modulo) / 26;
            }
            return columnName;
        }

        /// <summary>
        /// 将Excel列字母转换为列号（如A->1, B->2, Z->26, AA->27）
        /// </summary>
        private int ColumnLetterToNumber(string columnLetter)
        {
            int columnNumber = 0;
            for (int i = 0; i < columnLetter.Length; i++)
            {
                columnNumber = columnNumber * 26 + (columnLetter[i] - 'A' + 1);
            }
            return columnNumber;
        }

        public ExpenseMergeConfigForm()
        {
            InitializeComponents();
            LoadTargetSheets();
        }

        private void InitializeComponents()
        {
            this.Text = "费用归并配置 - 智能数据匹配工具 v2.0";

            // 自动适应屏幕尺寸
            Rectangle screenBounds = Screen.PrimaryScreen.WorkingArea;
            int formWidth = Math.Min(1200, (int)(screenBounds.Width * 0.8));
            int formHeight = Math.Min(1000, (int)(screenBounds.Height * 0.9));

            this.Size = new Size(formWidth, formHeight);
            this.MinimumSize = new Size(1000, 800);
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MaximizeBox = true;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.AutoScroll = true;
            this.AutoScaleMode = AutoScaleMode.Dpi;

            // 创建主分割容器 - 使用SplitContainer实现可拉伸布局
            var mainSplitContainer = new SplitContainer
            {
                Dock = DockStyle.Fill,
                Orientation = Orientation.Horizontal,
                SplitterDistance = 700,
                SplitterWidth = 5,
                BackColor = Color.FromArgb(200, 200, 200),
                Panel1MinSize = 400,
                Panel2MinSize = 100
            };

            // 上半部分：配置区域
            var configPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(10)
            };

            // 1. 标题
            var titleLabel = new Label
            {
                Text = "📊 费用归并配置向导",
                Dock = DockStyle.Top,
                Height = 70,
                Font = new Font("微软雅黑", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Margin = new Padding(0, 0, 0, 15)
            };

            // 创建可拉伸的分割容器1：工作表选择和索引配置
            var splitContainer1 = new SplitContainer
            {
                Dock = DockStyle.Top,
                Height = 400,
                Orientation = Orientation.Horizontal,
                SplitterDistance = 150,
                SplitterWidth = 5,
                BackColor = Color.FromArgb(200, 200, 200),
                Panel1MinSize = 100,
                Panel2MinSize = 200,
                Margin = new Padding(0, 0, 0, 15)
            };

            // 2. 工作表选择组
            var targetGroup = new GroupBox
            {
                Text = "📋 工作表选择",
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 150, 243),
                BackColor = Color.White,
                Padding = new Padding(15)
            };

            // 使用TableLayoutPanel来确保控件正确布局
            var targetTablePanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 2,
                Padding = new Padding(10)
            };

            // 设置列样式
            targetTablePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 200)); // 标签列
            targetTablePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));  // 下拉框列

            // 设置行样式
            targetTablePanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));  // 索引工作表行
            targetTablePanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));  // 目标工作表行

            // 索引字段来源工作表
            var lblIndexSheet = new Label
            {
                Text = "🔍 索引字段来源工作表:",
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 10),
                ForeColor = Color.FromArgb(76, 175, 80),
                TextAlign = ContentAlignment.MiddleLeft
            };
            cmbIndexSheet = new ComboBox
            {
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("微软雅黑", 10),
                Margin = new Padding(0, 5, 0, 5)
            };

            // 数据写入目标工作表
            var lblTargetSheet = new Label
            {
                Text = "📝 数据写入目标工作表:",
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 10),
                ForeColor = Color.FromArgb(255, 152, 0),
                TextAlign = ContentAlignment.MiddleLeft
            };
            cmbTargetSheet = new ComboBox
            {
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("微软雅黑", 10),
                Margin = new Padding(0, 5, 0, 5)
            };

            // 添加控件到表格布局
            targetTablePanel.Controls.Add(lblIndexSheet, 0, 0);
            targetTablePanel.Controls.Add(cmbIndexSheet, 1, 0);
            targetTablePanel.Controls.Add(lblTargetSheet, 0, 1);
            targetTablePanel.Controls.Add(cmbTargetSheet, 1, 1);

            targetGroup.Controls.Add(targetTablePanel);
            splitContainer1.Panel1.Controls.Add(targetGroup);

            // 3. 索引字段范围设置组
            var indexGroup = new GroupBox
            {
                Text = "🎯 索引字段范围 (要查找归并的数据范围)",
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(156, 39, 176),
                BackColor = Color.White,
                Padding = new Padding(15)
            };

            // 使用TableLayoutPanel来确保控件正确布局
            var indexTablePanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 5,
                RowCount = 3,
                Padding = new Padding(10)
            };

            // 设置列样式
            indexTablePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));  // 标签1
            indexTablePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100)); // 控件1
            indexTablePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80));  // 标签2
            indexTablePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100)); // 控件2
            indexTablePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));  // 预览按钮

            // 设置行样式
            indexTablePanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));  // 起始位置行
            indexTablePanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));  // 结束位置行
            indexTablePanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100));  // 预览区域

            // 起始位置控件
            var lblStartRow = new Label
            {
                Text = "起始行:",
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 10),
                ForeColor = Color.FromArgb(76, 175, 80),
                TextAlign = ContentAlignment.MiddleLeft
            };
            nudIndexStartRow = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Minimum = 1,
                Maximum = 1000,
                Value = 8,
                Font = new Font("微软雅黑", 10),
                Margin = new Padding(0, 5, 5, 5)
            };

            var lblStartCol = new Label
            {
                Text = "起始列:",
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 10),
                ForeColor = Color.FromArgb(76, 175, 80),
                TextAlign = ContentAlignment.MiddleLeft
            };
            nudIndexStartCol = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Minimum = 1,
                Maximum = 100,
                Value = 1,
                Font = new Font("微软雅黑", 10),
                Margin = new Padding(0, 5, 5, 5)
            };

            // 结束位置控件
            var lblEndRow = new Label
            {
                Text = "结束行:",
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 10),
                ForeColor = Color.FromArgb(255, 87, 34),
                TextAlign = ContentAlignment.MiddleLeft
            };
            nudIndexEndRow = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Minimum = 1,
                Maximum = 1000,
                Value = 50,
                Font = new Font("微软雅黑", 10),
                Margin = new Padding(0, 5, 5, 5)
            };

            var lblEndCol = new Label
            {
                Text = "结束列:",
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 10),
                ForeColor = Color.FromArgb(255, 87, 34),
                TextAlign = ContentAlignment.MiddleLeft
            };
            nudIndexEndCol = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Minimum = 1,
                Maximum = 100,
                Value = 2,
                Font = new Font("微软雅黑", 10),
                Margin = new Padding(0, 5, 5, 5)
            };

            // 预览按钮
            btnPreview = new Button
            {
                Text = "🔍 预览索引数据",
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                Margin = new Padding(10, 5, 0, 5)
            };
            btnPreview.Click += BtnPreview_Click;

            // 预览区域
            txtPreview = new TextBox
            {
                Dock = DockStyle.Fill,
                Multiline = true,
                ReadOnly = true,
                ScrollBars = ScrollBars.Both,
                Font = new Font("Consolas", 9),
                BackColor = Color.FromArgb(248, 248, 248),
                BorderStyle = BorderStyle.FixedSingle,
                Text = "点击\"预览索引数据\"按钮查看数据...",
                Margin = new Padding(0, 5, 0, 0)
            };

            // 添加控件到表格布局
            indexTablePanel.Controls.Add(lblStartRow, 0, 0);
            indexTablePanel.Controls.Add(nudIndexStartRow, 1, 0);
            indexTablePanel.Controls.Add(lblStartCol, 2, 0);
            indexTablePanel.Controls.Add(nudIndexStartCol, 3, 0);
            indexTablePanel.Controls.Add(btnPreview, 4, 0);
            indexTablePanel.SetRowSpan(btnPreview, 2);

            indexTablePanel.Controls.Add(lblEndRow, 0, 1);
            indexTablePanel.Controls.Add(nudIndexEndRow, 1, 1);
            indexTablePanel.Controls.Add(lblEndCol, 2, 1);
            indexTablePanel.Controls.Add(nudIndexEndCol, 3, 1);

            indexTablePanel.Controls.Add(txtPreview, 0, 2);
            indexTablePanel.SetColumnSpan(txtPreview, 5);

            indexGroup.Controls.Add(indexTablePanel);
            splitContainer1.Panel2.Controls.Add(indexGroup);

            // 创建可拉伸的分割容器2：输出配置和数据源文件管理
            var splitContainer2 = new SplitContainer
            {
                Dock = DockStyle.Top,
                Height = 350,
                Orientation = Orientation.Horizontal,
                SplitterDistance = 120,
                SplitterWidth = 5,
                BackColor = Color.FromArgb(200, 200, 200),
                Panel1MinSize = 80,
                Panel2MinSize = 150,
                Margin = new Padding(0, 0, 0, 15)
            };

            // 4. 输出配置组
            var outputGroup = new GroupBox
            {
                Text = "📤 输出配置 - 灵活设置数据和文件名的输出位置",
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(255, 152, 0),
                BackColor = Color.White,
                Padding = new Padding(15)
            };

            // 使用TableLayoutPanel来确保控件正确布局
            var outputTablePanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 3,
                Padding = new Padding(10)
            };

            // 设置列样式
            outputTablePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25));
            outputTablePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25));
            outputTablePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25));
            outputTablePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25));

            // 设置行样式
            outputTablePanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30));  // 提示行
            outputTablePanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 30));  // 标签行
            outputTablePanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 35));  // 控件行

            // 添加说明标签
            var lblOutputHint = new Label
            {
                Text = "💡 提示：现在可以自由设置取数列和输出位置，让功能更加灵活！",
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 9),
                ForeColor = Color.FromArgb(96, 125, 139),
                TextAlign = ContentAlignment.MiddleLeft
            };
            outputTablePanel.Controls.Add(lblOutputHint, 0, 0);
            outputTablePanel.SetColumnSpan(lblOutputHint, 4);

            // 取数列选择
            var lblDataColumn = new Label
            {
                Text = "🎯 取数列:",
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 10),
                ForeColor = Color.FromArgb(255, 87, 34),
                TextAlign = ContentAlignment.MiddleLeft
            };
            cmbDataColumn = new ComboBox
            {
                Dock = DockStyle.Fill,
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("微软雅黑", 10),
                Margin = new Padding(0, 5, 0, 0)
            };
            // 初始化取数列选项（A-Z列）
            for (int i = 1; i <= 26; i++)
            {
                cmbDataColumn.Items.Add($"{ColumnNumberToLetter(i)}列");
            }
            cmbDataColumn.SelectedIndex = 3; // 默认选择D列

            // 数据输出列
            var lblOutputDataCol = new Label
            {
                Text = "📊 数据输出起始列:",
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 10),
                ForeColor = Color.FromArgb(76, 175, 80),
                TextAlign = ContentAlignment.MiddleLeft
            };
            nudOutputDataStartCol = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Minimum = 1,
                Maximum = 100,
                Value = 3,
                Font = new Font("微软雅黑", 10),
                Margin = new Padding(0, 5, 0, 0)
            };

            // 文件名输出行
            var lblOutputFileNameRow = new Label
            {
                Text = "📝 文件名输出行:",
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 10),
                ForeColor = Color.FromArgb(33, 150, 243),
                TextAlign = ContentAlignment.MiddleLeft
            };
            nudOutputFileNameRow = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Minimum = 1,
                Maximum = 1000,
                Value = 7,
                Font = new Font("微软雅黑", 10),
                Margin = new Padding(0, 5, 0, 0)
            };

            // 文件名输出列
            var lblOutputFileNameCol = new Label
            {
                Text = "📁 文件名输出起始列:",
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 10),
                ForeColor = Color.FromArgb(156, 39, 176),
                TextAlign = ContentAlignment.MiddleLeft
            };
            nudOutputFileNameStartCol = new NumericUpDown
            {
                Dock = DockStyle.Fill,
                Minimum = 1,
                Maximum = 100,
                Value = 3,
                Font = new Font("微软雅黑", 10),
                Margin = new Padding(0, 5, 0, 0)
            };

            // 添加控件到表格布局
            outputTablePanel.Controls.Add(lblDataColumn, 0, 1);
            outputTablePanel.Controls.Add(cmbDataColumn, 0, 2);
            outputTablePanel.Controls.Add(lblOutputDataCol, 1, 1);
            outputTablePanel.Controls.Add(nudOutputDataStartCol, 1, 2);
            outputTablePanel.Controls.Add(lblOutputFileNameRow, 2, 1);
            outputTablePanel.Controls.Add(nudOutputFileNameRow, 2, 2);
            outputTablePanel.Controls.Add(lblOutputFileNameCol, 3, 1);
            outputTablePanel.Controls.Add(nudOutputFileNameStartCol, 3, 2);

            outputGroup.Controls.Add(outputTablePanel);
            splitContainer2.Panel1.Controls.Add(outputGroup);

            // 5. 数据源文件管理组
            var sourceGroup = new GroupBox
            {
                Text = "📁 数据源文件 - 重要：选择包含数据的附表！",
                Dock = DockStyle.Fill,
                ForeColor = Color.FromArgb(255, 87, 34),
                Font = new Font("微软雅黑", 11, FontStyle.Bold),
                BackColor = Color.White,
                Padding = new Padding(15)
            };

            // 使用TableLayoutPanel来确保按钮正确显示
            var sourceTablePanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                Padding = new Padding(5)
            };

            // 设置列样式：文件列表占70%，按钮区占30%
            sourceTablePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70));
            sourceTablePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30));

            // 文件列表
            lstSourceFiles = new ListBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 10),
                BackColor = Color.FromArgb(248, 248, 248),
                BorderStyle = BorderStyle.FixedSingle,
                SelectionMode = SelectionMode.One,
                Margin = new Padding(5)
            };

            // 按钮面板
            var buttonPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(5)
            };

            // 按钮
            btnAddSource = new Button
            {
                Text = "📂 添加数据源文件\n(选择附表)",
                Location = new Point(10, 10),
                Size = new Size(140, 70),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 10, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };
            btnRemoveSource = new Button
            {
                Text = "🗑️ 移除选中文件",
                Location = new Point(10, 90),
                Size = new Size(140, 50),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 10, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            btnAddSource.Click += BtnAddSource_Click;
            btnRemoveSource.Click += BtnRemoveSource_Click;

            buttonPanel.Controls.AddRange(new Control[] { btnAddSource, btnRemoveSource });

            sourceTablePanel.Controls.Add(lstSourceFiles, 0, 0);
            sourceTablePanel.Controls.Add(buttonPanel, 1, 0);
            sourceGroup.Controls.Add(sourceTablePanel);
            splitContainer2.Panel2.Controls.Add(sourceGroup);



            // 按正确顺序添加到配置面板
            configPanel.Controls.Add(splitContainer2);  // Top - 输出配置和数据源文件
            configPanel.Controls.Add(splitContainer1);  // Top - 工作表选择和索引配置
            configPanel.Controls.Add(titleLabel);       // Top - 标题

            // 将配置面板添加到主分割容器的上半部分
            mainSplitContainer.Panel1.Controls.Add(configPanel);

            // 下半部分：按钮和说明区域
            var bottomPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(15),
                BackColor = Color.FromArgb(250, 250, 250)
            };

            // 使用说明
            var instructionLabel = new Label
            {
                Text = "📋 操作步骤：\n" +
                       "1️⃣ 选择索引字段来源工作表和数据写入目标工作表\n" +
                       "2️⃣ 设置索引字段范围（要查找的关键字范围）\n" +
                       "3️⃣ 选择取数列（从哪一列获取数据，如A、B、C、D等）\n" +
                       "4️⃣ 配置输出位置（数据输出列、文件名输出行和列）\n" +
                       "5️⃣ 点击\"预览索引数据\"确认读取到正确的关键字\n" +
                       "6️⃣ 添加数据源文件，选择包含数据的工作表（通常是附表）\n" +
                       "7️⃣ 程序会在数据源文件中查找与关键字匹配的项目\n" +
                       "8️⃣ 将匹配到的指定列数据写入到您指定的输出位置\n" +
                       "9️⃣ 文件名将写入到您指定的文件名输出位置",
                Dock = DockStyle.Top,
                Height = 200,
                Font = new Font("微软雅黑", 9),
                ForeColor = Color.FromArgb(33, 33, 33),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(15)
            };

            // 底部按钮面板 - 使用FlowLayoutPanel确保按钮正确显示
            var bottomButtonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Bottom,
                Height = 80,
                FlowDirection = FlowDirection.RightToLeft,
                Padding = new Padding(15),
                AutoSize = false
            };

            btnOK = new Button
            {
                Text = "🚀 开始归并",
                Size = new Size(140, 50),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 12, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                Margin = new Padding(5)
            };

            btnCancel = new Button
            {
                Text = "❌ 取消",
                Size = new Size(120, 50),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 12, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                Margin = new Padding(5)
            };

            btnOK.Click += BtnOK_Click;
            btnCancel.Click += (s, e) => { this.DialogResult = DialogResult.Cancel; this.Close(); };

            // 注意：FlowLayoutPanel从右到左，所以先添加的按钮会显示在右边
            bottomButtonPanel.Controls.Add(btnOK);
            bottomButtonPanel.Controls.Add(btnCancel);
            bottomPanel.Controls.Add(bottomButtonPanel);
            bottomPanel.Controls.Add(instructionLabel);

            // 将底部面板添加到主分割容器的下半部分
            mainSplitContainer.Panel2.Controls.Add(bottomPanel);

            // 将主分割容器添加到窗体
            this.Controls.Add(mainSplitContainer);

            // 添加事件处理
            btnAddSource.Click += BtnAddSource_Click;
            btnRemoveSource.Click += BtnRemoveSource_Click;
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += (s, e) => { this.DialogResult = DialogResult.Cancel; this.Close(); };
        }

        private void LoadTargetSheets()
        {
            try
            {
                Excel.Application excelApp = Globals.ThisAddIn.Application;
                Excel.Workbook workbook = excelApp.ActiveWorkbook;

                if (workbook != null)
                {
                    // 清空两个下拉框
                    cmbTargetSheet.Items.Clear();
                    cmbIndexSheet.Items.Clear();

                    int indexSheetDefaultIndex = 0;
                    int targetSheetDefaultIndex = 0;
                    int currentIndex = 0;

                    foreach (Excel.Worksheet sheet in workbook.Sheets)
                    {
                        string sheetName = sheet.Name;
                        cmbTargetSheet.Items.Add(sheetName);
                        cmbIndexSheet.Items.Add(sheetName);

                        // 智能选择默认工作表
                        string lowerName = sheetName.ToLower();

                        // 对于索引字段来源：优先选择附表、明细表
                        if (lowerName.Contains("附表") || lowerName.Contains("明细") ||
                            lowerName.Contains("清单") || lowerName.Contains("detail"))
                        {
                            indexSheetDefaultIndex = currentIndex;
                        }

                        // 对于目标工作表：优先选择汇总表、主表
                        if (lowerName.Contains("汇总") || lowerName.Contains("主表") ||
                            lowerName.Contains("总表") || lowerName.Contains("summary") ||
                            currentIndex == 0) // 如果没有特殊标识，选择第一个
                        {
                            targetSheetDefaultIndex = currentIndex;
                        }

                        currentIndex++;
                    }

                    if (cmbTargetSheet.Items.Count > 0)
                    {
                        // 不设置默认选择，让用户自己选择
                        // cmbTargetSheet.SelectedIndex = targetSheetDefaultIndex;
                        // cmbIndexSheet.SelectedIndex = indexSheetDefaultIndex;

                        // 添加提示信息
                        if (cmbTargetSheet.Items.Count > 1)
                        {
                            // 在下拉框中添加提示项
                            cmbTargetSheet.Items.Insert(0, "-- 请选择数据写入目标工作表 --");
                            cmbIndexSheet.Items.Insert(0, "-- 请选择索引字段来源工作表 --");
                            cmbTargetSheet.SelectedIndex = 0;
                            cmbIndexSheet.SelectedIndex = 0;
                        }
                    }
                }
                else
                {
                    MessageBox.Show("请先打开一个Excel工作簿！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载工作表失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnPreview_Click(object sender, EventArgs e)
        {
            try
            {
                if (cmbIndexSheet.SelectedItem == null || cmbIndexSheet.SelectedItem.ToString().StartsWith("--"))
                {
                    MessageBox.Show("请先选择索引字段来源工作表！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cmbIndexSheet.Focus();
                    return;
                }

                Excel.Application excelApp = Globals.ThisAddIn.Application;
                Excel.Workbook workbook = excelApp.ActiveWorkbook;

                if (workbook == null)
                {
                    txtPreview.Text = "没有打开的Excel工作簿";
                    return;
                }

                Excel.Worksheet indexSheet = null;
                try
                {
                    indexSheet = workbook.Sheets[cmbIndexSheet.SelectedItem.ToString()];
                }
                catch (Exception sheetEx)
                {
                    txtPreview.Text = $"无法找到索引字段来源工作表 '{cmbIndexSheet.SelectedItem}': {sheetEx.Message}";
                    return;
                }

                int startRow = (int)nudIndexStartRow.Value;
                int startCol = (int)nudIndexStartCol.Value;
                int endRow = (int)nudIndexEndRow.Value;
                int endCol = (int)nudIndexEndCol.Value;

                StringBuilder preview = new StringBuilder();
                preview.AppendLine($"索引字段来源工作表: {indexSheet.Name}");
                preview.AppendLine($"指定范围: 第{startRow}-{endRow}行，第{startCol}-{endCol}列");
                preview.AppendLine("------------------------");

                // 检查工作表的实际使用范围
                Excel.Range usedRange = null;
                try
                {
                    usedRange = indexSheet.UsedRange;
                }
                catch (Exception rangeEx)
                {
                    txtPreview.Text = $"无法获取工作表使用范围: {rangeEx.Message}";
                    return;
                }

                if (usedRange == null)
                {
                    txtPreview.Text = "工作表中没有数据";
                    return;
                }

                // 获取实际的行列数（注意：UsedRange是从第一个有数据的单元格开始的）
                int usedStartRow = usedRange.Row;
                int usedStartCol = usedRange.Column;
                int actualMaxRow = usedStartRow + usedRange.Rows.Count - 1;
                int actualMaxCol = usedStartCol + usedRange.Columns.Count - 1;

                preview.AppendLine($"工作表实际数据范围: 第{usedStartRow}-{actualMaxRow}行，第{usedStartCol}-{actualMaxCol}列");
                preview.AppendLine("索引数据预览:");
                preview.AppendLine("行号\t列号\t内容");
                preview.AppendLine("------------------------");

                // 调整范围以避免越界
                int safeStartRow = Math.Max(startRow, usedStartRow);
                int safeStartCol = Math.Max(startCol, usedStartCol);
                int safeEndRow = Math.Min(endRow, actualMaxRow);
                int safeEndCol = Math.Min(endCol, actualMaxCol);

                if (safeStartRow > actualMaxRow || safeStartCol > actualMaxCol)
                {
                    preview.AppendLine("指定的起始位置超出了工作表的数据范围");
                    txtPreview.Text = preview.ToString();
                    return;
                }

                int previewCount = 0;
                for (int row = safeStartRow; row <= safeEndRow && previewCount < 20; row++)
                {
                    for (int col = safeStartCol; col <= safeEndCol; col++)
                    {
                        try
                        {
                            Excel.Range cell = indexSheet.Cells[row, col] as Excel.Range;
                            if (cell != null)
                            {
                                // 使用 Value2 获取实际值，避免列太窄时显示 ####
                                object cellValueObj = cell.Value2;
                                string cellValue = Ribbon1.FormatCellValue(cellValueObj, cell); // 传入源单元格

                                if (!string.IsNullOrEmpty(cellValue))
                                {
                                    preview.AppendLine($"{row}\t{col}\t{cellValue}");
                                    previewCount++;
                                    if (previewCount >= 20) break;
                                }
                            }
                        }
                        catch (Exception cellEx)
                        {
                            preview.AppendLine($"{row}\t{col}\t[读取错误: {cellEx.Message}]");
                        }
                    }
                }

                if (previewCount == 0)
                {
                    preview.AppendLine("指定范围内没有找到非空数据");
                    preview.AppendLine("建议：");
                    preview.AppendLine("1. 检查索引字段范围设置");
                    preview.AppendLine("2. 确认工作表中有数据");
                }
                else
                {
                    preview.AppendLine($"\n共找到 {previewCount} 个非空单元格");
                    preview.AppendLine("这些数据将用作查找关键字");
                }

                // 测试读取索引数据功能
                preview.AppendLine("\n=== 测试索引数据读取 ===");
                try
                {
                    // 直接在这里实现简化的索引数据读取逻辑
                    var testIndexData = new Dictionary<string, string>();

                    for (int row = safeStartRow; row <= safeEndRow; row++)
                    {
                        for (int col = safeStartCol; col <= safeEndCol; col++)
                        {
                            try
                            {
                                Excel.Range cell = indexSheet.Cells[row, col] as Excel.Range;
                                if (cell != null)
                                {
                                    // 使用 Value2 获取实际值，避免列太窄时显示 ####
                                    object cellValueObj = cell.Value2;
                                    string cellValue = Ribbon1.FormatCellValue(cellValueObj, cell); // 传入源单元格

                                    if (!string.IsNullOrEmpty(cellValue))
                                    {
                                        string key = $"{row}_{col}";
                                        testIndexData[key] = cellValue;
                                    }
                                }
                            }
                            catch { }
                        }
                    }

                    preview.AppendLine($"索引数据读取测试: 找到 {testIndexData.Count} 个索引项");
                    int testCount = 0;
                    foreach (var item in testIndexData)
                    {
                        if (testCount < 5) // 只显示前5个
                        {
                            preview.AppendLine($"  {item.Key} -> '{item.Value}'");
                            testCount++;
                        }
                    }
                    if (testIndexData.Count > 5)
                    {
                        preview.AppendLine($"  ... 还有 {testIndexData.Count - 5} 个项目");
                    }
                }
                catch (Exception testEx)
                {
                    preview.AppendLine($"测试读取索引数据时出错: {testEx.Message}");
                }

                txtPreview.Text = preview.ToString();
            }
            catch (Exception ex)
            {
                txtPreview.Text = $"预览失败: {ex.Message}\n\n可能的原因:\n1. 工作表不存在\n2. 指定的行列范围超出限制\n3. Excel应用程序异常";
            }
        }

        private void BtnAddSource_Click(object sender, EventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Title = "选择数据源Excel文件（包含要查找数据的文件）",
                Filter = "Excel文件|*.xls;*.xlsx;*.xlsm",
                Multiselect = true,
                CheckFileExists = true,
                CheckPathExists = true
            };

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                foreach (string filePath in openFileDialog.FileNames)
                {
                    try
                    {
                        // 检查文件是否可以访问
                        if (!File.Exists(filePath))
                        {
                            MessageBox.Show($"文件不存在: {filePath}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            continue;
                        }

                        // 显示提示信息
                        string selectedDataColumnText = cmbDataColumn.SelectedItem?.ToString() ?? "D列";
                        var result = MessageBox.Show(
                            $"即将为文件 '{Path.GetFileName(filePath)}' 选择工作表。\n\n" +
                            "请注意：\n" +
                            "• 选择包含您要查找数据的工作表（通常是附表或明细表）\n" +
                            $"• 确保所选工作表的{selectedDataColumnText}包含您需要的数据\n" +
                            "• 程序会在此工作表中查找与索引字段匹配的数据\n\n" +
                            "点击'确定'继续选择工作表",
                            "选择数据源工作表",
                            MessageBoxButtons.OKCancel,
                            MessageBoxIcon.Information
                        );

                        if (result != DialogResult.OK)
                            continue;

                        // 让用户选择工作表并预览数据
                        int selectedDataColumnIndex = cmbDataColumn.SelectedIndex + 1; // 转换为列号
                        using (var sheetSelector = new EnhancedSourceSheetSelectorForm(filePath, selectedDataColumnIndex))
                        {
                            if (sheetSelector.ShowDialog() == DialogResult.OK)
                            {
                                string displayText = $"{Path.GetFileName(filePath)} - 【{sheetSelector.SelectedSheetName}】 (数据行数: {sheetSelector.DataRowCount})";
                                lstSourceFiles.Items.Add(displayText);
                                lstSourceFiles.Tag = lstSourceFiles.Tag ?? new List<SourceFileInfo>();
                                ((List<SourceFileInfo>)lstSourceFiles.Tag).Add(new SourceFileInfo
                                {
                                    FilePath = filePath,
                                    SheetName = sheetSelector.SelectedSheetName
                                });

                                // 显示添加成功的提示
                                MessageBox.Show(
                                    $"成功添加数据源：\n" +
                                    $"文件：{Path.GetFileName(filePath)}\n" +
                                    $"工作表：{sheetSelector.SelectedSheetName}\n" +
                                    $"数据行数：{sheetSelector.DataRowCount}",
                                    "添加成功",
                                    MessageBoxButtons.OK,
                                    MessageBoxIcon.Information
                                );
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"处理文件 {Path.GetFileName(filePath)} 时出错:\n{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void BtnRemoveSource_Click(object sender, EventArgs e)
        {
            if (lstSourceFiles.SelectedIndex >= 0)
            {
                int index = lstSourceFiles.SelectedIndex;
                lstSourceFiles.Items.RemoveAt(index);

                if (lstSourceFiles.Tag is List<SourceFileInfo> sourceFiles && index < sourceFiles.Count)
                {
                    sourceFiles.RemoveAt(index);
                }
            }
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            // 验证输入
            if (cmbTargetSheet.SelectedItem == null || cmbTargetSheet.SelectedItem.ToString().StartsWith("--"))
            {
                MessageBox.Show("请选择数据写入目标工作表！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbTargetSheet.Focus();
                return;
            }

            if (cmbIndexSheet.SelectedItem == null || cmbIndexSheet.SelectedItem.ToString().StartsWith("--"))
            {
                MessageBox.Show("请选择索引字段来源工作表！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbIndexSheet.Focus();
                return;
            }

            if (lstSourceFiles.Items.Count == 0)
            {
                MessageBox.Show("请至少添加一个数据源文件！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 验证索引范围
            if (nudIndexStartRow.Value > nudIndexEndRow.Value)
            {
                MessageBox.Show("起始行不能大于结束行！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (nudIndexStartCol.Value > nudIndexEndCol.Value)
            {
                MessageBox.Show("起始列不能大于结束列！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 最后确认
            string selectedDataColumn = cmbDataColumn.SelectedItem?.ToString() ?? "D列";
            var result = MessageBox.Show(
                $"确认开始费用归并操作？\n\n" +
                $"目标工作表: {cmbTargetSheet.SelectedItem}\n" +
                $"索引范围: 第{nudIndexStartRow.Value}-{nudIndexEndRow.Value}行，第{nudIndexStartCol.Value}-{nudIndexEndCol.Value}列\n" +
                $"取数列: {selectedDataColumn}\n" +
                $"数据源文件: {lstSourceFiles.Items.Count}个\n\n" +
                $"操作将修改目标工作表，建议先备份文件。",
                "确认操作",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question
            );

            if (result != DialogResult.Yes)
                return;

            // 创建配置对象
            Config = new ExpenseMergeConfig
            {
                TargetSheetName = cmbTargetSheet.SelectedItem.ToString(),
                IndexSheetName = cmbIndexSheet.SelectedItem.ToString(),
                IndexStartRow = (int)nudIndexStartRow.Value,
                IndexStartCol = (int)nudIndexStartCol.Value,
                IndexEndRow = (int)nudIndexEndRow.Value,
                IndexEndCol = (int)nudIndexEndCol.Value,
                DataColumn = cmbDataColumn.SelectedIndex + 1, // 转换为列号（A=1, B=2, C=3, D=4...）
                OutputDataStartCol = (int)nudOutputDataStartCol.Value,
                OutputFileNameRow = (int)nudOutputFileNameRow.Value,
                OutputFileNameStartCol = (int)nudOutputFileNameStartCol.Value,
                SourceFiles = (List<SourceFileInfo>)lstSourceFiles.Tag ?? new List<SourceFileInfo>()
            };

            this.DialogResult = DialogResult.OK;
            this.Close();
        }
    }

    /// <summary>
    /// 数据源工作表选择器窗体
    /// </summary>
    public class SourceSheetSelectorForm : Form
    {
        private ComboBox cmbSheets;
        private Button btnOK, btnCancel;

        public string SelectedSheetName { get; private set; }

        public SourceSheetSelectorForm(string filePath)
        {
            InitializeComponents();
            LoadSheets(filePath);
        }

        private void InitializeComponents()
        {
            this.Text = "选择工作表";
            this.Size = new Size(300, 150);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MaximizeBox = false;

            var lblSheet = new Label { Text = "选择工作表:", Location = new Point(10, 20), Size = new Size(80, 20) };
            cmbSheets = new ComboBox { Location = new Point(100, 17), Size = new Size(150, 25), DropDownStyle = ComboBoxStyle.DropDownList };

            btnOK = new Button { Text = "确定", Location = new Point(100, 60), Size = new Size(70, 30) };
            btnCancel = new Button { Text = "取消", Location = new Point(180, 60), Size = new Size(70, 30) };

            btnOK.Click += (s, e) =>
            {
                if (cmbSheets.SelectedItem != null)
                {
                    SelectedSheetName = cmbSheets.SelectedItem.ToString();
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("请选择一个工作表！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            };

            btnCancel.Click += (s, e) => { this.DialogResult = DialogResult.Cancel; this.Close(); };

            this.Controls.AddRange(new Control[] { lblSheet, cmbSheets, btnOK, btnCancel });
        }

        private void LoadSheets(string filePath)
        {
            Excel.Application excelApp = Globals.ThisAddIn.Application;
            Excel.Workbook workbook = null;

            try
            {
                workbook = excelApp.Workbooks.Open(filePath, ReadOnly: true);

                foreach (Excel.Worksheet sheet in workbook.Sheets)
                {
                    cmbSheets.Items.Add(sheet.Name);
                }

                if (cmbSheets.Items.Count > 0)
                    cmbSheets.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载工作表失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                workbook?.Close(false);
            }
        }
    }

    /// <summary>
    /// 增强版数据源工作表选择器窗体
    /// </summary>
    public class EnhancedSourceSheetSelectorForm : Form
    {
        private ComboBox cmbSheets;
        private Button btnOK, btnCancel;
        private TextBox txtPreview;

        public string SelectedSheetName { get; private set; }
        public int DataRowCount { get; private set; }
        private string filePath;
        private int dataColumn; // 用户选择的数据列

        public EnhancedSourceSheetSelectorForm(string filePath, int dataColumn = 4)
        {
            this.filePath = filePath;
            this.dataColumn = dataColumn;
            InitializeComponents();
            LoadSheets();
        }

        /// <summary>
        /// 将列号转换为Excel列字母（如1->A, 2->B, 26->Z, 27->AA）
        /// </summary>
        private string ColumnNumberToLetter(int columnNumber)
        {
            string columnName = "";
            while (columnNumber > 0)
            {
                int modulo = (columnNumber - 1) % 26;
                columnName = Convert.ToChar(65 + modulo) + columnName;
                columnNumber = (columnNumber - modulo) / 26;
            }
            return columnName;
        }

        private void InitializeComponents()
        {
            this.Text = $"★ 选择数据源工作表 - {Path.GetFileName(filePath)} ★";
            this.Size = new Size(750, 600);
            this.MinimumSize = new Size(650, 500);
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MaximizeBox = true;

            // 创建主布局
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 4,
                Padding = new Padding(10)
            };

            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));   // 说明文字
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 60));   // 工作表选择
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100));   // 预览区域
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 50));   // 按钮

            // 0. 说明文字
            var lblInstruction = new Label
            {
                Text = "⚠️ 重要：请选择包含要查找数据的工作表！\n" +
                       "通常选择：附表、明细表、数据表等（不是汇总表）",
                Dock = DockStyle.Fill,
                ForeColor = Color.Red,
                Font = new Font(this.Font.FontFamily, 10, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 1. 工作表选择面板
            var sheetPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                Padding = new Padding(0, 10, 0, 0)
            };

            var lblSheet = new Label { Text = "选择工作表:", AutoSize = true, Anchor = AnchorStyles.Left, Margin = new Padding(0, 5, 10, 0) };
            cmbSheets = new ComboBox { Width = 250, DropDownStyle = ComboBoxStyle.DropDownList, Anchor = AnchorStyles.Left };
            cmbSheets.SelectedIndexChanged += CmbSheets_SelectedIndexChanged;

            var lblHint = new Label
            {
                Text = "（已智能选择可能的数据表）",
                AutoSize = true,
                ForeColor = Color.Gray,
                Margin = new Padding(10, 5, 0, 0)
            };

            sheetPanel.Controls.AddRange(new Control[] { lblSheet, cmbSheets, lblHint });

            // 2. 预览区域
            var previewGroup = new GroupBox
            {
                Text = "数据预览 (前10行，前5列)",
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };

            txtPreview = new TextBox
            {
                Multiline = true,
                ReadOnly = true,
                ScrollBars = ScrollBars.Both,
                Font = new Font("Consolas", 9),
                Dock = DockStyle.Fill
            };

            previewGroup.Controls.Add(txtPreview);

            // 3. 按钮面板
            var buttonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.RightToLeft,
                Padding = new Padding(0, 10, 0, 0)
            };

            btnCancel = new Button { Text = "取消", Width = 100, Height = 35, Margin = new Padding(0, 0, 10, 0) };
            btnOK = new Button { Text = "确认选择此工作表", Width = 150, Height = 35, BackColor = Color.LightGreen };

            btnOK.Click += (s, e) =>
            {
                if (cmbSheets.SelectedItem != null)
                {
                    string columnLetter = ColumnNumberToLetter(dataColumn);
                    var confirmResult = MessageBox.Show(
                        $"确认选择工作表：{cmbSheets.SelectedItem}\n\n" +
                        "请确认：\n" +
                        "• 此工作表包含您要查找的数据\n" +
                        $"• {columnLetter}列包含您需要的结果数据\n\n" +
                        "确认选择此工作表？",
                        "确认工作表选择",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question
                    );

                    if (confirmResult == DialogResult.Yes)
                    {
                        SelectedSheetName = cmbSheets.SelectedItem.ToString();
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                }
                else
                {
                    MessageBox.Show("请先选择一个工作表！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            };

            btnCancel.Click += (s, e) => { this.DialogResult = DialogResult.Cancel; this.Close(); };

            buttonPanel.Controls.AddRange(new Control[] { btnCancel, btnOK });

            // 添加到主面板
            mainPanel.Controls.Add(lblInstruction, 0, 0);
            mainPanel.Controls.Add(sheetPanel, 0, 1);
            mainPanel.Controls.Add(previewGroup, 0, 2);
            mainPanel.Controls.Add(buttonPanel, 0, 3);

            this.Controls.Add(mainPanel);
        }

        private void LoadSheets()
        {
            Excel.Application excelApp = null;
            Excel.Workbook workbook = null;
            bool shouldCloseWorkbook = false;

            try
            {
                excelApp = Globals.ThisAddIn.Application;

                // 简化文件打开逻辑，直接打开文件
                workbook = excelApp.Workbooks.Open(
                    Filename: filePath,
                    UpdateLinks: false,
                    ReadOnly: true,
                    AddToMru: false
                );
                shouldCloseWorkbook = true;

                cmbSheets.Items.Clear();
                int defaultIndex = 0;
                int currentIndex = 0;

                foreach (Excel.Worksheet sheet in workbook.Sheets)
                {
                    cmbSheets.Items.Add(sheet.Name);

                    // 智能选择默认工作表：优先选择包含"附表"、"明细"、"数据"等关键词的工作表
                    string sheetName = sheet.Name.ToLower();
                    if (sheetName.Contains("附表") || sheetName.Contains("明细") ||
                        sheetName.Contains("数据") || sheetName.Contains("detail") ||
                        sheetName.Contains("附件") || sheetName.Contains("清单"))
                    {
                        defaultIndex = currentIndex;
                    }
                    currentIndex++;
                }

                if (cmbSheets.Items.Count > 0)
                    cmbSheets.SelectedIndex = defaultIndex;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载工作表失败: {ex.Message}\n\n请确保:\n1. 文件存在且未损坏\n2. 文件未被其他程序占用\n3. 有足够的权限访问文件", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                if (workbook != null && shouldCloseWorkbook)
                {
                    try
                    {
                        workbook.Close(false);
                    }
                    catch { }
                }
            }
        }



        private void CmbSheets_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbSheets.SelectedItem == null) return;

            Excel.Application excelApp = null;
            Excel.Workbook workbook = null;

            try
            {
                excelApp = Globals.ThisAddIn.Application;

                // 简化文件打开逻辑
                workbook = excelApp.Workbooks.Open(
                    Filename: filePath,
                    UpdateLinks: false,
                    ReadOnly: true,
                    AddToMru: false
                );

                Excel.Worksheet sheet = workbook.Sheets[cmbSheets.SelectedItem.ToString()];

                StringBuilder preview = new StringBuilder();
                preview.AppendLine($"工作表: {sheet.Name}");

                Excel.Range usedRange = sheet.UsedRange;
                if (usedRange == null)
                {
                    preview.AppendLine("工作表中没有数据");
                    DataRowCount = 0;
                    txtPreview.Text = preview.ToString();
                    return;
                }

                DataRowCount = usedRange.Rows.Count;
                int actualMaxCol = usedRange.Columns.Count;

                preview.AppendLine($"数据预览 (前10行，实际范围: {DataRowCount}行 x {actualMaxCol}列):");
                preview.AppendLine("行\\列\tA\tB\tC\tD\tE\tF");
                preview.AppendLine("------------------------------------------------");

                int maxRows = Math.Min(10, DataRowCount);
                int maxCols = Math.Min(6, actualMaxCol); // 显示到F列

                for (int row = 1; row <= maxRows; row++)
                {
                    StringBuilder rowData = new StringBuilder($"{row}\t");
                    for (int col = 1; col <= maxCols; col++)
                    {
                        try
                        {
                            Excel.Range cell = (Excel.Range)sheet.Cells[row, col];
                            // 使用 Value2 获取实际值，避免列太窄时显示 ####
                            object cellValueObj = cell.Value2;
                            string cellValue = Ribbon1.FormatCellValue(cellValueObj, cell); // 传入源单元格
                            if (cellValue.Length > 8) cellValue = cellValue.Substring(0, 8) + "...";
                            rowData.Append(cellValue + "\t");
                        }
                        catch
                        {
                            rowData.Append("[错误]\t");
                        }
                    }
                    preview.AppendLine(rowData.ToString());
                }

                // 特别检查指定数据列
                string columnLetter = ColumnNumberToLetter(dataColumn);
                preview.AppendLine($"\n=== {columnLetter}列数据检查 ===");
                if (actualMaxCol >= dataColumn)
                {
                    int dataColumnCount = 0;
                    for (int row = 1; row <= Math.Min(20, DataRowCount); row++)
                    {
                        try
                        {
                            Excel.Range dataCell = (Excel.Range)sheet.Cells[row, dataColumn];
                            // 使用 Value2 获取实际值，避免列太窄时显示 ####
                            object dataValueObj = dataCell.Value2;
                            string dataValue = Ribbon1.FormatCellValue(dataValueObj, dataCell); // 传入源单元格

                            if (!string.IsNullOrEmpty(dataValue))
                            {
                                dataColumnCount++;
                                if (dataColumnCount <= 3) // 显示前3个数据列示例
                                {
                                    preview.AppendLine($"第{row}行{columnLetter}列: {dataValue}");
                                }
                            }
                        }
                        catch { }
                    }
                    preview.AppendLine($"{columnLetter}列有数据的行数: {dataColumnCount} (前20行中)");
                }
                else
                {
                    preview.AppendLine($"警告：此工作表没有{columnLetter}列数据！");
                }

                preview.AppendLine($"\n总行数: {DataRowCount}，总列数: {actualMaxCol}");
                txtPreview.Text = preview.ToString();
            }
            catch (Exception ex)
            {
                txtPreview.Text = $"预览失败: {ex.Message}\n\n请确保:\n1. 文件未被其他程序占用\n2. 文件格式正确\n3. 工作表存在";
                DataRowCount = 0;
            }
            finally
            {
                if (workbook != null)
                {
                    try
                    {
                        workbook.Close(false);
                    }
                    catch { }
                }
            }
        }
    }

    /// <summary>
    /// 西安汇总配置类
    /// </summary>
    public class XianSummaryConfig
    {
        public string TemplateFilePath { get; set; }
        public List<string> SourceFilePaths { get; set; }
        public List<XianSummaryIndexConfig> IndexConfigs { get; set; }

        public XianSummaryConfig()
        {
            SourceFilePaths = new List<string>();
            IndexConfigs = new List<XianSummaryIndexConfig>();
        }
    }

    /// <summary>
    /// 西安汇总索引配置类
    /// </summary>
    public class XianSummaryIndexConfig
    {
        public string TargetSheetName { get; set; }   // 目标附表名称（模板中的附表）
        public string SourceSheetName { get; set; }   // 源附表名称（单体表中的附表）
        public string SheetName { get; set; }         // 附表名称（兼容性属性）
        public List<string> Keywords { get; set; }    // 关键词列表（兼容性属性）
        public int IndexRow { get; set; }             // 索引行号（兼容性保留）
        public List<string> FieldNames { get; set; }  // 从索引行识别的字段名称
        public List<IndexFieldInfo> FieldInfos { get; set; }  // 详细字段信息（包含位置和层级）
        public int InsertRow { get; set; }            // 插入行号
        public int InsertColumn { get; set; }         // 插入列号

        // 新增：抓取范围设置
        public int StartRow { get; set; }             // 抓取起始行
        public int StartColumn { get; set; }          // 抓取起始列
        public int EndRow { get; set; }               // 抓取结束行
        public int EndColumn { get; set; }            // 抓取结束列

        // 新增：索引行范围设置
        public int IndexStartRow { get; set; }        // 索引起始行
        public int IndexStartColumn { get; set; }     // 索引起始列
        public int IndexEndRow { get; set; }          // 索引结束行
        public int IndexEndColumn { get; set; }       // 索引结束列

        public XianSummaryIndexConfig()
        {
            FieldNames = new List<string>();
            FieldInfos = new List<IndexFieldInfo>();
            Keywords = new List<string>();
            TargetSheetName = "";
            SourceSheetName = "";
            SheetName = "";
            IndexRow = 1;
            StartRow = 1;
            StartColumn = 1;
            EndRow = 100;
            EndColumn = 20;
            IndexStartRow = 1;
            IndexStartColumn = 1;
            IndexEndRow = 2;
            IndexEndColumn = 10;
        }
    }

    /// <summary>
    /// 索引字段详细信息类
    /// 用于存储字段的位置、层级和属性信息
    /// </summary>
    public class IndexFieldInfo
    {
        public string FieldName { get; set; }           // 字段名称
        public int Column { get; set; }                 // 字段所在列
        public int Row { get; set; }                    // 字段所在行
        public List<string> Hierarchy { get; set; }    // 字段的层级结构（从上到下）
        public string UniqueKey { get; set; }          // 唯一标识符

        public IndexFieldInfo()
        {
            Hierarchy = new List<string>();
        }

        public override string ToString()
        {
            return $"{FieldName}(列{Column},行{Row})";
        }
    }

    /// <summary>
    /// 固定汇总配置窗体
    /// </summary>
    public partial class FixedSummaryConfigForm : Form
    {
        private FixedSummaryConfig config;
        private Button btnSelectTemplate;
        private Button btnSelectSources;
        private Button btnOK;
        private Button btnCancel;
        private TextBox txtTemplate;
        private ListBox lstSources;
        private Label lblTemplate;
        private Label lblSources;
        private TextBox txtDescription;

        public FixedSummaryConfigForm()
        {
            InitializeComponent();
            config = new FixedSummaryConfig();
        }

        private void InitializeComponent()
        {
            this.Text = "📊 固定汇总配置 - 数据汇总工具";

            // 自动适应屏幕尺寸
            Rectangle screenBounds = Screen.PrimaryScreen.WorkingArea;
            int formWidth = Math.Min(1000, (int)(screenBounds.Width * 0.7));
            int formHeight = Math.Min(700, (int)(screenBounds.Height * 0.8));

            this.Size = new Size(formWidth, formHeight);
            this.MinimumSize = new Size(900, 650);
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MaximizeBox = true;
            this.MinimizeBox = true;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.AutoScroll = true;

            // 创建主布局容器
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 4,
                Padding = new Padding(15),
                BackColor = Color.FromArgb(240, 248, 255)
            };

            // 设置行高比例
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 80));   // 标题
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 200));  // 文件选择
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100));   // 功能说明
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 80));   // 按钮

            // 1. 标题区域
            var titleLabel = new Label
            {
                Text = "📊 固定汇总配置向导",
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Margin = new Padding(0, 0, 0, 10)
            };

            // 2. 文件选择区域
            var filePanel = new GroupBox
            {
                Text = "📁 文件选择",
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 150, 243),
                BackColor = Color.White,
                Padding = new Padding(15),
                Margin = new Padding(0, 0, 0, 10)
            };

            var fileTablePanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 2,
                Padding = new Padding(10)
            };

            // 设置列宽比例
            fileTablePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120));
            fileTablePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));
            fileTablePanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120));

            // 模板文件选择
            lblTemplate = new Label
            {
                Text = "📄 模板文件:",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("微软雅黑", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(76, 175, 80)
            };

            txtTemplate = new TextBox
            {
                Dock = DockStyle.Fill,
                ReadOnly = true,
                BackColor = Color.FromArgb(250, 250, 250),
                Font = new Font("微软雅黑", 9),
                Margin = new Padding(5, 8, 5, 8)
            };

            btnSelectTemplate = new Button
            {
                Text = "🔍 选择模板",
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 9, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                Margin = new Padding(5, 5, 0, 5)
            };
            btnSelectTemplate.FlatAppearance.BorderSize = 0;
            btnSelectTemplate.Click += BtnSelectTemplate_Click;

            // 单体表文件选择
            lblSources = new Label
            {
                Text = "📋 单体表文件:",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.TopLeft,
                Font = new Font("微软雅黑", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(255, 152, 0)
            };

            lstSources = new ListBox
            {
                Dock = DockStyle.Fill,
                SelectionMode = SelectionMode.MultiExtended,
                BackColor = Color.FromArgb(250, 250, 250),
                Font = new Font("微软雅黑", 9),
                Margin = new Padding(5, 8, 5, 8)
            };

            btnSelectSources = new Button
            {
                Text = "📁 选择单体表",
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 9, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                Margin = new Padding(5, 5, 0, 5)
            };
            btnSelectSources.FlatAppearance.BorderSize = 0;
            btnSelectSources.Click += BtnSelectSources_Click;

            // 添加到文件表格布局
            fileTablePanel.Controls.Add(lblTemplate, 0, 0);
            fileTablePanel.Controls.Add(txtTemplate, 1, 0);
            fileTablePanel.Controls.Add(btnSelectTemplate, 2, 0);
            fileTablePanel.Controls.Add(lblSources, 0, 1);
            fileTablePanel.Controls.Add(lstSources, 1, 1);
            fileTablePanel.Controls.Add(btnSelectSources, 2, 1);

            filePanel.Controls.Add(fileTablePanel);

            // 3. 功能说明区域
            var descriptionGroup = new GroupBox
            {
                Text = "📋 功能说明",
                Dock = DockStyle.Fill,
                Font = new Font("微软雅黑", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(156, 39, 176),
                BackColor = Color.White,
                Padding = new Padding(15),
                Margin = new Padding(0, 0, 0, 10)
            };

            txtDescription = new TextBox
            {
                Dock = DockStyle.Fill,
                Multiline = true,
                ReadOnly = true,
                ScrollBars = ScrollBars.Vertical,
                BackColor = Color.FromArgb(250, 250, 250),
                Font = new Font("微软雅黑", 9),
                Text = GetDescriptionText(),
                BorderStyle = BorderStyle.None,
                Margin = new Padding(5)
            };

            descriptionGroup.Controls.Add(txtDescription);

            // 4. 按钮区域
            var buttonPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(240, 248, 255)
            };

            // 创建一个流布局面板来放置按钮
            var buttonFlowPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.RightToLeft,
                Padding = new Padding(0, 15, 0, 0)
            };

            // 取消按钮
            btnCancel = new Button
            {
                Text = "❌ 取消",
                Size = new Size(100, 40),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 11, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                DialogResult = DialogResult.Cancel,
                Margin = new Padding(10, 0, 0, 0)
            };
            btnCancel.FlatAppearance.BorderSize = 0;

            // 开始汇总按钮 - 更大更显眼
            btnOK = new Button
            {
                Text = "🚀 开始汇总",
                Size = new Size(150, 40),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 12, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                DialogResult = DialogResult.OK,
                Margin = new Padding(10, 0, 0, 0)
            };
            btnOK.FlatAppearance.BorderSize = 0;
            btnOK.Click += BtnOK_Click;

            // 添加按钮到流布局
            buttonFlowPanel.Controls.Add(btnCancel);
            buttonFlowPanel.Controls.Add(btnOK);
            buttonPanel.Controls.Add(buttonFlowPanel);

            // 添加所有区域到主布局
            mainPanel.Controls.Add(titleLabel, 0, 0);
            mainPanel.Controls.Add(filePanel, 0, 1);
            mainPanel.Controls.Add(descriptionGroup, 0, 2);
            mainPanel.Controls.Add(buttonPanel, 0, 3);

            // 添加主布局到窗体
            this.Controls.Add(mainPanel);
        }

        private string GetDescriptionText()
        {
            return @"📊 固定汇总功能说明：

本功能将按照以下固定规则从单体表中提取数据并汇总到模板文件中：

🔹 附表1-工程建设概况总表：
   • 提取：单体表第2行，B-N列
   • 插入：模板附表1从第6行开始依次插入，B-N列

🔹 附表2-工程验收及移交资产情况明细表：
   • 提取：单体表第6行，B-H列
   • 插入：模板附表2从第7行开始依次插入，B-H列

🔹 附表3-工程竣工决算审计定案汇总表：
   • 提取：单体表第10-13行，C-O列
   • 插入：模板附表3第8-11行，C-O列（⚠️ 数值相加合并）

🔹 附表4-工程竣工决算审计定案明细表：
   • 提取：单体表第17行，B-O列
   • 插入：模板附表4从第8行开始依次插入，B-O列

🔹 附表5-工程建设规模统计总表：
   • 提取：单体表第22行，B-R列
   • 插入：模板附表5从第9行开始依次插入，B-R列

🔹 附表6-报告信息表：
   • 提取：单体表第25行，B-R列
   • 插入：模板附表6从第3行开始依次插入，A-Q列（注意：源数据B-R列对应目标A-Q列）

⚠️ 重要说明：
• 附表3采用数值相加合并模式（多个单体表的相同位置数值会相加）
• 其他附表采用依次插入模式（每个单体表插入到下一行）
• 处理完成后会生成带时间戳的新文件";
        }

        private void BtnSelectTemplate_Click(object sender, EventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Title = "选择模板文件",
                Filter = "Excel 文件 (*.xlsx;*.xls)|*.xlsx;*.xls",
                Multiselect = false
            };

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                config.TemplateFilePath = openFileDialog.FileName;
                txtTemplate.Text = config.TemplateFilePath;
            }
        }

        private void BtnSelectSources_Click(object sender, EventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Title = "选择单体表文件",
                Filter = "Excel 文件 (*.xlsx;*.xls)|*.xlsx;*.xls",
                Multiselect = true
            };

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                config.SourceFilePaths.Clear();
                lstSources.Items.Clear();

                foreach (string filePath in openFileDialog.FileNames)
                {
                    config.SourceFilePaths.Add(filePath);
                    lstSources.Items.Add(Path.GetFileName(filePath));
                }
            }
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            // 验证配置
            if (string.IsNullOrEmpty(config.TemplateFilePath))
            {
                MessageBox.Show("请选择模板文件！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (config.SourceFilePaths.Count == 0)
            {
                MessageBox.Show("请选择至少一个单体表文件！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 验证文件是否存在
            if (!File.Exists(config.TemplateFilePath))
            {
                MessageBox.Show("模板文件不存在！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            foreach (string filePath in config.SourceFilePaths)
            {
                if (!File.Exists(filePath))
                {
                    MessageBox.Show($"单体表文件不存在：{Path.GetFileName(filePath)}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
            }

            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        /// <summary>
        /// 获取配置信息
        /// </summary>
        /// <returns>固定汇总配置</returns>
        public FixedSummaryConfig GetConfiguration()
        {
            return config;
        }
    }

    /// <summary>
    /// 固定汇总配置类
    /// </summary>
    public class FixedSummaryConfig
    {
        public string TemplateFilePath { get; set; }
        public List<string> SourceFilePaths { get; set; }

        public FixedSummaryConfig()
        {
            SourceFilePaths = new List<string>();
        }
    }

    /// <summary>
    /// 固定汇总映射规则类
    /// </summary>
    public class FixedSummaryRule
    {
        public string TargetSheetName { get; set; }      // 目标附表名称
        public int SourceStartRow { get; set; }          // 源起始行
        public int SourceEndRow { get; set; }            // 源结束行
        public string SourceStartColumn { get; set; }    // 源起始列
        public string SourceEndColumn { get; set; }      // 源结束列
        public int TargetStartRow { get; set; }          // 目标起始行
        public string TargetStartColumn { get; set; }    // 目标起始列
        public bool IsSummaryMode { get; set; }          // 是否为汇总模式（数值相加）
        public string Description { get; set; }          // 规则描述

        /// <summary>
        /// 获取固定汇总的所有规则
        /// </summary>
        /// <returns>规则列表</returns>
        public static List<FixedSummaryRule> GetFixedRules()
        {
            return new List<FixedSummaryRule>
            {
                new FixedSummaryRule
                {
                    TargetSheetName = "附表1-工程建设概况总表",
                    SourceStartRow = 2, SourceEndRow = 2,
                    SourceStartColumn = "B", SourceEndColumn = "N",
                    TargetStartRow = 6, TargetStartColumn = "B",
                    IsSummaryMode = false,
                    Description = "单体表2-2行、B-N列插入到模版附表1从6行开始依次插入列是B-N列"
                },
                new FixedSummaryRule
                {
                    TargetSheetName = "附表2-工程验收及移交资产情况明细表",
                    SourceStartRow = 6, SourceEndRow = 6,
                    SourceStartColumn = "B", SourceEndColumn = "H",
                    TargetStartRow = 7, TargetStartColumn = "B",
                    IsSummaryMode = false,
                    Description = "单体表6-6行、B-H列插入附表2从第7行开始依次插入、B-H列"
                },
                new FixedSummaryRule
                {
                    TargetSheetName = "附表3-工程竣工决算审计定案汇总表",
                    SourceStartRow = 10, SourceEndRow = 13,
                    SourceStartColumn = "C", SourceEndColumn = "O",
                    TargetStartRow = 8, TargetStartColumn = "C",
                    IsSummaryMode = true,
                    Description = "单体表10-13行、C-O列插入到模板附表3的8-11行、C-O列，数值相加合并"
                },
                new FixedSummaryRule
                {
                    TargetSheetName = "附表4-工程竣工决算审计定案明细表",
                    SourceStartRow = 17, SourceEndRow = 17,
                    SourceStartColumn = "B", SourceEndColumn = "O",
                    TargetStartRow = 8, TargetStartColumn = "B",
                    IsSummaryMode = false,
                    Description = "单体表17-17行、B-O列插入到模板附表4从8行开始B-O列"
                },
                new FixedSummaryRule
                {
                    TargetSheetName = "附表5-工程建设规模统计总表",
                    SourceStartRow = 22, SourceEndRow = 22,
                    SourceStartColumn = "B", SourceEndColumn = "R",
                    TargetStartRow = 9, TargetStartColumn = "B",
                    IsSummaryMode = false,
                    Description = "单体表22-22行、B-R列插入模板附表5从9行开始B-R列"
                },
                new FixedSummaryRule
                {
                    TargetSheetName = "附表6-报告信息表",
                    SourceStartRow = 25, SourceEndRow = 25,
                    SourceStartColumn = "B", SourceEndColumn = "R",
                    TargetStartRow = 3, TargetStartColumn = "A",
                    IsSummaryMode = false,
                    Description = "单体表25-25行B-R列插入模版附表6从3行开始A-Q列"
                }
            };
        }

        /// <summary>
        /// 生成Excel报告按钮点击事件
        /// </summary>
        private void buttonExcelReport_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                Excel.Application excelApp = Globals.ThisAddIn.Application;
                Excel.Workbook workbook = excelApp.ActiveWorkbook;
                Excel.Worksheet worksheet = workbook.ActiveSheet;

                if (workbook == null || worksheet == null)
                {
                    MessageBox.Show("请先打开一个Excel工作簿！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 1. 显示索引行选择表单
                using (var indexForm = new ExcelReportIndexForm(worksheet))
                {
                    if (indexForm.ShowDialog() != DialogResult.OK)
                    {
                        return; // 用户取消操作
                    }

                    int indexRow = indexForm.SelectedIndexRow;
                    var fieldNames = indexForm.FieldNames;
                    var dataRows = indexForm.DataRows;

                    // 2. 选择Word模板
                    using (var openFileDialog = new OpenFileDialog
                    {
                        Title = "选择Word模板文件",
                        Filter = "Word文件 (*.doc;*.docx)|*.doc;*.docx",
                        Multiselect = false
                    })
                    {
                        if (openFileDialog.ShowDialog() != DialogResult.OK)
                        {
                            MessageBox.Show("未选择Word模板文件，操作取消！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            return;
                        }

                        string templatePath = openFileDialog.FileName;

                        // 3. 选择保存文件夹
                        using (var folderDialog = new FolderBrowserDialog
                        {
                            Description = "选择报告保存文件夹"
                        })
                        {
                            if (folderDialog.ShowDialog() != DialogResult.OK)
                            {
                                MessageBox.Show("未选择保存位置，操作取消！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                return;
                            }

                            string saveFolderPath = folderDialog.SelectedPath;

                            // 4. 调用核心生成器生成报告
                            ExcelReportGenerator.GenerateReports(worksheet, indexRow, templatePath, saveFolderPath);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成Excel报告时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}