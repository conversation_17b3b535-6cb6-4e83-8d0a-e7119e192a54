using System;
using Word = Microsoft.Office.Interop.Word;

namespace ExcelToWord
{
    /// <summary>
    /// 批注位置修复验证类
    /// 用于测试和验证链接报告中批注位置的准确性
    /// </summary>
    public class CommentPositionFix
    {
        /// <summary>
        /// 验证批注是否在正确位置的辅助方法
        /// </summary>
        /// <param name="doc">Word文档</param>
        /// <param name="expectedText">期望的文本内容</param>
        /// <param name="commentText">批注文本</param>
        /// <returns>验证结果</returns>
        public static string VerifyCommentPosition(Word.Document doc, string expectedText, string commentText)
        {
            try
            {
                foreach (Word.Comment comment in doc.Comments)
                {
                    // 获取批注所在的范围
                    Word.Range commentRange = comment.Scope;
                    string rangeText = commentRange.Text?.Trim() ?? "";
                    
                    if (comment.Range.Text.Contains(commentText))
                    {
                        return $"批注位置正确：'{rangeText}' - {commentText}";
                    }
                }
                return "未找到匹配的批注";
            }
            catch (Exception ex)
            {
                return $"验证失败：{ex.Message}";
            }
        }
        
        /// <summary>
        /// 安全创建批注的方法
        /// </summary>
        /// <param name="range">目标范围</param>
        /// <param name="commentText">批注文本</param>
        /// <returns>是否成功创建</returns>
        public static bool CreateCommentSafely(Word.Range range, string commentText)
        {
            try
            {
                if (range == null || string.IsNullOrEmpty(commentText))
                    return false;
                
                // 确保范围有效
                if (range.Start >= 0 && range.End > range.Start)
                {
                    Word.Comment comment = range.Comments.Add(range, commentText);
                    return comment != null;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"创建批注失败：{ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 获取范围的详细信息（用于调试）
        /// </summary>
        /// <param name="range">Word范围</param>
        /// <returns>范围信息</returns>
        public static string GetRangeInfo(Word.Range range)
        {
            try
            {
                if (range == null) return "Range is null";
                
                return $"Start: {range.Start}, End: {range.End}, Text: '{range.Text?.Trim() ?? ""}', Length: {range.Text?.Length ?? 0}";
            }
            catch (Exception ex)
            {
                return $"获取范围信息失败：{ex.Message}";
            }
        }
    }
    
    /// <summary>
    /// 链接报告批注位置修复的关键改进点说明
    /// </summary>
    public static class CommentFixExplanation
    {
        public const string PROBLEM_DESCRIPTION = @"
        原问题分析：
        1. 使用 wordApp.Selection.Comments.Add() 时，Selection 可能已经不是原来的占位符位置
        2. PasteSpecial 操作后，Selection 范围可能发生变化
        3. 搜索和替换过程中，文档内容变化影响后续 Range 定位
        ";
        
        public const string SOLUTION_DESCRIPTION = @"
        修复方案：
        1. 使用 foundRange.Duplicate 保存原始位置
        2. 记录粘贴前的 targetRange 位置
        3. 使用粘贴后的实际范围 pastedRange 添加批注
        4. 确保批注添加到正确的文本范围上
        5. 改进搜索范围的更新逻辑，避免遗漏或重复
        ";
        
        public const string KEY_IMPROVEMENTS = @"
        关键改进：
        1. Word.Range foundRange = doc.Range(findObject.Parent.Start, findObject.Parent.End)
        2. Word.Range targetRange = wordApp.Selection.Range.Duplicate
        3. Word.Range pastedRange = wordApp.Selection.Range.Duplicate
        4. pastedRange.Comments.Add(pastedRange, commentText)
        5. 正确的搜索范围更新和边界检查
        ";
    }
}
