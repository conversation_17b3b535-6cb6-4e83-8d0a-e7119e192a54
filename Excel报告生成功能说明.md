# Excel报告生成功能说明

## 功能介绍
"生成Excel报告"是一个全新的自动化功能，可以根据Excel中的数据批量生成Word报告。

## 主要特点
1. **智能索引行选择**：通过可视化界面选择字段名所在的行
2. **自动数据检测**：自动检测从索引行下一行开始的所有数据行，直到遇到空行
3. **字段智能匹配**：自动匹配Excel字段名与Word模板中的【】占位符
4. **批量生成**：一次操作为每行数据生成一个独立的Word报告
5. **错误提示**：未匹配的占位符会被高亮显示

## 使用步骤

### 第一步：准备Excel数据
确保您的Excel数据格式如下：
```
行1: （可选的标题行）
行2: 姓名    性别    年龄    部门    （索引行/字段名行）
行3: 张三    男      25      技术部  （数据行1）
行4: 李四    女      28      销售部  （数据行2）
行5: 王五    男      30      财务部  （数据行3）
行6: （空行，程序会自动停止检测）
```

### 第二步：准备Word模板
创建Word模板文件，使用【】格式的占位符：
```
员工信息报告

基本信息：
姓名：【姓名】
性别：【性别】
年龄：【年龄】
部门：【部门】

其他信息：
入职时间：【入职时间】  （如果Excel中没有此字段，会被高亮显示）
```

### 第三步：执行生成
1. 在Excel中打开包含数据的工作簿
2. 点击"中联五洲"选项卡中的"生成Excel报告"按钮
3. 在弹出的选择窗口中：
   - 查看数据预览
   - 点击选择索引行（如示例中的第2行）
   - 确认字段数量和数据行数量
   - 点击"确定"
4. 选择Word模板文件
5. 选择报告保存文件夹
6. 等待生成完成

## 生成结果
- 每行数据会生成一个独立的Word文件
- 文件名格式：`Excel报告_张三_第3行_20250811_143022.docx`
- 所有匹配的占位符会被替换为对应的数据
- 未匹配的占位符会保持原样并被高亮显示

## 技术实现
- **核心类**：`ExcelReportGenerator` - 负责主要的生成逻辑
- **界面类**：`ExcelReportIndexForm` - 提供用户友好的选择界面
- **按钮位置**：位于"生成报告"组中，与其他报告功能并列

## 错误处理
- 自动检测并提示无效的索引行
- 自动检测并提示没有数据行的情况
- 对生成失败的单个报告进行错误提示
- 提供详细的匹配信息供用户确认

## 与现有功能的区别
- **生成报告(横排)**：需要手动指定数据行，一次生成一个报告
- **生成报告(竖排)**：处理竖向排列的数据
- **生成Excel报告**：自动检测所有数据行，批量生成多个报告

这个功能特别适合处理员工信息、客户资料、产品清单等需要批量生成个人报告的场景。
