﻿
using System;
using System.Diagnostics;
using System.Net.Http;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows.Forms;
using Newtonsoft.Json;

namespace ExcelToWord
{
    public class UpdateChecker
    {
        private const string UpdateUrl = "http://120.211.204.196:8085/excel/updates/update.json";

        public class UpdateInfo
        {
            public string version { get; set; }
            public string downloadUrl { get; set; }
            public string releaseNotes { get; set; }
        }

        public static async Task CheckForUpdateAsync(bool isManual = false)
        {
            try
            {
                using (HttpClient client = new HttpClient())
                {
                    string json = await client.GetStringAsync(UpdateUrl);
                    UpdateInfo info = JsonConvert.DeserializeObject<UpdateInfo>(json);

                    Version remoteVersion = new Version(info.version.Trim());
                    Version currentVersion = new Version("1.8.0.8"); // 请同步更新这里

                    if (remoteVersion.CompareTo(currentVersion) > 0)
                    {
                        DialogResult result = MessageBox.Show(
                            $"发现新版本 {info.version}，是否立即下载更新？\n\n更新内容：\n{info.releaseNotes}",
                            "更新提示",
                            MessageBoxButtons.YesNo,
                            MessageBoxIcon.Information);

                        if (result == DialogResult.Yes)
                        {
                            // 启动安装程序
                            Process.Start(info.downloadUrl);

                            // 弹出提示说明必须重启 Excel
                            MessageBox.Show(
                                "安装最新版前请关闭Excel，否则可能安装失败！",
                                "更新说明",
                                MessageBoxButtons.OK,
                                MessageBoxIcon.Information);
                        }
                    }
                    else if (isManual)
                    {
                        // 只有“手动检查更新”才提示这个
                        MessageBox.Show("当前已是最新版本。", "更新提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                if (isManual)
                {
                    MessageBox.Show("检查更新失败，请稍后重试。\n错误：" + ex.Message, "网络错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
                else
                {
                    Console.WriteLine("更新检查失败: " + ex.Message);
                }
            }
        }


    }

}
