using System;
using Word = Microsoft.Office.Interop.Word;

namespace ExcelToWord
{
    /// <summary>
    /// 批注位置精确度修复验证类
    /// 解决批注偏移到相邻文字的问题
    /// </summary>
    public class CommentPositionAccuracyFix
    {
        /// <summary>
        /// 测试批注位置是否准确
        /// </summary>
        /// <param name="doc">Word文档</param>
        /// <param name="expectedText">期望批注所在的文本</param>
        /// <returns>验证结果</returns>
        public static string VerifyCommentAccuracy(Word.Document doc, string expectedText)
        {
            try
            {
                int correctComments = 0;
                int incorrectComments = 0;
                
                foreach (Word.Comment comment in doc.Comments)
                {
                    // 获取批注所在的范围文本
                    string commentRangeText = comment.Scope.Text?.Trim() ?? "";
                    
                    if (commentRangeText.Contains(expectedText))
                    {
                        correctComments++;
                    }
                    else
                    {
                        incorrectComments++;
                        System.Diagnostics.Debug.WriteLine($"批注位置错误：期望'{expectedText}'，实际在'{commentRangeText}'");
                    }
                }
                
                return $"批注位置验证：正确 {correctComments} 个，错误 {incorrectComments} 个";
            }
            catch (Exception ex)
            {
                return $"验证失败：{ex.Message}";
            }
        }
        
        /// <summary>
        /// 安全添加批注的改进方法
        /// </summary>
        /// <param name="wordApp">Word应用程序</param>
        /// <param name="targetRange">目标范围</param>
        /// <param name="commentText">批注文本</param>
        /// <returns>是否成功添加</returns>
        public static bool AddCommentSafely(Word.Application wordApp, Word.Range targetRange, string commentText)
        {
            try
            {
                if (targetRange == null || string.IsNullOrEmpty(commentText))
                    return false;
                
                // 方法1：先选择范围，再添加批注
                targetRange.Select();
                
                // 确保Selection正确
                if (wordApp.Selection.Range.Text == targetRange.Text)
                {
                    Word.Comment comment = wordApp.Selection.Comments.Add(wordApp.Selection.Range, commentText);
                    return comment != null;
                }
                
                // 方法2：直接在范围上添加批注（备用）
                Word.Comment backupComment = targetRange.Comments.Add(targetRange, commentText);
                return backupComment != null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加批注失败：{ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 获取PasteSpecial后的准确范围
        /// </summary>
        /// <param name="wordApp">Word应用程序</param>
        /// <param name="originalStart">原始起始位置</param>
        /// <returns>粘贴后的实际范围</returns>
        public static Word.Range GetAccuratePastedRange(Word.Application wordApp, int originalStart)
        {
            try
            {
                // 获取当前Selection范围
                Word.Range currentSelection = wordApp.Selection.Range.Duplicate;
                
                // 验证范围的合理性
                if (currentSelection.Start >= originalStart && 
                    currentSelection.End > currentSelection.Start &&
                    !string.IsNullOrEmpty(currentSelection.Text))
                {
                    return currentSelection;
                }
                
                // 如果Selection范围异常，尝试其他方法
                return wordApp.Selection.Range.Duplicate;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取粘贴范围失败：{ex.Message}");
                return wordApp.Selection.Range.Duplicate;
            }
        }
    }
    
    /// <summary>
    /// 批注位置修复的关键改进说明
    /// </summary>
    public static class CommentPositionFixExplanation
    {
        public const string PROBLEM_DESCRIPTION = @"
        问题描述：
        文档内容：【国网四平供电公司】提供
        期望结果：批注应该在【国网四平供电公司】（链接字段）上
        实际结果：批注却出现在'提供'这个词上
        
        根本原因：
        PasteSpecial操作后，wordApp.Selection的范围可能发生偏移，
        导致批注被添加到错误的位置。
        ";
        
        public const string SOLUTION_DESCRIPTION = @"
        解决方案：
        1. 在PasteSpecial后立即获取Selection.Range.Duplicate
        2. 先移动光标到安全位置：Selection.Collapse(wdCollapseEnd)
        3. 重新选择粘贴的内容范围：actualPastedRange.Select()
        4. 在选中的范围添加批注：Selection.Comments.Add()
        5. 提供备用批注添加方案，确保批注能够成功添加
        ";
        
        public const string KEY_IMPROVEMENTS = @"
        关键改进：
        1. 使用actualPastedRange.Select()重新选择正确范围
        2. 添加wordApp.Selection.Collapse()避免范围偏移
        3. 双重批注添加策略：主方案+备用方案
        4. 详细的调试日志，便于问题追踪
        5. 异常处理确保程序稳定性
        ";
    }
}
